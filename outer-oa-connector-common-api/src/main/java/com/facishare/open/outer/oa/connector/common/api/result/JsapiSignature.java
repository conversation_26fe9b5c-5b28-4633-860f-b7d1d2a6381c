package com.facishare.open.outer.oa.connector.common.api.result;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/1/3 15:14:59
 */
@Data
public class JsapiSignature {
    /**
     * 生成签名的随机串
     */
    private String nonceStr;
    /**
     * 生成签名的时间戳
     */
    private Long timestamp;
    /**
     * 签名
     */
    private String signature;
    /**
     * 当前网页的URL，不包含#及其后面部分(url必须是调用JS接口页面的完整URL)
     */
    private String url;
}
