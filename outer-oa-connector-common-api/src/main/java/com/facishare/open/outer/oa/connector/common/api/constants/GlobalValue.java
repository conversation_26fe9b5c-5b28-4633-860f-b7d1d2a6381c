package com.facishare.open.outer.oa.connector.common.api.constants;

/**
 * 全局变量
 * <AUTHOR>
 * @date 20220722
 */
// IgnoreI18nFile
public interface GlobalValue {
    //纷享系统管理员用户ID
    Integer FS_ADMIN_USER_ID = 1000;
    //纷享系统用户ID
    Integer FS_SYSTEM_USER_ID = -10000;
    //允许试用天数
    Integer ALLOW_TRIAL_DAYS = 15;
    //一月默认天数
    Integer DAYS_OF_ONE_MONTH = 30;
    //一年默认天数
    Integer DAYS_OF_ONE_YEAR = 365;
    //一天毫秒数
    Long TOTAL_MS_IN_ONE_DAY = 24 * 60 * 60 * 1000L;
    //用户ticket key前缀
    String USER_TICKET_KEY_PREFIX="user_ticket_key_";
    //用户ticket有效期为10分钟
    Long USER_TICKET_EXPIRE_TIME = 10 * 60L;
    //机器人回复语模板
    String BOT_REPLY_MESSAGE_MODEL = "{\n" +
            "  \"config\": {\n" +
            "    \"wide_screen_mode\": true\n" +
            "  },\n" +
            "  \"i18n_elements\": {\n" +
            "    \"zh_cn\": [\n" +
            "      {\n" +
            "        \"tag\": \"div\",\n" +
            "        \"text\": {\n" +
            "          \"content\": \"您好，欢迎使用ShareCRM，如有任何疑问，您可以点击下方按钮咨询人工客服，我们安排专人为您解答\uD83D\uDC47\uD83D\uDC47\",\n" +
            "          \"tag\": \"lark_md\"\n" +
            "        }\n" +
            "      },\n" +
            "      {\n" +
            "        \"actions\": [\n" +
            "          {\n" +
            "            \"tag\": \"button\",\n" +
            "            \"text\": {\n" +
            "              \"content\": \"联系客服\",\n" +
            "              \"tag\": \"plain_text\"\n" +
            "            },\n" +
            "            \"type\": \"primary\",\n" +
            "            \"url\": \"https://applink.feishu.cn/client/helpdesk/open?id=7156150918839484417&extra=%7B%22channel%22%3A1%2C%22created_at%22%3A1667186531%7D\"\n" +
            "          },\n" +
            "          {\n" +
            "            \"tag\": \"button\",\n" +
            "            \"text\": {\n" +
            "              \"content\": \"进入应用\",\n" +
            "              \"tag\": \"plain_text\"\n" +
            "            },\n" +
            "            \"type\": \"primary\",\n" +
            "            \"url\": \"{crm_app_url}\"\n" +
            "          }\n" +
            "        ],\n" +
            "        \"tag\": \"action\"\n" +
            "      }\n" +
            "    ]\n" +
            "  }\n" +
            "}";
    //欢迎语模板
    String WELCOME_MESSAGE_MODEL = "{\n" +
            "  \"config\": {\n" +
            "    \"wide_screen_mode\": true\n" +
            "  },\n" +
            "  \"header\": {\n" +
            "    \"title\": {\n" +
            "      \"content\": \" \uD83C\uDF89 欢迎使用ShareCRM\",\n" +
            "      \"tag\": \"plain_text\"\n" +
            "    }\n" +
            "  },\n" +
            "  \"i18n_elements\": {\n" +
            "    \"zh_cn\": [\n" +
            "      {\n" +
            "        \"tag\": \"div\",\n" +
            "        \"text\": {\n" +
            "          \"content\": \"您已成功安装ShareCRM~\\nShareCRM以连接型 CRM 为特色，连接业务，连接人，连接系统，实现以客户为中心，企业内部和上下游业务的高效协作。\\n\uD83C\uDF08  **连接员工，提升沟通协作效率**\\n  ⦿ 互联客群\\n  ⦿ OA 与 CRM 互通\\n  ⦿ 社交化工作圈\\n\uD83C\uDF08  **连接客户，营销服务高效触达**\\n  ⦿ 多渠道的客户服务接入\\n  ⦿ 无缝对接 ERP 系统\\n\uD83C\uDF08  **连接上下游，重构增长模式**\\n  ⦿ 渠道伙伴自主在线订货\\n  ⦿ 营销活动与费用在线核销\\n  ⦿ 上下游深度协作\\n\uD83C\uDF08  **连接生态与系统，灵活实现与ERP、电子签章、企业自建等系统的数据打通**\\n  ⦿ 灵活配置\\n  ⦿ 稳定运行\\n  ⦿ OpenAPI\\n\\n**\uD83D\uDCCD我们提供的 CRM 行业解决方案**\\nICT 行业 | SAAS 软件  | 工业自动化 |  快消品 | 医疗健康  | 家居建材 | 教育培训  | 农牧行业\\n连接型 CRM 通过深入行业场景，助力细分行业数字化转型，在三大行业及其下包括54个细分类目中打磨服务于行业典型场景的专属 CRM 解决方案。\\n了解产品：[新手快速入门](https://help.fxiaoke.com/2615)\\n公司官网：[https://www.fxiaoke.com/](https://www.fxiaoke.com/)\\n客服电话：400-1357-578\\n\\n\uD83D\uDC47\uD83D\uDC47联系客户经理，免费预约产品演示\uD83D\uDC47\uD83D\uDC47\",\n" +
            "          \"tag\": \"lark_md\"\n" +
            "        }\n" +
            "      },\n" +
            "      {\n" +
            "        \"actions\": [\n" +
            "          {\n" +
            "            \"tag\": \"button\",\n" +
            "            \"text\": {\n" +
            "              \"content\": \"联系客服\",\n" +
            "              \"tag\": \"plain_text\"\n" +
            "            },\n" +
            "            \"type\": \"primary\",\n" +
            "            \"url\": \"https://applink.feishu.cn/client/helpdesk/open?id=7156150918839484417&extra=%7B%22channel%22%3A1%2C%22created_at%22%3A1667186531%7D\"\n" +
            "          },\n" +
            "          {\n" +
            "            \"tag\": \"button\",\n" +
            "            \"text\": {\n" +
            "              \"content\": \"进入应用\",\n" +
            "              \"tag\": \"plain_text\"\n" +
            "            },\n" +
            "            \"type\": \"primary\",\n" +
            "            \"url\": \"{crm_app_url}\"\n" +
            "          }\n" +
            "        ],\n" +
            "        \"tag\": \"action\"\n" +
            "      }\n" +
            "    ]\n" +
            "  }\n" +
            "}";

    String MENTION_KEY_ALL = "@_all";

    //机器人回复语模板(英文版)
    String BOT_REPLY_MESSAGE_MODEL_EN = "{\n" +
            "  \"config\": {\n" +
            "    \"wide_screen_mode\": true\n" +
            "  },\n" +
            "  \"i18n_elements\": {\n" +
            "    \"en_us\": [\n" +
            "      {\n" +
            "        \"tag\": \"div\",\n" +
            "        \"text\": {\n" +
            "          \"content\": \"Hello, welcome to ShareCRM. If you have any questions, you can click the button below to consult our customer service, we will arrange professionals to answer for you\uD83D\uDC47\uD83D\uDC47\",\n" +
            "          \"tag\": \"lark_md\"\n" +
            "        }\n" +
            "      },\n" +
            "      {\n" +
            "        \"actions\": [\n" +
            "          {\n" +
            "            \"tag\": \"button\",\n" +
            "            \"text\": {\n" +
            "              \"content\": \"Contact Support\",\n" +
            "              \"tag\": \"plain_text\"\n" +
            "            },\n" +
            "            \"type\": \"primary\",\n" +
            "            \"url\": \"https://applink.feishu.cn/client/helpdesk/open?id=7156150918839484417&extra=%7B%22channel%22%3A1%2C%22created_at%22%3A1667186531%7D\"\n" +
            "          },\n" +
            "          {\n" +
            "            \"tag\": \"button\",\n" +
            "            \"text\": {\n" +
            "              \"content\": \"Enter App\",\n" +
            "              \"tag\": \"plain_text\"\n" +
            "            },\n" +
            "            \"type\": \"primary\",\n" +
            "            \"url\": \"{crm_app_url}\"\n" +
            "          }\n" +
            "        ],\n" +
            "        \"tag\": \"action\"\n" +
            "      }\n" +
            "    ]\n" +
            "  }\n" +
            "}";
    
    //欢迎语模板(英文版)
    String WELCOME_MESSAGE_MODEL_EN = "{\n" +
            "  \"config\": {\n" +
            "    \"wide_screen_mode\": true\n" +
            "  },\n" +
            "  \"header\": {\n" +
            "    \"title\": {\n" +
            "      \"content\": \" \uD83C\uDF89 Welcome to ShareCRM\",\n" +
            "      \"tag\": \"plain_text\"\n" +
            "    }\n" +
            "  },\n" +
            "  \"i18n_elements\": {\n" +
            "    \"en_us\": [\n" +
            "      {\n" +
            "        \"tag\": \"div\",\n" +
            "        \"text\": {\n" +
            "          \"content\": \"You have successfully installed ShareCRM~\\nShareCRM features connection-based CRM, connecting business, people, and systems to achieve customer-centric, efficient collaboration within enterprises and between upstream and downstream businesses.\\n\uD83C\uDF08  **Connect employees, improve communication and collaboration efficiency**\\n  ⦿ Connected customer groups\\n  ⦿ Integration between OA and CRM\\n  ⦿ Social work circles\\n\uD83C\uDF08  **Connect customers, efficient marketing service reach**\\n  ⦿ Multi-channel customer service access\\n  ⦿ Seamless connection to ERP systems\\n\uD83C\uDF08  **Connect upstream and downstream, restructure growth model**\\n  ⦿ Channel partners self-service online ordering\\n  ⦿ Online verification of marketing activities and expenses\\n  ⦿ Deep collaboration between upstream and downstream\\n\uD83C\uDF08  **Connect ecosystem and systems, flexibly achieve data integration with ERP, electronic signature, enterprise self-built and other systems**\\n  ⦿ Flexible configuration\\n  ⦿ Stable operation\\n  ⦿ OpenAPI\\n\\n**\uD83D\uDCCD Our CRM industry solutions**\\nICT Industry | SAAS Software | Industrial Automation | FMCG | Healthcare | Home Building Materials | Education & Training | Agriculture & Livestock\\nThe connection-based CRM helps industry digital transformation through in-depth industry scenarios, polishing exclusive CRM solutions for typical scenarios in 54 subdivided categories across three major industries.\\nLearn about our product: [Quick Start Guide](https://help.fxiaoke.com/2615)\\nCompany website: [https://www.fxiaoke.com/](https://www.fxiaoke.com/)\\nCustomer service: 400-1357-578\\n\\n\uD83D\uDC47\uD83D\uDC47Contact our customer manager for a free product demo\uD83D\uDC47\uD83D\uDC47\",\n" +
            "          \"tag\": \"lark_md\"\n" +
            "        }\n" +
            "      },\n" +
            "      {\n" +
            "        \"actions\": [\n" +
            "          {\n" +
            "            \"tag\": \"button\",\n" +
            "            \"text\": {\n" +
            "              \"content\": \"Contact Support\",\n" +
            "              \"tag\": \"plain_text\"\n" +
            "            },\n" +
            "            \"type\": \"primary\",\n" +
            "            \"url\": \"https://applink.feishu.cn/client/helpdesk/open?id=7156150918839484417&extra=%7B%22channel%22%3A1%2C%22created_at%22%3A1667186531%7D\"\n" +
            "          },\n" +
            "          {\n" +
            "            \"tag\": \"button\",\n" +
            "            \"text\": {\n" +
            "              \"content\": \"Enter App\",\n" +
            "              \"tag\": \"plain_text\"\n" +
            "            },\n" +
            "            \"type\": \"primary\",\n" +
            "            \"url\": \"{crm_app_url}\"\n" +
            "          }\n" +
            "        ],\n" +
            "        \"tag\": \"action\"\n" +
            "      }\n" +
            "    ]\n" +
            "  }\n" +
            "}";

    //企业绑定扩展字段
    String enterprise_extend = "{\"isFirstLand\":true,\"isRetainInformation\":false}";
}
