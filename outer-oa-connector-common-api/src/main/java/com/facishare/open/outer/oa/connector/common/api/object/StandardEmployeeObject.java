package com.facishare.open.outer.oa.connector.common.api.object;

import com.facishare.open.outer.oa.connector.common.api.annotation.SystemAnnotation;
import com.facishare.open.outer.oa.connector.common.api.enums.CRMEmployeeFiledEnum;
import com.facishare.open.outer.oa.connector.common.api.params.ObjectData;
import com.facishare.open.outer.oa.connector.i18n.I18NStringEnum;
import lombok.Data;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;
import java.util.List;

/**
 * 标准人员
 */
@Data
public class StandardEmployeeObject extends BaseOuterEmployeeObject implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * UserID
     */
    @SystemAnnotation(value = CRMEmployeeFiledEnum.USER_ID,description = "员工userId",i18n = I18NStringEnum.s191)
    private String userid;

    /**
     * 名称
     */
    @SystemAnnotation(value = CRMEmployeeFiledEnum.NAME,description = "名称",i18n = I18NStringEnum.s194)
    private String name;

    /**
     * 手机号
     */
    @SystemAnnotation(value = CRMEmployeeFiledEnum.PHONE,description = "电话",i18n = I18NStringEnum.s186)
    private String phone;

    /**
     * 部门ID
     */
    @SystemAnnotation(value = CRMEmployeeFiledEnum.MAIN_DEPARTMENT,description = "主属部门",i18n = I18NStringEnum.s188)
    private String deptId;

    /**
     * 职员工号
     */
    @SystemAnnotation(value = CRMEmployeeFiledEnum.EMPLOYEE_NUMBER,description = "工号",i18n = I18NStringEnum.s195)
    private String jobNumber;

    /**
     * 邮箱
     */
    @SystemAnnotation(value = CRMEmployeeFiledEnum.EMAIL,description = "邮箱",i18n = I18NStringEnum.s185)
    private String email;

    private String extendValue;

}