package com.facishare.open.outer.oa.connector.common.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 连接器认证类型枚举
 */
@Getter
@AllArgsConstructor
public enum AuthTypeEnum {


    OAUTH2("OAUTH2", "OAuth2.0认证");

    /**
     * 认证类型编码
     */
    private final String code;

    /**
     * 认证类型描述
     */
    private final String desc;

    /**
     * 根据code获取枚举
     *
     * @param code 认证类型编码
     * @return 对应的枚举值，如果不存在返回null
     */
    public static AuthTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (AuthTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}