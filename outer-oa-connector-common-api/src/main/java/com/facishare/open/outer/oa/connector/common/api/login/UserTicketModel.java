package com.facishare.open.outer.oa.connector.common.api.login;

import com.facishare.webhook.common.util.MD5Util;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserTicketModel implements Serializable {
    private String corpId;
    private String appId;
//    private String userId;
    private String fsUserId;
    private String outerUserId;
    private long timestamp;
    private String fsEa;

    public static String convertMd5String(UserTicketModel userTicketModel){
        return MD5Util.getMD5(userTicketModel.getCorpId()+userTicketModel.getAppId()+userTicketModel.getFsUserId()+userTicketModel.getOuterUserId()+userTicketModel.getTimestamp()+userTicketModel.getFsEa());

    }

}
