package com.facishare.open.outer.oa.connector.common.api.params;

import com.facishare.open.outer.oa.connector.common.api.annotation.SecurityField;
import com.facishare.open.outer.oa.connector.common.api.info.BaseOuterOaAppInfoParams;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/2/26 10:15:45
 *
 * 自建应用: agentId/appSecret/redirectAppId/redirectAppSecret/token/clientIp
 * isv应用: agentId/suiteId/authInfo
 */
@Data
public class DingtalkAppInfoParams extends BaseOuterOaAppInfoParams {
    private String agentId;

    @SecurityField
    private String appSecret;
    private String redirectAppId;
    @SecurityField
    private String redirectAppSecret;
    @SecurityField
    private String token;
    private String clientIp;

    private Long suiteId;
    private String authInfo;
}
