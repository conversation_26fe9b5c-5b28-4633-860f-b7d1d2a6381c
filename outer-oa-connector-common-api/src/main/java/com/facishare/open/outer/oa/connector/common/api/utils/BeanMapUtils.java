package com.facishare.open.outer.oa.connector.common.api.utils;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.beanutils.PropertyUtilsBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Java对象与Map互相转换的工具类
 * 提供了多种转换方式，适用于不同场景
 */
public class BeanMapUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(BeanMapUtils.class);
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    
    /**
     * 使用Jackson库将Java对象转换为Map
     * 优点：速度快，支持各种复杂类型
     * 缺点：依赖Jackson库，不支持自定义转换逻辑
     *
     * @param obj 待转换的对象
     * @return 转换后的Map
     */
    public static Map<String, Object> objectToMapByJackson(Object obj) {
        if (obj == null) {
            return new HashMap<>();
        }
        try {
            return OBJECT_MAPPER.convertValue(obj, Map.class);
        } catch (Exception e) {
            logger.error("使用Jackson转换对象到Map失败", e);
            return new HashMap<>();
        }
    }
    
    /**
     * 使用Apache Commons BeanUtils将Java对象转换为Map
     * 优点：处理Java Bean属性较好，性能适中
     * 缺点：依赖额外的库
     *
     * @param obj 待转换的对象
     * @return 转换后的Map
     */
    public static Map<String, Object> objectToMapByBeanUtils(Object obj) {
        if (obj == null) {
            return new HashMap<>();
        }
        
        Map<String, Object> map = new HashMap<>();
        try {
            PropertyUtilsBean propertyUtilsBean = new PropertyUtilsBean();
            PropertyDescriptor[] descriptors = propertyUtilsBean.getPropertyDescriptors(obj);
            for (PropertyDescriptor descriptor : descriptors) {
                String name = descriptor.getName();
                if (!"class".equals(name)) {
                    Method readMethod = descriptor.getReadMethod();
                    if (readMethod != null) {
                        Object value = readMethod.invoke(obj);
                        map.put(name, value);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("使用BeanUtils转换对象到Map失败", e);
        }
        return map;
    }
    
    /**
     * 使用反射将Java对象转换为Map
     * 优点：不依赖外部库，支持自定义转换逻辑
     * 缺点：性能相对较低
     *
     * @param obj 待转换的对象
     * @return 转换后的Map
     */
    public static Map<String, Object> objectToMapByReflection(Object obj) {
        if (obj == null) {
            return new HashMap<>();
        }
        
        Map<String, Object> map = new HashMap<>();
        
        try {
            Class<?> clazz = obj.getClass();
            // 获取包括父类在内的所有字段
            Field[] fields = getAllFields(clazz);
            
            for (Field field : fields) {
                // 跳过静态字段、被JsonIgnore注解的字段
                if (Modifier.isStatic(field.getModifiers()) || 
                    field.isAnnotationPresent(JsonIgnore.class)) {
                    continue;
                }
                
                field.setAccessible(true);
                String fieldName = field.getName();
                Object fieldValue = field.get(obj);
                
                map.put(fieldName, fieldValue);
            }
        } catch (Exception e) {
            logger.error("使用反射转换对象到Map失败", e);
        }
        
        return map;
    }
    
    /**
     * 使用反射将Java对象转换为Map (递归转换嵌套对象)
     * 支持处理嵌套的复杂对象，将嵌套对象也转换为Map
     *
     * @param obj 待转换的对象
     * @return 转换后的Map
     */
    public static Map<String, Object> objectToMapDeep(Object obj) {
        if (obj == null) {
            return new HashMap<>();
        }
        
        Map<String, Object> map = new HashMap<>();
        
        try {
            Class<?> clazz = obj.getClass();
            Field[] fields = getAllFields(clazz);
            
            for (Field field : fields) {
                if (Modifier.isStatic(field.getModifiers()) || 
                    field.isAnnotationPresent(JsonIgnore.class)) {
                    continue;
                }
                
                field.setAccessible(true);
                String fieldName = field.getName();
                Object fieldValue = field.get(obj);
                
                // 处理嵌套对象
                if (fieldValue != null) {
                    if (isBasicType(fieldValue.getClass())) {
                        map.put(fieldName, fieldValue);
                    } else if (fieldValue instanceof Map) {
                        map.put(fieldName, fieldValue);
                    } else if (fieldValue instanceof Iterable) {
                        map.put(fieldName, fieldValue);
                    } else {
                        // 递归处理复杂对象
                        map.put(fieldName, objectToMapDeep(fieldValue));
                    }
                } else {
                    map.put(fieldName, null);
                }
            }
        } catch (Exception e) {
            logger.error("使用深度反射转换对象到Map失败", e);
        }
        
        return map;
    }
    
    /**
     * 获取类的所有字段，包括父类的字段
     */
    private static Field[] getAllFields(Class<?> clazz) {
        if (clazz == Object.class) {
            return new Field[0];
        }
        
        // 获取当前类的字段
        Field[] fields = clazz.getDeclaredFields();
        
        // 获取父类的字段
        Field[] parentFields = getAllFields(clazz.getSuperclass());
        
        // 合并当前类和父类的字段
        Field[] allFields = new Field[fields.length + parentFields.length];
        System.arraycopy(fields, 0, allFields, 0, fields.length);
        System.arraycopy(parentFields, 0, allFields, fields.length, parentFields.length);
        
        return allFields;
    }
    
    /**
     * 判断一个类是否为基本类型或其包装类型
     */
    private static boolean isBasicType(Class<?> clazz) {
        return clazz.isPrimitive() || 
               clazz == String.class ||
               clazz == Boolean.class ||
               clazz == Character.class ||
               clazz == Byte.class ||
               clazz == Short.class ||
               clazz == Integer.class ||
               clazz == Long.class ||
               clazz == Float.class ||
               clazz == Double.class ||
               clazz == Date.class ||
               clazz.isEnum();
    }
    
    /**
     * 将Map转换为Java对象
     *
     * @param map 待转换的Map
     * @param clazz 目标类
     * @param <T> 目标类型
     * @return 转换后的对象
     */
    public static <T> T mapToObject(Map<String, Object> map, Class<T> clazz) {
        if (map == null) {
            return null;
        }
        
        try {
            return OBJECT_MAPPER.convertValue(map, clazz);
        } catch (Exception e) {
            logger.error("使用Jackson转换Map到对象失败", e);
            return null;
        }
    }
    
    /**
     * 根据不同场景智能选择最适合的转换方法
     * 
     * @param obj 待转换的对象
     * @return 转换后的Map
     */
    public static Map<String, Object> objectToMap(Object obj) {
        if (obj == null) {
            return new HashMap<>();
        }
        
        // 如果对象本身就是Map类型，直接返回
        if (obj instanceof Map) {
            return (Map<String, Object>) obj;
        }
        
        // 默认使用Jackson转换，速度最快
        return objectToMapByJackson(obj);
    }
} 