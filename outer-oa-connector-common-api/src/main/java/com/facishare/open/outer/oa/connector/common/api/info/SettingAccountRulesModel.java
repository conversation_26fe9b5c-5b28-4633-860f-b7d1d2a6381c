package com.facishare.open.outer.oa.connector.common.api.info;

import com.facishare.open.outer.oa.connector.common.api.enums.BindTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.EnterpriseConfigAccountSyncTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 页面绑定账号同步规则
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SettingAccountRulesModel implements Serializable {
    /**
     * 账号绑定类型
     */
    private EnterpriseConfigAccountSyncTypeEnum syncTypeEnum;

    /**
     * 账号绑定规则
     */
    private BindTypeEnum bindTypeEnum;

    /**
     * 范围移除处理逻辑
     */

    private EmployeeRangeRemoveRule employeeRangeRemoveRule;

    /**
     * 人员离职处理逻辑
     */
    private EmployeeLeaveRule employeeLeaveRule;
    /**
     * 绑定的字段设置
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class EmployeeRangeRemoveRule implements Serializable {
        private static final long serialVersionUID = -1L;

        //是否解除绑定关系
        private Boolean unbind;

        //是否停用CRM员工
        private Boolean stopEmp;
    }

    /**
     * 绑定的字段设置
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class EmployeeLeaveRule implements Serializable {
        private static final long serialVersionUID = -1L;

        //是否解除绑定关系
        private Boolean unbind;

        //是否停用CRM员工
        private Boolean stopEmp;
    }
}
