package com.facishare.open.outer.oa.connector.common.api.result;

import lombok.Data;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/07/13
 */
@Data
public class QyweixinJsapiSignature extends JsapiSignature {

    private static final long serialVersionUID = 5233019919880044421L;
    private String appId; //企业corpId

    /**
     * wx注册agentConfig 返回的参数
     * agentConfig的作用
     * config注入的是企业的身份与权限，而agentConfig注入的是应用的身份与权限。尤其是当调用者为第三方服务商时，
     * 通过config无法准确区分出调用者是哪个第三方应用，而在部分场景下，又必须严谨区分出第三方应用的身份，
     * 此时即需要通过agentConfig来注入应用的身份信息。
     */


    private String agentCorpId; // 企业微信的corpid，必须与当前登录的企业一致

    private String agentId; // 企业微信的应用id

    private long agentTimestamp; // 生成签名的时间戳

    private String agentNonceStr;  // 生成签名的随机串

    private String agentSignature; // 签名，见附录1

    /**
     * 真实的应用appId
     */
    private String realAppId;
}
