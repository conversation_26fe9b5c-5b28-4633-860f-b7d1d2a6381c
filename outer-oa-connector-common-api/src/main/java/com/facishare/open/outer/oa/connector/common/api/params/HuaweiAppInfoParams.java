package com.facishare.open.outer.oa.connector.common.api.params;

import com.facishare.open.outer.oa.connector.common.api.annotation.SecurityField;
import com.facishare.open.outer.oa.connector.common.api.info.BaseOuterOaAppInfoParams;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class HuaweiAppInfoParams extends BaseOuterOaAppInfoParams {
    private static final long serialVersionUID = -1L;

    private String clientId;
    @SecurityField
    private String clientSecret;
}
