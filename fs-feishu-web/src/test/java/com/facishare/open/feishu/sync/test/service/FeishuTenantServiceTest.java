package com.facishare.open.feishu.sync.test.service;

import com.facishare.open.feishu.sync.test.BaseTest;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.data.QueryTenantInfoData;
import com.facishare.open.feishu.syncapi.service.FeishuTenantService;
import org.junit.Test;

import javax.annotation.Resource;

public class FeishuTenantServiceTest extends BaseTest {
    @Resource
    private FeishuTenantService feishuTenantService;

    @Test
    public void queryTenantInfo() {
        Result<QueryTenantInfoData> result = feishuTenantService.queryTenantInfo("cli_a7fcf7b29af85003","14121dcad548175a");
        System.out.println(result);
    }
}
