package com.facishare.open.feishu.sync.service

import com.facishare.open.feishu.syncapi.enums.CalendarEventAttendeeAbilityEnum
import com.facishare.open.feishu.syncapi.enums.CalendarEventAttendeeTypeEnum
import com.facishare.open.feishu.syncapi.enums.CalendarEventVisibilityEnum
import com.facishare.open.feishu.syncapi.enums.CalendarPermissionEnum
import com.facishare.open.feishu.syncapi.model.externalCalendar.ExternalCalendarDetail
import com.facishare.open.feishu.syncapi.model.externalCalendar.ExternalCalendarEvent
import com.facishare.open.feishu.syncapi.model.externalCalendar.ExternalCalendarEventAddAttendeeDetail
import com.facishare.open.feishu.syncapi.service.FeishuCalendarService
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import javax.annotation.Resource

@ContextConfiguration(locations = "classpath:spring-test/applicationContext-test.xml")
class FeishuCalendarServiceTest extends Specification {
    @Resource
    private FeishuCalendarService feishuCalendarService

    def "createCalendars"() {
        given:
        ExternalCalendarDetail externalCalendarDetail = new ExternalCalendarDetail()
        externalCalendarDetail.setSummary("你是谁")
        externalCalendarDetail.setColor(-1)
        externalCalendarDetail.setPermissions(CalendarPermissionEnum.PRIVATE.getCode())
        expect:
        def a = feishuCalendarService.createCalendars("cli_a3ddeb52763b100c", "100d08b69448975d", externalCalendarDetail)
        print(a)
    }

    def "createCalendarEvent"() {
        given:
        ExternalCalendarEvent externalCalendarEvent = new ExternalCalendarEvent()
        externalCalendarEvent.setSummary("test可以添加非官方大哥风格的法国人艰苦一看就\n你绝对的呢")
        externalCalendarEvent.setDescription("test可以添加非官方大哥风格的法国人艰苦一看就\n你绝对的呢")
        ExternalCalendarEvent.TimeInfo startTime = new ExternalCalendarEvent.TimeInfo()
        startTime.setTimestamp(String.valueOf(1706875220L))
        externalCalendarEvent.setStartTime(startTime)
        ExternalCalendarEvent.TimeInfo endTime = new ExternalCalendarEvent.TimeInfo()
        endTime.setTimestamp(String.valueOf(1706878820L))
        externalCalendarEvent.setEndTime(endTime)
        externalCalendarEvent.setAttendeeAbility(CalendarEventAttendeeAbilityEnum.CAN_SEE_OTHERS.getCode())
        ExternalCalendarEvent.Location location = new ExternalCalendarEvent.Location()
        location.setName("大冲冲")
        externalCalendarEvent.setLocation(location)
        externalCalendarEvent.setColor(-1)
        externalCalendarEvent.setVisibility(CalendarEventVisibilityEnum.PRIVATE.getCode())
        List<ExternalCalendarEvent.Reminder> reminders = new LinkedList<>()
        ExternalCalendarEvent.Reminder reminder1 = new ExternalCalendarEvent.Reminder()
        reminder1.setMinutes(6)
        ExternalCalendarEvent.Reminder reminder2 = new ExternalCalendarEvent.Reminder()
        reminder2.setMinutes(11)
        ExternalCalendarEvent.Reminder reminder3 = new ExternalCalendarEvent.Reminder()
        reminder3.setMinutes(16)
        reminders.add(reminder1)
        reminders.add(reminder2)
        reminders.add(reminder3)
        externalCalendarEvent.setReminders(reminders)
        expect:
        def a = feishuCalendarService.createCalendarEvent("cli_a3ddeb52763b100c", "100d08b69448975d", "<EMAIL>", externalCalendarEvent)
        print(a)
    }

    def "addCalendarEventAttendees"() {
        given:
        ExternalCalendarEventAddAttendeeDetail externalCalendarEventAddAttendeeDetail = new ExternalCalendarEventAddAttendeeDetail()
        List<ExternalCalendarEventAddAttendeeDetail.Attendee> attendees = new LinkedList<>()
        ExternalCalendarEventAddAttendeeDetail.Attendee attendee1 = new ExternalCalendarEventAddAttendeeDetail.Attendee()
        attendee1.setType(CalendarEventAttendeeTypeEnum.USER.getCode())
        attendee1.setUserId("ou_0fd979c697c5dd375d12ffb999492a91")
        ExternalCalendarEventAddAttendeeDetail.Attendee attendee2 = new ExternalCalendarEventAddAttendeeDetail.Attendee()
        attendee2.setType(CalendarEventAttendeeTypeEnum.USER.getCode())
        attendee2.setUserId("ou_e014772b2c679b750a68c6c93e354e9b")
        attendees.add(attendee1)
        attendees.add(attendee2)
        externalCalendarEventAddAttendeeDetail.setAttendees(attendees)
        expect:
        def a = feishuCalendarService.addCalendarEventAttendees("cli_a3ddeb52763b100c", "100d08b69448975d", "<EMAIL>", "2071db01-fb90-47af-9da1-50036199d132_0", externalCalendarEventAddAttendeeDetail)
        print(a)
    }

    def "updateCalendarEvent"() {
        given:
        ExternalCalendarEvent externalCalendarEvent = new ExternalCalendarEvent()
        externalCalendarEvent.setSummary("dddddds")
        externalCalendarEvent.setDescription("ddddddds\n你绝对的呢")
        ExternalCalendarEvent.TimeInfo startTime = new ExternalCalendarEvent.TimeInfo()
        startTime.setTimestamp(String.valueOf(1706875220L))
        externalCalendarEvent.setStartTime(startTime)
        ExternalCalendarEvent.TimeInfo endTime = new ExternalCalendarEvent.TimeInfo()
        endTime.setTimestamp(String.valueOf(1706878820L))
        externalCalendarEvent.setEndTime(endTime)
        externalCalendarEvent.setAttendeeAbility(CalendarEventAttendeeAbilityEnum.CAN_SEE_OTHERS.getCode())
        ExternalCalendarEvent.Location location = new ExternalCalendarEvent.Location()
        location.setName("大冲冲")
        externalCalendarEvent.setLocation(location)
        externalCalendarEvent.setColor(-1)
        externalCalendarEvent.setVisibility(CalendarEventVisibilityEnum.PRIVATE.getCode())
        List<ExternalCalendarEvent.Reminder> reminders = new LinkedList<>()
        ExternalCalendarEvent.Reminder reminder1 = new ExternalCalendarEvent.Reminder()
        reminder1.setMinutes(6)
        ExternalCalendarEvent.Reminder reminder2 = new ExternalCalendarEvent.Reminder()
        reminder2.setMinutes(11)
        ExternalCalendarEvent.Reminder reminder3 = new ExternalCalendarEvent.Reminder()
        reminder3.setMinutes(16)
        reminders.add(reminder1)
        reminders.add(reminder2)
        reminders.add(reminder3)
        externalCalendarEvent.setReminders(reminders)
        expect:
        def a = feishuCalendarService.updateCalendarEvent("cli_a3ddeb52763b100c", "100d08b69448975d", "<EMAIL>", "2071db01-fb90-47af-9da1-50036199d132_0", externalCalendarEvent)
        print(a)
    }
}
