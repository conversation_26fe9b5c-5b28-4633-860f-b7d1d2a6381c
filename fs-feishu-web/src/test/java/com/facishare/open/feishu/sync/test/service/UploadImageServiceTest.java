package com.facishare.open.feishu.sync.test.service;

import com.facishare.open.feishu.sync.test.BaseTest;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.service.UploadImageService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.Base64;

public class UploadImageServiceTest extends BaseTest {

    @Autowired
    private UploadImageService uploadImageService;

    @Test
    public void uploadImage(){
        Result<String> stringResult = uploadImageService.uploadImage("cli_a20192f6afb8d00c", "11f8e072d486575f", "message", new File("C:\\Users\\<USER>\\IdeaProjects\\fs-open-feishu-gateway\\feishu-sync-provider\\src\\test\\java\\com\\facishare\\open\\feishu\\sync\\fxiaoke.png"));
        System.out.println(stringResult.toString());
    }

    @Test
    public void test(){
        File file = new File("C:\\Users\\<USER>\\IdeaProjects\\fs-open-feishu-gateway\\feishu-sync-provider\\src\\test\\java\\com\\facishare\\open\\feishu\\sync\\fxiaoke.png");
        try {
            FileInputStream fileInputStream = new FileInputStream(file);
            byte[] bytes = new byte[fileInputStream.available()];
            fileInputStream.read(bytes);
            String baseStr= Base64.getEncoder().encodeToString(bytes);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
