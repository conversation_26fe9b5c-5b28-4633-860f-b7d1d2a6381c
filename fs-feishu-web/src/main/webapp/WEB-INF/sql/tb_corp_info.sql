use `fs-feishu-db`;
CREATE TABLE if not exists tb_corp_info(
    id int auto_increment primary key comment '主键ID',
    display_id VARCHAR(32) NOT NULL COMMENT '企业编号，平台内唯一' ,
    tenant_key VARCHAR(128) NOT NULL COMMENT '开通应用的企业唯一标识' ,
    tenant_name VARCHAR(256) NOT NULL   COMMENT '企业名称' ,
    tenant_tag INT NOT NULL   COMMENT '个人版/团队版标志;0：团队版 2：个人版' ,
    cancellation INT NOT NULL  DEFAULT 0 COMMENT '企业是否已注销，0 正常 1 已注销',
    create_time timestamp default current_timestamp  COMMENT '创建时间' ,
    update_time timestamp default current_timestamp on update current_timestamp COMMENT '更新时间',
)  COMMENT = '企业信息表';