<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context.xsd
        ">
    <context:component-scan base-package="com.facishare.open.feishu.*"/>

    <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer"
          init-method="start" destroy-method="shutdown">
        <constructor-arg name="configName" value="fs-feishu-config"/>
        <constructor-arg name="sectionNames" value="FEISHU_ENTERPRISE_REGISTER_SECTION"/>
        <constructor-arg name="messageListener" ref="enterpriseEventListener"/>
    </bean>

    <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer"
          init-method="start" destroy-method="shutdown">
        <constructor-arg name="configName" value="fs-feishu-config"/>
        <constructor-arg name="sectionNames" value="FEISHU_TODO_SECTION"/>
        <constructor-arg name="messageListener" ref="externalMessageListener"/>
    </bean>

    <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer"
          init-method="start" destroy-method="shutdown">
        <constructor-arg name="configName" value="fs-feishu-config"/>
        <constructor-arg name="sectionNames" value="FEISHU_OBJECT_DATA_SECTION"/>
        <constructor-arg name="messageListener" ref="paasObjectDataListener"/>
    </bean>

    <!--oa外部事件跨云投递-->
    <bean id="outEventDataChangeMQSender" class="com.fxiaoke.rocketmq.producer.AutoConfMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-feishu-config"/>
        <constructor-arg name="sectionNames" value="out-event-data-change-provider-section"/>
    </bean>

    <!--飞书消息事件-->
    <bean id="feishuMsgChangeSender" class="com.fxiaoke.rocketmq.producer.AutoConfMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-feishu-config"/>
        <constructor-arg name="sectionNames" value="feishu-msg-change-provider-section"/>
    </bean>

    <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer"
          init-method="start" destroy-method="shutdown">
        <constructor-arg name="configName" value="fs-feishu-config"/>
        <constructor-arg name="sectionNames" value="FEISHU_MSG_CHANGE_CONSUMER_SECTION"/>
        <constructor-arg name="messageListener" ref="feishuMsgChangeListener"/>
    </bean>


    <!--    feishu-sync-provider spring-mq.xml-->
    <bean id="whatsappMsgNotifyMQSender" class="com.fxiaoke.rocketmq.producer.AutoConfMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-feishu-config"/>
        <constructor-arg name="sectionNames" value="fs-open-whatsapp-msg-mq-section"/>
    </bean>

    <!--oa事件跨云投递-->
    <bean id="oaconnectorEventDataChangeMQSender" class="com.fxiaoke.rocketmq.producer.AutoConfMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-feishu-config"/>
        <constructor-arg name="sectionNames" value="oaconnector-event-data-change-provider-section"/>
    </bean>

<!--    &lt;!&ndash;oa外部事件跨云投递&ndash;&gt;-->
<!--    <bean id="outEventDataChangeMQSender" class="com.fxiaoke.rocketmq.producer.AutoConfMQProducer"-->
<!--          init-method="start">-->
<!--        <constructor-arg name="configName" value="fs-feishu-config"/>-->
<!--        <constructor-arg name="sectionNames" value="out-event-data-change-provider-section"/>-->
<!--    </bean>-->

    <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer"
          init-method="start" destroy-method="shutdown">
        <constructor-arg name="configName" value="fs-feishu-config"/>
        <constructor-arg name="sectionNames" value="oaconnector-event-data-change-consume-section"/>
        <constructor-arg name="messageListener" ref="oaconnectorEventDataChangeListener"/>
    </bean>
    <!--    feishu-sync-provider spring-mq.xml-->
</beans>