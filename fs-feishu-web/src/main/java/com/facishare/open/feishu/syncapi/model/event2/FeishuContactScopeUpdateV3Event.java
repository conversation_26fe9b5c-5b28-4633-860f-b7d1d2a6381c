package com.facishare.open.feishu.syncapi.model.event2;

import com.facishare.open.feishu.syncapi.result.data.DepartmentData;
import com.facishare.open.feishu.syncapi.result.data.UserData;
import com.facishare.open.feishu.syncapi.result.data.UserGroupData;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * 通讯录范围权限被更新
 * <AUTHOR>
 * @date 20220803
 */
@Data
public class FeishuContactScopeUpdateV3Event implements Serializable {
    private Added added;
    private Removed removed;

    @Data
    public static class Added implements Serializable {
        private List<DepartmentData.Department> departments;
        private List<UserData.User> users;
        private List<UserGroupData> userGroups;
    }

    @Data
    public static class Removed implements Serializable {
        private List<DepartmentData.Department> departments;
        private List<UserData.User> users;
        private List<UserGroupData> userGroups;
    }

}
