package com.facishare.open.feishu.syncapi.result.whatsapp;

import lombok.Data;

import java.io.Serializable;

@Data
public class WhatsappCreateTemplateResult implements Serializable {
    /**
     * 模板id
     */
    private String id;
    /**
     * 一般为APPROVED，REJECTED，PENDING
     * 审核状态枚举枚举值:
     * APPROVED: 使用中 IN_APPEAL: 已提出上诉
     * PENDING: 待处理 REJECTED: 已拒绝
     * PENDING_DELETION: 待删除 DELETED: 已删除
     * DISABLED: 不可用 PAUSED: 暂时停用 LIMIT_EXCEEDED: 超出限制
     */
    private String status;
    /**
     * 枚举值：MARKETING,UTILITY,AUTHENTICATION
     */
    private String category;
}
