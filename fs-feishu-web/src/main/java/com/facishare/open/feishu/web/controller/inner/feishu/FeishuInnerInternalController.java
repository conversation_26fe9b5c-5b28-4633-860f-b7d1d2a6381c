package com.facishare.open.feishu.web.controller.inner.feishu;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.outer.event.order.OpenEnterpriseHandlerTemplate;
import com.facishare.open.feishu.sync.manager.EnterpriseBindManager;
import com.facishare.open.feishu.sync.manager.ExternalTodoInstanceManager;
import com.facishare.open.feishu.sync.manager.ToolsManager;
import com.facishare.open.feishu.syncapi.arg.DealTodoPushArg;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.entity.ExternalTodoInstanceEntity;
import com.facishare.open.feishu.syncapi.enums.ApprovalStatusEnum;
import com.facishare.open.feishu.syncapi.enums.ApprovalTaskStatusEnum;
import com.facishare.open.feishu.syncapi.model.event.FeishuAppOpenEvent;
import com.facishare.open.feishu.syncapi.model.event.FeishuOrderPaidEvent;
import com.facishare.open.feishu.syncapi.model.event2.FeishuEventModel2;
import com.facishare.open.feishu.syncapi.model.externalApprovals.ExternalApprovalsTaskUpdateDetail;
import com.facishare.open.feishu.syncapi.model.externalApprovals.ExternalInstancesDetail;
import com.facishare.open.feishu.syncapi.model.jsapi.JsApiSignatureModel;
import com.facishare.open.feishu.syncapi.model.login.FsUserModel;
import com.facishare.open.feishu.syncapi.result.ExternalApprovalTaskResult;
import com.facishare.open.feishu.syncapi.result.ExternalInstancesDetailResult;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.service.EnterpriseBindService;
import com.facishare.open.feishu.syncapi.service.ExternalApprovalsService;
import com.facishare.open.feishu.syncapi.vo.InstanceVo.ExternalMessageInstanceVo;
import com.facishare.open.feishu.web.handler.AppOpenEventHandler;
import com.facishare.open.feishu.web.template.inner.jsapi.FeishuJsApiTemplate;
import com.facishare.open.feishu.web.template.inner.login.FeishuLoginTemplate;
import com.facishare.open.feishu.web.template.model.JsApiModel;
import com.facishare.open.feishu.web.template.outer.event.app.FeishuAppEventTemplate;
import com.facishare.open.feishu.web.template.outer.event.order.FeishuOpenEnterpriseHandlerTemplate;
import com.facishare.open.feishu.web.utils.BeanUtil;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaMessageBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaMessageBindManager;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaEnterpriseBindMapper;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaMessageBindMapper;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaMessageBindParams;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaMessageBindEventTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaMessageBindMsgTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaMessageBindStatusEnum;
import com.facishare.open.outer.oa.connector.i18n.I18NHeaderObj;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.Page;
import com.fxiaoke.crmrestapi.common.data.SearchQuery;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.message.extrnal.platform.model.arg.DealTodoArg;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.CollectionUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.Date;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.ArrayList;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;
import com.facishare.open.feishu.web.service.FeishuApprovalService;
import com.facishare.open.feishu.web.result.FeishuTaskQueryResult;
import com.facishare.open.feishu.web.model.task.FeishuTask;

/**
 * 飞书内部服务接口
 * <AUTHOR>
 * @date 20220726
 */
@RestController
@RequestMapping(value="/erpdss/API/v1/rest/inner/feishu/internal")
// IgnoreI18nFile
public class FeishuInnerInternalController {
    @Resource
    private FeishuLoginTemplate feishuLoginTemplate;
    @Resource
    private EnterpriseBindService enterpriseBindService;

    @ReloadableProperty("feishu_redirect_uri")
    private String feishuRedirectUri;//{"cli_a20192f6afb8d00c":"/hcrm/feishu/login?ticket="}

    @ReloadableProperty("crm_domain")
    private String crmDomain;

    @Resource
    private EnterpriseEditionService enterpriseEditionService;

    @Resource
    private FeishuJsApiTemplate feishuJsApiTemplate;
    @Autowired
    private ToolsManager toolsManager;
    @Resource
    private EnterpriseBindManager enterpriseBindManager;
    @Autowired
    private FeishuOpenEnterpriseHandlerTemplate feishuOpenEnterpriseHandlerTemplate;
    @Autowired
    private AppOpenEventHandler appOpenEventHandler;

    @Autowired
    private OuterOaMessageBindManager outerOaMessageBindManager;
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private ExternalApprovalsService externalApprovalsService;
    @Autowired
    private OuterOaEnterpriseBindMapper outerOaEnterpriseBindMapper;
    @Autowired
    private ExternalTodoInstanceManager externalTodoInstanceManager;
    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;

    @Resource(name = "todoThreadPool")
    private ThreadPoolTaskExecutor todoThreadPool;

    @Resource
    private FeishuApprovalService feishuApprovalService;

    /**
     * 用ticket获取纷享用户信息
     * @param ticket
     * @return
     */
    @RequestMapping(value="/getFsUserInfo",method = RequestMethod.GET)
    @ResponseBody
    public Result<FsUserModel> getFsUserInfo(@RequestParam String ticket) {
        LogUtils.info("FeishuInternalController.getFsUserInfo,ticket={}",ticket);
        MethodContext context = MethodContext.newInstance(ticket);
        feishuLoginTemplate.getFsUserInfoByTicket(context);
        Result<FsUserModel> fsUser = context.getResultData();
        LogUtils.info("FeishuInternalController.getFsUserInfo,fsUser={}",fsUser);
        return fsUser;
    }

    /**
     * 获取飞书签名
     * 1、前端调用，后面需要屏蔽
     * 2、俊文服务调用
     * @param appId
     * @param url
     * @return
     */
    @RequestMapping(value="/getJsApiSignature",method = RequestMethod.GET)
    @ResponseBody
    public Result<JsApiSignatureModel> getJsApiSignature(@RequestParam String appId,
                                                         @RequestParam String url,
                                                         @RequestParam(required = false) String fsEa,
                                                         @RequestParam(required = false) String outEa) {
        if(StringUtils.isAnyEmpty(appId, fsEa, url)) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }

        LogUtils.info("FeishuInternalController.getJsApiSignature,appId={},fsEa={},outEa={},url={}",appId,fsEa,outEa,url);

        JsApiModel jsApiModel = new JsApiModel();
        jsApiModel.setAppId(appId);
        jsApiModel.setFsEa(fsEa);
        jsApiModel.setOutEa(outEa);
        jsApiModel.setUrl(url);

        MethodContext context = MethodContext.newInstance(jsApiModel);
        feishuJsApiTemplate.getJsApiSignature(context);
        Result<JsApiSignatureModel> result = (Result<JsApiSignatureModel>) context.getResult().getData();

        LogUtils.info("FeishuInternalController.getJsApiSignature,result={}",result);
        return result;
    }
    /**
     * 检查并初始化飞书连接器，仅供集成平台调用
     * @param fsEa
     * @return
     */
    @RequestMapping(value="/checkAndInitConnector",method = RequestMethod.GET)
    @ResponseBody
    public Result<Void> checkAndInitConnector(@RequestParam String fsEa,@RequestParam(required = false) String channel,
                                              @RequestParam(required = false) String dataCenterId) {
        LogUtils.info("checkAndInitConnector,fsEa={},dataCenterId={}",fsEa,dataCenterId);
        return enterpriseBindService.checkAndInitConnector(fsEa,dataCenterId,channel);
    }

    /**
     * 更新企业绑定表拓展字段
     * 1、留资功能：函数调用此接口更新拓展字段的是否已留资的字段
     * @return
     */
    @RequestMapping(value = "/enterprise/updateEnterpriseExtend", method = RequestMethod.POST)
    @ResponseBody
    public Result<Void> updateEnterpriseExtend(@RequestParam String fsEa,
                                               @RequestParam String extendField,
                                               @RequestParam Object extendValue,
                                               @RequestParam(required = false) String outEa) {
        if (StringUtils.isEmpty(fsEa) || StringUtils.isEmpty(extendField) || ObjectUtils.isEmpty(extendValue)) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        return enterpriseBindService.updateEnterpriseExtend(fsEa, outEa, extendField, extendValue);
    }

    private GetEnterpriseDataResult getEnterpriseInfo(String fsEa) {
        GetEnterpriseDataArg arg = new GetEnterpriseDataArg();
        arg.setEnterpriseAccount(fsEa);
        GetEnterpriseDataResult result = enterpriseEditionService.getEnterpriseData(arg);
        LogUtils.info("ControllerQYWeixin.getEnterpriseInfo,result={}",result);
        return result;
    }
    @RequestMapping(value = "/transferDb",method =RequestMethod.GET)
    @ResponseBody
    public Result<Void> transferDb(@RequestParam(value = "ea",required = false) String ea) {
        if(StringUtil.isNotEmpty(ea)){
            toolsManager.transfer(ea);
            return Result.newSuccess();
        }
        enterpriseBindManager.batchProcessEnterprises();

        return new Result<>();
    }


    @RequestMapping(value = "/transRules",method =RequestMethod.GET)
    @ResponseBody
    public Result<Void> transRules(@RequestParam(value = "ea",required = false) String ea) {
        if(StringUtil.isNotEmpty(ea)){
            toolsManager.transferRules(ea);
            return Result.newSuccess();
        }
        enterpriseBindManager.batchProcessEnterprises();

        return new Result<>();
    }

    @RequestMapping(value = "/saveOrder",method =RequestMethod.GET)
    @ResponseBody
    public Result<Void> saveOrder(@RequestParam(value = "orderId",required = false)String orderId,
                                  @RequestParam(value = "outTenantId",required = false)String outTenantId,
                                  @RequestParam(value = "pricePlanId",required = false)String pricePlanId,
                                  @RequestParam(value = "appId",required = false)String appId) {
        String data="{\n" +
                "  \"orderId\": \"7494624514269052931\",\n" +
                "  \"pricePlanId\": \"price_a7e8c5803b56500d\",\n" +
                "  \"pricePlanType\": \"trial\",\n" +
                "  \"seats\": 0,\n" +
                "  \"buyCount\": 10,\n" +
                "  \"createTime\": 1744978748000,\n" +
                "  \"payTime\": 1744978748000,\n" +
                "  \"buyType\": \"buy\",\n" +
                "  \"srcOrderId\": \"\",\n" +
                "  \"orderPayPrice\": 0\n" +
                "}";
        FeishuOrderPaidEvent event = JSON.parseObject(data, FeishuOrderPaidEvent.class);
        event.setTenantKey("100d08b69448975d");
        event.setAppId("cli_a3ddeb52763b100c");
        event.setType("order_paid");
        if(StringUtil.isNotEmpty(outTenantId)){
            event.setTenantKey(outTenantId);
        }
        if(StringUtil.isNotEmpty(orderId)){
            event.setOrderId(orderId);
        }
        if(StringUtils.isNotEmpty(appId)){
            event.setAppId(appId);
        }
        if(StringUtils.isNotEmpty(pricePlanId)){
            event.setPricePlanId(pricePlanId);
        }
        MethodContext methodContext= new MethodContext();
        methodContext.setData(event);
        LogUtils.info("OPEN saveOrder EVENT:{}",JSONObject.toJSONString(methodContext));
        feishuOpenEnterpriseHandlerTemplate.execute(event);
        return new Result<>();
    }

    @RequestMapping(value = "/openApp",method =RequestMethod.GET)
    @ResponseBody
    public Result<Void> openApp(@RequestParam(value = "openId",required = false)String openId,
                                  @RequestParam(value = "outTenantId",required = false)String outTenantId,
                                  @RequestParam(value = "appId",required = false)String appId) {
        /**
         * MethodContext(data=FeishuAppOpenEvent(applicants=null,
         * installer=FeishuAppOpenEvent.FeishuAppOpenId(openId=ou_9690e68f13ee4d2017ae0b1865f5c789), installerEmployee=null),
         * result=TemplateResult(code=0, msg=null, data=null))
         */
        FeishuAppOpenEvent feishuAppOpenEvent = new FeishuAppOpenEvent();
        FeishuAppOpenEvent.FeishuAppOpenId feishuAppOpenId = new FeishuAppOpenEvent.FeishuAppOpenId();
        feishuAppOpenId.setOpenId(openId);
        feishuAppOpenEvent.setInstaller(feishuAppOpenId);
        feishuAppOpenEvent.setAppId(appId);
        feishuAppOpenEvent.setTenantKey(outTenantId);
        FeishuEventModel2.EventModelHeader eventModelHeader = new FeishuEventModel2.EventModelHeader();
        LogUtils.info("OPEN APP EVENT:{}",JSONObject.toJSONString(feishuAppOpenEvent));
        appOpenEventHandler.handle(eventModelHeader, JSON.toJSONString(feishuAppOpenEvent));
        return new Result<>();
    }

    @RequestMapping(value = "/fixTodo",method =RequestMethod.GET)
    @ResponseBody
    public Result<Void> fixTodo(@RequestParam(value = "ea",required = false)String ea,
                                @RequestParam(value = "startTime",required = false)Long startTime,
                                @RequestParam(value = "endTime",required = false)Long endTime) {

        LogUtils.info("fixTodo，tenantId：{}，start_time：{}，end_time：{}", ea, startTime, endTime);
        List<String> fsEas = Lists.newArrayList();
        if(StringUtil.isNotEmpty(ea)){
            fsEas.add(ea);
        }else{
            fsEas.addAll(ConfigCenter.TODO_GRAY_EA);
        }

        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (String todoEa : fsEas) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    processEnterpriseData(todoEa, startTime, endTime);
                } catch (Exception e) {
                    LogUtils.error("处理企业数据异常, ea: " + todoEa, e);
                }
            }, todoThreadPool);
            futures.add(future);
        }

        try {
            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get(30, TimeUnit.MINUTES);
            return Result.newSuccess();
        } catch (Exception e) {
            LogUtils.error("fixTodo执行异常", e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR, "处理数据异常: " + e.getMessage());
        }
    }

    @RequestMapping(value = "/fixOriginTodo",method =RequestMethod.GET)
    @ResponseBody
    public Result<Void> fixOriginTodo(@RequestParam(value = "ea",required = false)String ea,
                                @RequestParam(value = "startTime",required = false)Long startTime,
                                @RequestParam(value = "endTime",required = false)Long endTime) {

        LogUtils.info("fixOriginTodo，tenantId：{}，start_time：{}，end_time：{}", ea, startTime, endTime);
        List<String> fsEas = Lists.newArrayList();
        if(StringUtil.isNotEmpty(ea)){
            fsEas.add(ea);
        }else{
            fsEas.addAll(ConfigCenter.TODO_GRAY_EA);
        }

        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (String todoEa : fsEas) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    processOriginData(todoEa, startTime, endTime);
                } catch (Exception e) {
                    LogUtils.error("处理企业数据异常, ea: " + todoEa, e);
                }
            }, todoThreadPool);
            futures.add(future);
        }

        try {
            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get(30, TimeUnit.MINUTES);
            return Result.newSuccess();
        } catch (Exception e) {
            LogUtils.error("fixOriginTodo", e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR, "fixOriginTodo: " + e.getMessage());
        }
    }


    /**
     * 处理单个企业的数据
     */
    private void processEnterpriseData(String todoEa, Long startTime, Long endTime) {
        OuterOaEnterpriseBindEntity enterpriseBindEntity = outerOaEnterpriseBindManager.getEntity(ChannelEnum.feishu, todoEa, null, null);
        if (enterpriseBindEntity == null) {
            LogUtils.warn("企业绑定信息不存在, ea: {}", todoEa);
            return;
        }

        try {
            // 创建查询参数
            OuterOaMessageBindParams params = OuterOaMessageBindParams.builder()
                    .channel(ChannelEnum.feishu)
                    .fsEa(todoEa)
                    .build();
            Integer tenantId = eieaConverter.enterpriseAccountToId(todoEa);
            
            // 分页参数
            int pageSize = 100;
            int pageNo = 1;
            boolean hasMore = true;

            while (hasMore) {
                com.baomidou.mybatisplus.extension.plugins.pagination.Page<OuterOaMessageBindEntity> page = 
                    outerOaMessageBindManager.getEntitiesByTimeRangePage(params, startTime, endTime, pageNo, pageSize);

                if (page == null || CollectionUtils.isEmpty(page.getRecords())) {
                    hasMore = false;
                    continue;
                }

                LogUtils.info("Processing ea: {}, page {}, size: {}, total: {}", todoEa, pageNo, page.getSize(), page.getTotal());

                // 处理当前页的数据
                for (OuterOaMessageBindEntity message : page.getRecords()) {
                    LogUtils.info("process message :{}:{}",message.getFsEa(),message.getTaskId());
                    try {
                        //需要拿到待办的消息
                        List<ObjectData> objectData = queryApprovalTaskData(tenantId, message.getSourceId());
                        if (ObjectUtils.isNotEmpty(objectData)) {
                            ObjectData itemData = objectData.get(0);
                            String state = String.valueOf(itemData.get("state"));
                            if (!state.equals("in_progress")) {
                                if (message.getMessageType().equals(OuterOaMessageBindMsgTypeEnum.todo)) {
                                    //需要根据detail，重新推送
                                    ExternalMessageInstanceVo externalMessageInstanceVo=convertMessageInfo(message.getMessageInfo());
                                    ExternalInstancesDetail externalInstancesDetail = externalMessageInstanceVo.getTodoDetail();
                                    for (ExternalInstancesDetail.Task task : externalInstancesDetail.getTaskList()) {
                                        task.setStatus(ApprovalStatusEnum.APPROVED.name());
                                    }
                                    // 组装数据
                                    externalInstancesDetail.setStatus(ApprovalStatusEnum.APPROVED.name());
                                    Result<ExternalInstancesDetailResult> instancesDetailResultResult = externalApprovalsService.syncExternalApprovals(enterpriseBindEntity.getOutEa(), message.getAppId(), externalInstancesDetail);
                                    LogUtils.info("external approval taskids:{}", instancesDetailResultResult);
                                    message.setMessageInfo(JSON.toJSONString(externalMessageInstanceVo));
                                    message.setStatus(OuterOaMessageBindStatusEnum.approved);
                                    message.setUpdateTime(System.currentTimeMillis());
                                    outerOaMessageBindManager.updateById(message);
                                } else {
                                    if (message.getMessageType().equals(OuterOaMessageBindMsgTypeEnum.bot)) {
                                        //bot消息
                                        ExternalApprovalsTaskUpdateDetail externalApprovalsTaskUpdateDetail = new ExternalApprovalsTaskUpdateDetail();
                                        externalApprovalsTaskUpdateDetail.setStatus(ApprovalStatusEnum.APPROVED.name());
                                        externalApprovalsTaskUpdateDetail.setMessageId(message.getTaskId());
                                        Result<ExternalApprovalTaskResult> externalApprovalTaskResultResult = externalApprovalsService.updateExternalApprovalTask(message.getOutEa(), message.getAppId(), externalApprovalsTaskUpdateDetail);
                                        LogUtils.info("external approval taskids:{}", externalApprovalTaskResultResult);
                                        message.setUpdateTime(System.currentTimeMillis());
                                        message.setStatus(OuterOaMessageBindStatusEnum.approved);
                                        outerOaMessageBindManager.updateById(message);
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        LogUtils.warn("fix error approval for message: {}", message.getId(), e);
                    }
                }

                // 判断是否还有更多数据
                if (page.getCurrent() >= page.getPages()) {
                    hasMore = false;
                } else {
                    pageNo++;
                }
            }
        } catch (Exception e) {
            LogUtils.error("fixTodo执行异常", e);
            return;
        }
    }


    /**
     * 处理单个企业的数据
     */
    private void processOriginData(String todoEa, Long startTime, Long endTime) {
        OuterOaEnterpriseBindEntity enterpriseBindEntity = outerOaEnterpriseBindManager.getEntity(ChannelEnum.feishu, todoEa, null, null);
        if (enterpriseBindEntity == null) {
            LogUtils.warn("企业绑定信息不存在, ea: {}", todoEa);
            return;
        }
        try {
            Date startDate = new Date(startTime);
            Date endDate = new Date(endTime);
            Integer tenantId = eieaConverter.enterpriseAccountToId(todoEa);
            // 创建查询参数
            // 待办事项处理
            Boolean isPageToDo = true;
            Integer pageToDoNum = 1;  // 从第1页开始
            Integer pageToDoSize = 20;  // 每页获取20条记录
            while (isPageToDo) {
                IPage<ExternalTodoInstanceEntity> todoPage = externalTodoInstanceManager.pageQueryBetweenTime(enterpriseBindEntity.getFsEa(),
                        pageToDoNum, pageToDoSize,startDate,endDate);
                List<ExternalTodoInstanceEntity> records = todoPage.getRecords();
                if (org.apache.commons.collections4.CollectionUtils.isEmpty(records)) {
                    isPageToDo = false;
                    break;
                }
                List<OuterOaMessageBindEntity> messageBindEntities = Lists.newArrayList();
                for (ExternalTodoInstanceEntity record : records) {
                    if (record.getStatus().equals(0)) {
                        //需要处理的审批
                        LogUtils.info("message bind data sourceId:{}", record.getSourceId());
                        OuterOaMessageBindParams outerOaMessageBindParams = new OuterOaMessageBindParams();
                        outerOaMessageBindParams.setFsEa(record.getFsEa());
                        outerOaMessageBindParams.setChannel(ChannelEnum.feishu);
                        outerOaMessageBindParams.setSourceId(record.getSourceId());
                        List<OuterOaMessageBindEntity> entities = outerOaMessageBindManager.getEntities(outerOaMessageBindParams);
                        if (ObjectUtils.isNotEmpty(entities)) {
                            for (OuterOaMessageBindEntity message : entities) {
                                try {
                                    //需要拿到待办的消息
                                    List<ObjectData> objectData = queryApprovalTaskData(tenantId, message.getSourceId());
                                    if (ObjectUtils.isNotEmpty(objectData)) {
                                        ObjectData itemData = objectData.get(0);
                                        String state = String.valueOf(itemData.get("state"));
                                        if (!state.equals("in_progress")) {
                                            if (message.getMessageType().equals(OuterOaMessageBindMsgTypeEnum.todo)) {
                                                //需要根据detail，重新推送
                                                ExternalMessageInstanceVo externalMessageInstanceVo = convertMessageInfo(message.getMessageInfo());
                                                ExternalInstancesDetail externalInstancesDetail = externalMessageInstanceVo.getTodoDetail();
                                                for (ExternalInstancesDetail.Task task : externalInstancesDetail.getTaskList()) {
                                                    task.setStatus(ApprovalStatusEnum.APPROVED.name());
                                                }
                                                // 组装数据
                                                externalInstancesDetail.setStatus(ApprovalStatusEnum.APPROVED.name());
                                                Result<ExternalInstancesDetailResult> instancesDetailResultResult = externalApprovalsService.syncExternalApprovals(enterpriseBindEntity.getOutEa(), message.getAppId(), externalInstancesDetail);
                                                LogUtils.info("external approval taskids:{}", instancesDetailResultResult);
                                                message.setMessageInfo(JSON.toJSONString(externalMessageInstanceVo));
                                                message.setStatus(OuterOaMessageBindStatusEnum.approved);
                                                message.setUpdateTime(System.currentTimeMillis());
                                                outerOaMessageBindManager.updateById(message);
                                            } else {
                                                if (message.getMessageType().equals(OuterOaMessageBindMsgTypeEnum.bot)) {
                                                    //bot消息
                                                    ExternalApprovalsTaskUpdateDetail externalApprovalsTaskUpdateDetail = new ExternalApprovalsTaskUpdateDetail();
                                                    externalApprovalsTaskUpdateDetail.setStatus(ApprovalStatusEnum.APPROVED.name());
                                                    externalApprovalsTaskUpdateDetail.setMessageId(message.getTaskId());
                                                    Result<ExternalApprovalTaskResult> externalApprovalTaskResultResult = externalApprovalsService.updateExternalApprovalTask(message.getOutEa(), message.getAppId(), externalApprovalsTaskUpdateDetail);
                                                    LogUtils.info("external approval taskids:{}", externalApprovalTaskResultResult);
                                                    message.setUpdateTime(System.currentTimeMillis());
                                                    message.setStatus(OuterOaMessageBindStatusEnum.approved);
                                                    outerOaMessageBindManager.updateById(message);
                                                }
                                            }
                                        }
                                    }
                                } catch (Exception e) {
                                    LogUtils.warn("fix error approval for message: {}", message.getId(), e);
                                }
                            }
                        }

                    }
                }

                if (!org.apache.commons.collections4.CollectionUtils.isEmpty(messageBindEntities)) {
                    Integer count = outerOaMessageBindManager.batchUpsertInfos(messageBindEntities);
                    LogUtils.info("批量更新待办事项成功，当前页：{}，更新数量：{}", pageToDoNum, count);
                }

                pageToDoNum++;
                if (pageToDoNum > todoPage.getPages()) {
                    isPageToDo = false;
                }
            }


        }catch (Exception e){
            LogUtils.error("fix error approval for message: {}", e);
        }

    }


    private ExternalMessageInstanceVo convertMessageInfo(String messageInfo){
        ExternalMessageInstanceVo externalMessageInstanceVo = JSONObject.parseObject(messageInfo, new TypeReference<ExternalMessageInstanceVo>() {
        });
        //兼容之前的数据
        ExternalInstancesDetail externalInstancesDetail=null;
        if(ObjectUtils.isEmpty(externalMessageInstanceVo)||ObjectUtils.isEmpty(externalMessageInstanceVo.getTodoDetail())){
            externalMessageInstanceVo=new ExternalMessageInstanceVo();
            externalInstancesDetail = JSONObject.parseObject(messageInfo, new TypeReference<ExternalInstancesDetail>() {
            });
            externalMessageInstanceVo.setTodoDetail(externalInstancesDetail);

        }else{
            externalInstancesDetail = externalMessageInstanceVo.getTodoDetail();
        }
        return externalMessageInstanceVo;
    }

    /**
     * 查询crm审批数据
     *
     * @param tenantId
     * @param dataId
     * @return
     */
    public List<ObjectData> queryApprovalTaskData(Integer tenantId, String dataId) {

        HeaderObj headerObj =HeaderObj.newInstance(tenantId, CrmConstants.SYSTEM_USER);
        String erpOrgObj = "ApprovalTaskObj";
        ControllerListArg listArg = new ControllerListArg();
        List<String> fieldValues = Lists.newArrayList();
        fieldValues.add(dataId);
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setLimit(10);
        searchQuery.addFilter("_id", fieldValues, "In");
        listArg.setSearchQuery(searchQuery);
        com.fxiaoke.crmrestapi.common.result.Result<Page<ObjectData>> dataListRes = metadataControllerService.list(headerObj, erpOrgObj, listArg);
        LogUtils.info("ApprovalTaskManager data={}", dataListRes);
        if (!dataListRes.isSuccess()) {
            LogUtils.warn("list erp org obj failed,tenantId:{},res:{}", tenantId, dataListRes);
            return null;
        }
        return dataListRes.getData().getDataList();
    }

    /**
     * 临时使用，仅供集成平台调用
     * @param executeSql
     * @return
     */
    @RequestMapping(value="/fixsql",method = RequestMethod.POST)
    @ResponseBody
    public Result<Void> fixsql(@RequestBody String executeSql) {

        Integer deleteCount = outerOaEnterpriseBindMapper.superUpdateSql(executeSql);
        LogUtils.info("data fixsql:{}",deleteCount);
        return Result.newSuccess();
    }

    /**
     * 临时使用，仅供集成平台调用
     * @param executeSql
     * @return
     */
    @RequestMapping(value="/querySql",method = RequestMethod.POST)
    @ResponseBody
    public Result<Object> querySql(@RequestBody String executeSql) {

        Object querySqlResult = outerOaEnterpriseBindMapper.superQuerySql(executeSql);
        LogUtils.info("data querySql:{}",querySqlResult);
        return Result.newSuccess(querySqlResult);
    }


    /**
     * 同步飞书待办审批状态
     * @param fsEa 纷享企业账号
     * @param fsEmpId CRM人员ID
     * @return 同步结果
     */
    @RequestMapping(value="/syncFeishuApproval",method = RequestMethod.POST)
    @ResponseBody
    public Result<Void> syncFeishuApproval(@RequestParam String fsEa, @RequestParam String fsEmpId) {
        LogUtils.info("syncFeishuApproval start, fsEa={}, fsEmpId={}", fsEa, fsEmpId);
        try {
            // 1. 根据fsEa查询飞书企业绑定信息
            OuterOaEnterpriseBindEntity enterpriseBindEntity = outerOaEnterpriseBindManager.getEntity(ChannelEnum.feishu, fsEa, null, null);
            if (enterpriseBindEntity == null) {
                LogUtils.warn("query enterprise fail , fsEa:{}", fsEa);
                return Result.newError(ResultCodeEnum.PARAM_ILLEGAL, "query enterprise fail");
            }

            // 2. 根据fsEmpId查询飞书用户绑定信息
            OuterOaEmployeeBindEntity employeeBindEntity = outerOaEmployeeBindManager.getEntitiesByDcId(enterpriseBindEntity.getId(), fsEmpId, null);
            if (employeeBindEntity == null) {
                LogUtils.warn("query user fail, fsEa:{}, fsEmpId:{}", fsEa, fsEmpId);
                return Result.newError(ResultCodeEnum.PARAM_ILLEGAL, "query user fail");
            }

            // 3. 调用飞书API查询待办审批
            Result<FeishuTaskQueryResult> taskQueryResult = feishuApprovalService.queryTasks(enterpriseBindEntity.getOutEa(),
                employeeBindEntity.getOutEmpId(), 1); // 1表示待办
            if (!taskQueryResult.isSuccess() || taskQueryResult.getData() == null) {
                LogUtils.warn("query task fail, fsEa:{}, fsEmpId:{}, result:{}", fsEa, fsEmpId, taskQueryResult);
                return Result.newError(ResultCodeEnum.SYSTEM_ERROR, "query task fail");
            }

            // 4. 构造同步审批状态的请求参数并调用API
            if (taskQueryResult.getData().getTasks() != null) {
                for (FeishuTask task : taskQueryResult.getData().getTasks()) {
                    ExternalInstancesDetail externalInstancesDetail = new ExternalInstancesDetail();
                    // 必填字段
                    externalInstancesDetail.setApprovalCode(task.getDefinitionCode());
                    externalInstancesDetail.setInstanceId(task.getProcessExternalId());
                    externalInstancesDetail.setStatus(ApprovalStatusEnum.APPROVED.name());
                    externalInstancesDetail.setUpdateMode("REPLACE");  // UPDATE 或 REPLACE

                    // 设置时间字段（必填）
                    long currentTime = System.currentTimeMillis();
                    externalInstancesDetail.setStartTime(String.valueOf(currentTime));
                    externalInstancesDetail.setEndTime(String.valueOf(currentTime + 24*60*60*1000)); // 24小时后
                    externalInstancesDetail.setUpdateTime(String.valueOf(currentTime));

                    // 设置显示方法（必填）
                    externalInstancesDetail.setDisplayMethod("NORMAL");

                    // 设置标题（必填，使用i18n）
                    externalInstancesDetail.setTitle("@i18n@1");

                    // 构造i18n资源（必填）
                    List<ExternalInstancesDetail.I18NResource> i18nResources = new ArrayList<>();
                    ExternalInstancesDetail.I18NResource i18nResource = new ExternalInstancesDetail.I18NResource();
                    i18nResource.setLocale("zh-CN");
                    i18nResource.setIsDefault(true);

                    List<ExternalInstancesDetail.TextResource> texts = new ArrayList<>();
                    // 添加标题的国际化
                    ExternalInstancesDetail.TextResource titleText = new ExternalInstancesDetail.TextResource();
                    titleText.setKey("@i18n@1");
                    titleText.setValue("待处理的CRM审批流程");
                    texts.add(titleText);

                    // 添加表单项的国际化
                    ExternalInstancesDetail.TextResource formTitleText = new ExternalInstancesDetail.TextResource();
                    formTitleText.setKey("@i18n@2");
                    formTitleText.setValue("流程主题");
                    texts.add(formTitleText);

                    ExternalInstancesDetail.TextResource formValueText = new ExternalInstancesDetail.TextResource();
                    formValueText.setKey("@i18n@3");
                    formValueText.setValue(task.getTitle() != null ? task.getTitle() : "CRM审批流程");
                    texts.add(formValueText);

                    i18nResource.setTexts(texts);
                    i18nResources.add(i18nResource);
                    externalInstancesDetail.setI18nResources(i18nResources);

                    // 构造表单数据（必填）
                    List<ExternalInstancesDetail.Form> formItems = new ArrayList<>();
                    // 添加流程主题
                    ExternalInstancesDetail.Form formItem = new ExternalInstancesDetail.Form();
                    formItem.setName("@i18n@2");
                    formItem.setValue("@i18n@3");
                    formItems.add(formItem);


                    // 构造任务列表（必填）
                    List<ExternalInstancesDetail.Task> taskList = new ArrayList<>();
                    ExternalInstancesDetail.Task taskDetail = new ExternalInstancesDetail.Task();
                    taskDetail.setTaskId(task.getTaskExternalId());
                    taskDetail.setStatus(ApprovalStatusEnum.APPROVED.name());

                    // 设置任务时间（必填）
                    taskDetail.setCreateTime(String.valueOf(currentTime));
                    taskDetail.setEndTime(String.valueOf(currentTime + 24*60*60*1000));

                    // 设置链接（必填）
                    ExternalInstancesDetail.Links links = new ExternalInstancesDetail.Links();
                    if (task.getUrls() != null) {
                        links.setPcLink(task.getUrls().getPc());
                        links.setMobileLink(task.getUrls().getMobile());
                    } else {
                        // 设置默认链接，避免空值
                        String defaultLink = "https://www.fxiaoke.com";
                        links.setPcLink(defaultLink);
                        links.setMobileLink(defaultLink);
                    }
                    taskDetail.setLinks(links);
                    ExternalInstancesDetail.Links externalLinks = BeanUtil.copy(links, ExternalInstancesDetail.Links.class);
                    externalInstancesDetail.setLinks(externalLinks);

                    taskList.add(taskDetail);
                    externalInstancesDetail.setTaskList(taskList);

                    // 调用飞书API同步审批状态
                    Result<ExternalInstancesDetailResult> syncResult = externalApprovalsService.syncExternalApprovals(
                        enterpriseBindEntity.getOutEa(),
                        enterpriseBindEntity.getAppId(),
                        externalInstancesDetail
                    );

                    if (!syncResult.isSuccess()) {
                        LogUtils.warn("update approval fail, task:{}, result:{}", task, syncResult);
                        return Result.newError(ResultCodeEnum.SYSTEM_ERROR, "update approval fail");
                    }
                }
            }

            return Result.newSuccess();
        } catch (Exception e) {
            LogUtils.error("update approval fail", e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR, "update approval fail");
        }
    }

}
