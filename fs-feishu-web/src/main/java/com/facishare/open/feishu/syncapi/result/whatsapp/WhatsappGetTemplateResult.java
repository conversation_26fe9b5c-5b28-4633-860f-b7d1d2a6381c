package com.facishare.open.feishu.syncapi.result.whatsapp;

import com.facishare.open.feishu.syncapi.data.whatsapp.PageInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class WhatsappGetTemplateResult implements Serializable {
    private List<TemplateInfo> data;
    private PageInfo paging;

    @Data
    public static class TemplateInfo implements Serializable {
        /**
         * 模板ID
         */
        private String id;
        /**
         * 模板类型
         */
        private String category;
        /**
         * 模板语言
         */
        private String language;
        /**
         * 模板名称
         */
        private String name;
        /**
         * 审核状态枚举
         * 枚举值:
         * APPROVED: 使用中 IN_APPEAL: 已提出上诉
         * PENDING: 待处理 REJECTED: 已拒绝
         * PENDING_DELETION: 待删除 DELETED: 已删除
         * DISABLED: 不可用 PAUSED: 暂时停用 LIMIT_EXCEEDED: 超出限制
         */
        private String status;
        /**
         * 拒绝原因
         */
        private String rejected_reason;
        /**
         * 模板质量
         */
        private QualityScore quality_score;
        /**
         * 模板组件
         */
        private List<Component> components;

        @Data
        public static class Component implements Serializable {
            /**
             * 组件类型，取值包括：HEADER, BODY, FOOTER, BUTTONS
             */
            private String type;
            /**
             * 仅type= HEADER时有此项且为必须项，type!=HEADER时，无此项。
             * 描述HEADER里内容的类型。取值范围包括：TEXT，DOCUMENT，IMAGE， VIDEO
             */
            private String format;
            /**
             * type= HEADER且format=text时，为必须项；type=BODY时，为必须项；
             * type=FOOTER时，为必须项；type=BUTTONS时，无此项。
             */
            private String text;
            /**
             * 变量示例，当HEADER或者BODY内容中配置了变量时，为必须项。否则无此项。
             */
            private Example example;
            /**
             * 仅type=BUTTONS时，为必须项，否则无此项。
             */
            private List<Button> buttons;
            /**
             * 添加安全建议 身份验证模板 type = BODY 时可选 值为 true/false。
             */
            private Boolean add_security_recommendation;
            /**
             * 验证码的失效时间 身份验证模板 type = FOOTER 时可选 值的可填范围是 1-90。
             */
            private Integer code_expiration_minutes;

            @Data
            public static class Example implements Serializable {
                /**
                 * 当HEADER中 format=DOCUMENT或format=IMAGE或format=VIDEO时，与custom_header_handle_url二选一，否则无此项，如果与custom_header_handle_url同时存在时，以custom_header_handle_url为准
                 */
                private List<String> header_handle;
                /**
                 * 当HEADER中 format=DOCUMENT或format=IMAGE或format=VIDEO时，与header_handle二选一，否则无此项,示例媒体url建议小于1M，否则会超时。如果与header_handle同时存在时，以custom_header_handle_url为准
                 */
                private String custom_header_handle_url;
                /**
                 * 当HEADER中 format=TEXT时为必须项，否则无此项
                 */
                private List<String> header_text;
                /**
                 * 当BODY内容文本中配置了变量时为必须项，根据变量配置，数组中可能有1个或多个值。当BODY内容文本中未配置变量则无此项
                 */
                private List<List<String>> body_text;

                private String type;

                private String filename;
            }

            @Data
            public static class Button implements Serializable {
                /**
                 * 按钮类型，包括QUICK_REPLY, URL , PHONE_NUMBER
                 * QUICK_REPLY即快速回复按钮；
                 * URL即行动号召url按钮
                 * PHONE_NUMBER即行动号召phone_number按钮
                 */
                private String type;
                /**
                 * 按钮的显示文字
                 */
                private String text;
                /**
                 * 行动号召按钮上配置的网址，仅type=URL时为必须项。如果是动态url，则需带上后缀变量1。
                 */
                private String url;
                /**
                 * 行动号召按钮上配置的电话，仅type= PHONE_NUMBER时为必须项
                 */
                private String phone_number;
                /**
                 * 示例的URL, type=URL，且为动态url时为必须项，例如:https://www.baidu.com/user
                 */
                private List<String> example;
                /**
                 * 验证码按钮类型 固定值 ONE_TAP 或者 COPY_CODE ；ONE_TAP的含义是一键填充 COPY_CODE的含义是复制验证码
                 */
                private String otp_type;
                /**
                 * 自动填充按钮文本，展示在按钮上的文案，仅otp_type = ONE_TAP 时为必须项
                 */
                private String autofill_text;
                /**
                 * 安卓包名，仅otp_type = ONE_TAP 时为必须项
                 */
                private String package_name;
                /**
                 * 应用hash，仅otp_type = ONE_TAP 时为必须项
                 */
                private String signature_hash;
            }
        }

        @Data
        public static class QualityScore implements Serializable {
            /**
             * 模板质量枚举
             * 枚举值:
             * GREEN: 高 YELLOW: 中
             * RED: 低 UNKNOWN: 质量待定
             */
            private String score;
        }
    }
}
