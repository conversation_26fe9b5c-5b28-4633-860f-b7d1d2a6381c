package com.facishare.open.feishu.syncapi.enums;

public enum CalendarEventVchatVcTypeEnum {
    VC("vc", "飞书视频会议"),
    THIRD_PARTY("third_party", "第三方链接视频会议"),
    NO_MEETING("no_meeting", "无视频会议"),
    LARK_LIVE("lark_live", "飞书直播"),
    UNKNOWN("unknown", "未知类型");

    private final String code;
    private final String description;

    CalendarEventVchatVcTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
	}