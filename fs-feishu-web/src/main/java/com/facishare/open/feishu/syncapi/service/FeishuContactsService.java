package com.facishare.open.feishu.syncapi.service;


import com.facishare.open.feishu.syncapi.enums.DepartmentIdTypeEnum;
import com.facishare.open.feishu.syncapi.enums.UserIdTypeEnum;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.data.ContactScopeData;

/**
 * 飞书通讯录服务
 *
 * <AUTHOR>
 * @date 20220728
 */
public interface FeishuContactsService {

    /**
     * 获取通讯录授权范围
     *
     * @param appId
     * @return
     */
    Result<ContactScopeData> getContactScopeData(String appId,String tenantKey);

    /**
     * 获取通讯录授权范围
     *
     * @param appId
     * @param tenantKey
     * @param userIdType
     * @param departmentIdType
     * @param pageSize
     * @param pageToken
     * @return
     */
    Result<ContactScopeData> getContactScopeData(String appId,
                                                 String tenantKey,
                                                 UserIdTypeEnum userIdType,
                                                 DepartmentIdTypeEnum departmentIdType,
                                                 String pageToken,
                                                 Integer pageSize);

}
