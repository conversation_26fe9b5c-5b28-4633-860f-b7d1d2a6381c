package com.facishare.open.feishu.syncapi.arg;

import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextCardMessageArg;
import lombok.Data;

import java.io.Serializable;

@Data
public class SendTextCardMessagePushArg implements Serializable {
    private String msgType;
    private String upstreamEa;
    private OuterOaEnterpriseBindEntity enterpriseBindEntity;
    private SendTextCardMessageArg sendTextCardMessageArg;
}
