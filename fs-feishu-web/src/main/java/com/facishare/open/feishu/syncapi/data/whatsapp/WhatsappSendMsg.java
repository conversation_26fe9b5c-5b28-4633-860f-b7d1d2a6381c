package com.facishare.open.feishu.syncapi.data.whatsapp;

import com.facishare.open.feishu.syncapi.base.FsBase;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class WhatsappSendMsg extends FsBase implements Serializable {
    private String appkey;
    /**
     * 商户手机号码，需要带国码。如86158xxxx1795
     */
    private String business_phone;
    /**
     * 发送消息的通道，应用于WhatsApp消息的发送时，值必须为“whatsapp”
     */
    private String messaging_product = ChannelEnum.whatsapp.toString();

    private String recipient_type = "individual";
    /**
     * 消息接收方的WhatsApp号码，需要带国码。如86158xxxx1795
     */
    private String to;
    /**
     * 支持以下消息类型的发送：
     * 1. text 文本
     * 2. image 图片
     * 3. video 视频
     * 4. audio 音频
     * 5. document 文档
     * 6. location 位置
     * 7. sticker 贴图表情
     * 8. template 模板
     * 9. interactive 互动消息
     */
    private String type;

    private TextModel text;

    private ImageModel image;

    private VideoModel video;

    private AudioModel audio;

    private DocumentModel document;

    private LocationModel location;

    private StickerModel sticker;

    private TemplateModel template;

    private InteractiveModel interactive;

    @Data
    public static class TextModel implements Serializable {
        /**
         *文本消息体，可以包含 URL 或者格式化标 识。 格式化标识说明如下： 加粗：需要加粗的文字，如 Your total is $10.50. 斜体：需要斜体的文字，如 Your total is $10.50. 划线：~需要划线的文字~，如 Your total is ~$10.50~.
         */
        private String body;
        /**
         * 是否开启 URL 预览，设置为 true 为开启预 览，设置为 false 为关闭预览。URL 需是以http://或者https://开头，后续以域名表达 的链接地址信息（不识别以 IP 地址表达的 链接地址）。URL 预览是 WhatsApp 客户端 支持的消息呈现功能
         */
        private String preview_url;
    }

    @Data
    public static class ImageModel implements Serializable {
        private String link;
        private String id;
        private String npath;
        private String caption;
        private String type;
        private String filename;
    }

    @Data
    public static class VideoModel implements Serializable {
        private String link;
        private String id;
        private String npath;
        private String caption;
        private String type;
        private String filename;
    }

    @Data
    public static class AudioModel implements Serializable {
        private String link;
        private String id;
        private String npath;
        private String type;
        private String filename;
    }

    @Data
    public static class DocumentModel implements Serializable {
        private String link;
        private String id;
        private String npath;
        private String filename;
        private String type;
    }

    @Data
    public static class LocationModel implements Serializable {
        private Double longitude;
        private Double latitude;
        private String name;
        private String address;
    }

    @Data
    public static class StickerModel implements Serializable {
        private String link;
        private String id;
        private String npath;
        private String type;
        private String filename;
    }

    @Data
    public static class TemplateModel implements Serializable {
        private String name;
        private Language language;
        private List<Component> components;

        @Data
        public static class Language implements Serializable {
            private String code;
            private String policy;
        }

        @Data
        public static class Component implements Serializable {
            private String type;
            private String sub_type;
            private String index;
            private List<Parameter> parameters;

            @Data
            public static class Parameter implements Serializable {
                private String type;
                private String text;
                private String payload;
                private ImageModel image;
                private VideoModel video;
                private DocumentModel document;
            }
        }
    }

    @Data
    public static class InteractiveModel implements Serializable {
        private String type;
        private Header header;
        private Body body;
        private Footer footer;
        private Action action;

        @Data
        public static class Header implements Serializable {
            private String type;
            private String text;
            private DocumentModel document;
            private ImageModel image;
            private VideoModel video;
        }

        @Data
        public static class Body implements Serializable {
            private String text;
        }
        @Data
        public static class Footer implements Serializable {
            private String text;
        }
        @Data
        public static class Action implements Serializable {
            private String button;
            private String text;
            private List<Button> buttons;
            private String catalog_id;
            private String product_retailer_id;
            private List<Section> sections;
            private String mode;
            private String flow_message_version;
            private String flow_token;
            private String flow_id;
            private String flow_cta;
            private String flow_action;
            private FlowActionPayload flow_action_payload;

            @Data
            public static class Button implements Serializable {
                private String type;
                private List<Reply> reply;

                @Data
                public static class Reply implements Serializable {
                    private String id;
                    private String title;
                }
            }

            @Data
            public static class Section implements Serializable {
                private List<ProductItem> product_items;
                private List<Row> rows;
                private String title;
                @Data
                public static class ProductItem implements Serializable {
                    private String product_retailer_id;
                }

                @Data
                public static class Row implements Serializable {
                    private String id;
                    private String title;
                    private String description;
                }
            }

            @Data
            public static class FlowActionPayload implements Serializable {
                private String screen;
                private DataModel data;

                @Data
                public static class DataModel implements Serializable {
                    private String demo;
                }
            }
        }
    }
}
