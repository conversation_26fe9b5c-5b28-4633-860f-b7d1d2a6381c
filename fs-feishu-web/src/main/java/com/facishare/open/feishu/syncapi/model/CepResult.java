package com.facishare.open.feishu.syncapi.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

@Data
public class CepResult implements Serializable {
    @J<PERSON>NField(name = "Result")
    private Result Result;

    @J<PERSON>NField(name = "Value")
    private Value Value;
    @Data
    public static class Result implements Serializable {
        @J<PERSON><PERSON>ield(name = "FailureCode")
        private Integer FailureCode;

        @JSONField(name = "StatusCode")
        private Integer StatusCode;

        @JSONField(name = "UserInfo")
        private UserInfo UserInfo;
    }

    @Data
    public static class Value implements Serializable {
        private Object data;
        private String errCode;
        private String errMsg;
        private String traceMsg;
    }

    @Data
    public static class UserInfo implements Serializable {
        @JSONField(name = "EmployeeID")
        private Integer EmployeeID;

        @JSONField(name = "EnterpriseAccount")
        private String EnterpriseAccount;
    }
}
