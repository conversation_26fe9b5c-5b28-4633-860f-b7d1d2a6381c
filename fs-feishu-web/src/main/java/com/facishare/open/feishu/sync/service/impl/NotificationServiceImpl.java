package com.facishare.open.feishu.sync.service.impl;

import com.facishare.converter.EIEAConverter;
import com.facishare.open.feishu.sync.utils.IdGenerator;
import com.facishare.open.feishu.syncapi.arg.SendTextNoticeArg;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.service.NotificationService;
import com.facishare.warehouse.api.model.arg.NTempFileUploadArg;
import com.facishare.warehouse.api.model.result.NTempFileUploadResult;
import com.facishare.warehouse.api.service.NFileStorageService;
import com.fxiaoke.otherrestapi.openmessage.arg.SendTextMessageArg;
import com.fxiaoke.otherrestapi.openmessage.common.MessageData;
import com.fxiaoke.otherrestapi.openmessage.common.MessageSendTypeEnum;
import com.fxiaoke.otherrestapi.openmessage.common.MessageTypeEnum;
import com.fxiaoke.otherrestapi.openmessage.result.SendTextMessageResult;
import com.fxiaoke.otherrestapi.openmessage.service.SendMessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.Charset;
import java.util.List;

@Slf4j
@Service("notificationService")
public class NotificationServiceImpl implements NotificationService {
    @Autowired
    private SendMessageService sendMessageService;
    @Autowired
    private IdGenerator idGenerator;
    @Autowired
    private NFileStorageService storageService;
    @Autowired
    private EIEAConverter eieaConverter;

    /**
     * 发送ERP数据同步应用通知到企信
     * 文本消息
     *
     * @param arg
     * @return
     */
    @Override
    public Result<Void> sendNotice(SendTextNoticeArg arg) {
        String msg = arg.getMsg();
        String ea;
        if (StringUtils.isBlank(arg.getEnterpriseAccount())) {
            String tenantId = arg.getTenantId();
            int intTenantId = Integer.parseInt(tenantId);
            ea = eieaConverter.enterpriseIdToAccount(intTenantId);
        } else {
            ea = arg.getEnterpriseAccount();
        }
        List<Integer> receivers = arg.getReceivers();
        String appId = ConfigCenter.ERP_SYNC_DATA_APP_ID;
        String msgTitle = arg.getMsgTitle();
        if (StringUtils.isNotBlank(msgTitle)) {
            msg = String.format("---%s---\n%s", msgTitle, msg);
        }
        //如果文本信息过长，则转换成文本文件发送
        if (msg.length() < ConfigCenter.NOTICE_MAX_SIZE) {
            sendTextMessage(ea, receivers, appId, msg);
        } else {
            sendTextTransFileMsg(ea, receivers, appId, msgTitle, msg);
        }
        return new Result<>();
    }

    public String uploadTnFile(String ea, Integer userId, byte[] bytes) {
        NTempFileUploadArg arg = new NTempFileUploadArg();
        arg.setBusiness("FS_OPEN_QYWX_GATEWAY");
        arg.setData(bytes);
        arg.setEa(ea);
        arg.setSourceUser("E." + userId);
        NTempFileUploadResult result = storageService.nTempFileUpload(arg);
        if (result != null && result.getTempFileName() != null) {
            return result.getTempFileName();
        } else {
            log.error("uploadTnFile failed");
        }
        return null;
    }

    private void sendTextTransFileMsg(String ea, List<Integer> toUserList, String appId, String title, String content) {
        String tnPath = uploadTnFile(ea, -10000, content.getBytes(Charset.defaultCharset()));
        if (tnPath == null) {
            log.error("sendTextTransFileMsg uploadTnFile failed,ea:{},toUserList:{},content:{}", ea, toUserList, content);
            return;
        }
        String previewUrl = String.format(ConfigCenter.PREVIEW_FILE_PATH, tnPath + ".txt");
        String msg = String.format("---%s---\n结果已转成文件：%s", title, previewUrl);
        sendTextMessage(ea, toUserList, appId, msg);
    }

    /**
     * 发送文本消息
     *
     * @param ea
     * @param toUserList
     * @param appId
     * @param content
     */
    private void sendTextMessage(String ea, List<Integer> toUserList, String appId, String content) {
        //最大长度为2000
        if (content.length() > 1998) {
            content = content.substring(0, 1900) + "...";
        }
        SendTextMessageArg arg = new SendTextMessageArg();
        arg.setMessageSendType(MessageSendTypeEnum.THIRD_PARTY_PUSH.getValue());
        MessageData textMessageVO = new MessageData();
        textMessageVO.setEnterpriseAccount(ea);
        textMessageVO.setToUserList(toUserList);
        textMessageVO.setAppId(appId);
        textMessageVO.setContent(content);
        textMessageVO.setType(MessageTypeEnum.TEXT.getType());
        textMessageVO.setPostId(idGenerator.get());
        arg.setTextMessageVO(textMessageVO);
        SendTextMessageResult sendTextMessageResult = sendMessageService.sendTextMessage(arg);
        if (!sendTextMessageResult.isSuccess()) {
            log.error("send text msg failed,arg:{},result:{}", arg, sendTextMessageResult);
        }
    }
}
