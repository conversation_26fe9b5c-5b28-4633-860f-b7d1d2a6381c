package com.facishare.open.feishu.web.handler;

import com.alibaba.fastjson.JSON;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.enums.BuyTypeEnum;
import com.facishare.open.feishu.syncapi.enums.PricePlanTypeEnum;
import com.facishare.open.feishu.syncapi.model.event.FeishuAppOpenEvent;
import com.facishare.open.feishu.syncapi.model.event.FeishuOrderPaidEvent;
import com.facishare.open.feishu.syncapi.model.event2.FeishuEventModel2;
import com.facishare.open.feishu.web.template.outer.event.app.FeishuAppEventTemplate;
import com.facishare.open.feishu.web.template.outer.event.order.FeishuOrderPaidEventHandlerTemplate;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.fxiaoke.api.IdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 首次启用应用事件处理器
 */
@Component
public class AppOpenEventHandler extends FeishuEventHandler {
    @Resource
    private FeishuAppEventTemplate feishuAppEventTemplate;
    @Resource
    private FeishuOrderPaidEventHandlerTemplate feishuOrderPaidEventHandlerTemplate;


    @Override
    public String getSupportEventType() {
        return "app_open";
    }

    @Override
    public String handle(FeishuEventModel2.EventModelHeader header, String eventData) {
        FeishuAppOpenEvent event = JSON.parseObject(eventData, FeishuAppOpenEvent.class);
        LogUtils.info("AppOpenEventHandler.handle,event={}",event);

        MethodContext context = MethodContext.newInstance(event);
        //lark现在只有应用开通事件，没有订单事件，需要在应用开通补充mock下单逻辑
        feishuAppEventTemplate.onAppOpen(context);
        if(ConfigCenter.CURRENT_CHANNEL_ISV_CHANNEL.equals(ConfigCenter.LARK_CONSTANTS)){
            String orderId=IdGenerator.get();

            FeishuOrderPaidEvent feishuOrderPaidEvent= FeishuOrderPaidEvent.builder()
                    .orderId(orderId)
                    .pricePlanId(ConfigCenter.LARK_POINT_PLANT_ID)
                    .pricePlanType(PricePlanTypeEnum.trial)
                    .seats(0)
                    .buyCount(1)
                    .createTime(System.currentTimeMillis())
                     .payTime(System.currentTimeMillis()).buyType(BuyTypeEnum.buy)
                    .srcOrderId(null)
                    .orderPayPrice(0)
                    .build();
            feishuOrderPaidEvent.setAppId(event.getAppId());
            feishuOrderPaidEvent.setType("order_paid");
            feishuOrderPaidEvent.setTenantKey(event.getTenantKey());
            feishuOrderPaidEventHandlerTemplate.execute(feishuOrderPaidEvent);
        }

//        AppInfoEntity entity = AppInfoEntity.builder()
//                .appId(event.getAppId())
//                .tenantKey(event.getTenantKey())
//                .applicants(JSONObject.toJSONString(event.getApplicants()))
//                .installerOpenId(event.getInstaller()!=null ? event.getInstaller().getOpenId() : null)
//                .installerEmployeeOpenId(event.getInstallerEmployee()!=null ? event.getInstallerEmployee().getOpenId() : null)
//                .build();
//        Result<Integer> result = appService.updateAppInfo(entity);
//        LogUtils.info("AppOpenEventHandler.handle,result={}",result);
//
//        Result<Void> updateCorpInfo = corpService.updateCorpInfo(event.getAppId(), event.getTenantKey());
//        LogUtils.info("AppOpenEventHandler.handle,updateCorpInfo={}",updateCorpInfo);

        return SUCCESS;
    }
}
