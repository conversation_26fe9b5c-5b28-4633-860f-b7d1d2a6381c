package com.facishare.open.feishu.web.handler;

import com.alibaba.fastjson.JSON;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.feishu.syncapi.model.event.FeishuAppUninstalledEvent;
import com.facishare.open.feishu.syncapi.model.event2.FeishuEventModel2;
import com.facishare.open.feishu.web.template.outer.event.app.FeishuAppEventTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 飞书企业解散事件处理器
 */
@Component
public class AppUninstalledEventHandler extends FeishuEventHandler {
    @Resource
    private FeishuAppEventTemplate feishuAppEventTemplate;

    @Override
    public String getSupportEventType() {
        return "app_uninstalled";
    }

    @Override
    public String handle(FeishuEventModel2.EventModelHeader header, String eventData) {
        FeishuAppUninstalledEvent event = JSON.parseObject(eventData, FeishuAppUninstalledEvent.class);
        MethodContext context = MethodContext.newInstance(event);
        feishuAppEventTemplate.onAppStop(context);

        //todo 飞书企业解散，对当前应用相关的数据进行清理，包括清理中间表，注销CRM企业等
        return SUCCESS;
    }
}
