package com.facishare.open.feishu.syncapi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("tb_external_todo_task")
public class ExternalTodoTaskEntity implements Serializable {
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 渠道
     */
    private ChannelEnum channel;
    /**
     * 纷享企业账号
     */
    private String fsEa;
    /**
     * 外部企业账号
     */
    private String outEa;
    /**
     * 资源id
     */
    private String sourceId;
    /**
     * 任务id
     */
    private String taskId;
    /**
     * 人员id
     */
    private String outUserId;
    /**
     * 创建者人员id
     */
    private String outOwnerId;
    /**
     * 状态 0未完成，1同意，2驳回，3已删除
     */
    private Integer status;

    private Date createTime;
    private Date updateTime;
}
