package com.facishare.open.feishu.syncapi.service;

import com.facishare.open.feishu.syncapi.enums.MsgTypeEnum;
import com.facishare.open.feishu.syncapi.model.event2.FeishuIMMessageReceiveV1Event;
import com.facishare.open.feishu.syncapi.result.Result;

import java.util.List;

public interface MsgService {
    /**
     * 批量发送飞书消息
     * @param msgType
     * @param fsEa
     * @param fsUserIdList
     * @param content
     * @param appId
     * @return
     */
    Result<Void> batchSend(MsgTypeEnum msgType,
                             String fsEa,
                             List<String> fsUserIdList,
                             Object content,
                             String appId);

    /**
     * 批量发送飞书消息
     * @param appId
     * @param tenantKey
     * @param msgType
     * @param receiveIds
     * @param content
     * @return
     */
    Result<Void> batchSend(String appId,
                      String tenantKey,
                      MsgTypeEnum msgType,
                      List<String> receiveIds,
                      Object content);

    /**
     * 接收机器人事件后，给该消息发送人回复特定的消息
     * @param event
     * @return
     */
    Result<Void> replyIMMessage(String appId,
                                String tenantKey,
                                FeishuIMMessageReceiveV1Event event);

    /**
     * 接收机器人进群事件后，给该群聊发送特定的消息
     * @param appId
     * @param tenantKey
     * @param chatId
     * @return
     */
    Result<Void> GroupChatAddBotToSendMessage(String appId,
                                              String tenantKey,
                                              String chatId);
}
