package com.facishare.open.feishu.sync.service.impl;

import com.alibaba.fastjson.TypeReference;
import com.facishare.open.feishu.sync.manager.EnterpriseBindManager;
import com.facishare.open.feishu.sync.manager.FeishuAppManager;
import com.facishare.open.feishu.sync.manager.UrlManager;
import com.facishare.open.feishu.syncapi.arg.QueryTenantAccessTokenArg;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.result.data.GetAppAdminUserListData;
import com.facishare.open.feishu.syncapi.service.FeishuAppService;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaEnterpriseBindMapper;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEnterpriseBindParams;
import com.facishare.open.order.contacts.proxy.api.enums.FeishuUrlEnum;
import com.facishare.open.order.contacts.proxy.api.network.ProxyHttpClient;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

@Service("feishuAppService")
public class FeishuAppServiceImpl implements FeishuAppService {
    @Resource
    private FeishuAppManager feishuAppManager;
    @Resource
    private ProxyHttpClient proxyHttpClient;
    @Resource
    private EnterpriseBindManager enterpriseBindManager;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private UrlManager urlManager;

    @Override
    public Result<GetAppAdminUserListData> getAdminUserList(String appId,String tenantKey) {
        String url = urlManager.getFeishuUrl(tenantKey, appId, FeishuUrlEnum.user_v4_app_admin_user_list.getUrl());

        Result<String> tenantAccessToken = feishuAppManager.getTenantAccessToken(appId, tenantKey);
        if(tenantAccessToken.isSuccess()==false) {
            return Result.newError(tenantAccessToken.getCode(),tenantAccessToken.getMsg());
        }

        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("Authorization", "Bearer " + tenantAccessToken.getData());
        headerMap.put(com.facishare.open.order.contacts.proxy.api.consts.GlobalValue.outLimit, String.format(FeishuUrlEnum.user_v4_app_admin_user_list.getName(), appId, tenantKey));
        headerMap.put(com.facishare.open.order.contacts.proxy.api.consts.GlobalValue.outLimitTimes, ConfigCenter.OUT_LIMIT_TIMES);
        Result<GetAppAdminUserListData> result = proxyHttpClient.getUrl(url, headerMap, new TypeReference<Result<GetAppAdminUserListData>>() {
        });
        return result;
    }

    @Override
    public Result<String> getTenantAccessToken(String channel, QueryTenantAccessTokenArg arg) {
        String outEa = arg.getOutEa();
        String appId = arg.getAppId();
        OuterOaEnterpriseBindParams outerOaEnterpriseBindParams=OuterOaEnterpriseBindParams.builder().outEa(outEa).build();
        List<OuterOaEnterpriseBindEntity> enterpriseBindList = outerOaEnterpriseBindManager.getEntities(outerOaEnterpriseBindParams);
        if(CollectionUtils.isEmpty(enterpriseBindList)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        //根据channel去获取不同连接器的授权信息
        if(channel.equals(ChannelEnum.feishu.name())) {
            return feishuAppManager.getTenantAccessToken(appId, outEa);
        }
        return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
    }
}
