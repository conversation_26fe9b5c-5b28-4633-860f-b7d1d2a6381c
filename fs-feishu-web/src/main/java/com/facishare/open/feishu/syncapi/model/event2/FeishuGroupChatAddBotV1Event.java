package com.facishare.open.feishu.syncapi.model.event2;

import lombok.Data;

import java.io.Serializable;

/**
 * 机器人被用户添加至群聊时触发此事件
 */
@Data
public class FeishuGroupChatAddBotV1Event implements Serializable {
    private String chatId;
    private OperatorId operatorId;
    private String external;
    private String operatorTenantKey;
    private String name;
    private I18nNames I18nNames;

    @Data
    public static class OperatorId implements Serializable {
        private String unionId;
        private String userId;
        private String openId;
    }

    @Data
    public static class I18nNames implements Serializable {
        private String zhCn;
        private String enUs;
        private String jaJp;
    }
}
