package com.facishare.open.feishu.web.utils;

import org.apache.commons.lang3.StringUtils;

import java.nio.charset.StandardCharsets;

/**
 * 使用Xor进行加密和解密
 */
public class XorUtils {
    /**
     * 异或操作
     * @param rawData 要异或的字节数组
     * @param number 运算数
     * @return 异或运算后的字节数组
     */
    public static byte[] Xor(byte[] rawData, int number) {
        byte[] encodeData = new byte[rawData.length];
        for (int i = 0; i < rawData.length; i++) //遍历字符数组
        {
            encodeData[i] = (byte) (rawData[i] ^ number); //对每个数组元素进行异或运算
        }
        return encodeData;
    }

    /**
     * 异或加密
     * @param rawStr 原文
     * @param key 密钥
     * @return 密文
     */
    public static String EncodeByXor(String rawStr, String key) {
        if(StringUtils.isEmpty(rawStr)) {
            return null;
        }
        // 通过字符串拿到密钥
        int number = 1;
        for (int i = 0; i < key.length(); i++) {
            number *= key.charAt(i);
        }

        // 转成字节数组
        byte[] rawData = rawStr.getBytes(StandardCharsets.UTF_8);
        byte[] encodeData = Xor(rawData, number);

        // 把字节数组转成某种格式的字符串，方便传输（格式可以自定义，好解析就行）
        StringBuilder encodeStr = new StringBuilder();
        for (byte b : encodeData) {
            encodeStr.append(b).append("x");
        }

        return encodeStr.toString();
    }

    /**
     * 异或解密
     * @param encodeStr 密文
     * @param key 密钥
     * @return 原文
     */
    public static String DecodeByXor(String encodeStr, String key) {
        if(StringUtils.isEmpty(encodeStr)) {
            return null;
        }
        // 通过字符串拿到密钥
        int number = 1;
        for (int i = 0; i < key.length(); i++) {
            number *= key.charAt(i);
        }

        // 解析EncodeByXor方法中的字节数组格式，找到加密后的字节数组
        String[] strings = encodeStr.substring(0,encodeStr.length()-1).split("x");
        byte[] rawData = new byte[strings.length];
        for (int i = 0; i < strings.length; i++) {
            rawData[i] = Byte.parseByte(strings[i]);
        }

        // 异或一下
        byte[] encodeData = Xor(rawData, number);

        // 重新编码成原始字符串
        return new String(encodeData,StandardCharsets.UTF_8);
    }
}
