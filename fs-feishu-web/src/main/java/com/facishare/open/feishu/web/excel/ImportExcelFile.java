package com.facishare.open.feishu.web.excel;

import com.facishare.open.feishu.web.enums.TemplateTypeEnum;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/31
 */
public class ImportExcelFile implements Serializable {

    @Data
    @ToString(exclude = "file")
    public static class MappingDataArg implements Serializable {
        private static final long serialVersionUID = -8761645379239188525L;
        /**
         * 纷享EA
         */
        @NotNull
        private String fsEa;

        /**
         * 企微企业ID
         */
        @NotNull
        private String outEa;

        /**
         * 纷享用户ID
         */
        private Integer fsUserId;

        @NotNull(message = "模板类型不能为空")
        private TemplateTypeEnum templateType;

        /**
         * npath
         */
        @NotNull
        private String npath;

//        @JSONField(serialize = false)
//        private MultipartFile file;
    }

    @Data
    public static class Result implements Serializable {
        private static final long serialVersionUID = 9181946463285972887L;

        /**
         * 解析成功行数
         */
        private Integer invokedNum;

        /**
         * 插入数据行数
         */
        private Integer insertNum;

        /**
         * 更新数据行数
         */
        private Integer updateNum;

        /**
         * 删除数据行数
         */
        private Integer deleteNum;

        /**
         * 导入结果Message
         */
        private String message;

        /**
         * 数据不合法行信息,该信息汇总展示
         */
        private List<ErrorRow> importErrorRows;

        /**
         * 解析失败行信息，该信息每条分别展示
         */
        private List<ErrorRow> invokeExceptionRows;

        /**
         * 汇总信息
         */
        private String printMsg= StringUtils.EMPTY;
        public void setPrintMsg(String value) {
            this.printMsg = value;
            this.message = value;
        }

        public Result() {
            invokedNum = 0;
            insertNum = 0;
            updateNum = 0;
            deleteNum = 0;
            importErrorRows = new ArrayList<>();
            invokeExceptionRows = new ArrayList<>();
        }

        public void incrDelete(int incr) {
            deleteNum+=incr;
        }

        public void incrInsert(int incr) {
            insertNum+=incr;
        }

        public void incrUpdate(int incr) {
            updateNum+=incr;
        }

        public void incrInvoke(int incr) {
            invokedNum+=incr;
        }

        public void addImportError(Integer rowNo, String errMsg) {
            ErrorRow errorRow = new ErrorRow();
            errorRow.setRowNo(rowNo);
            errorRow.setErrMsg(errMsg);
            this.importErrorRows.add(errorRow);
        }

        public void addInvokeExceptionRow(Integer rowNo, String errMsg) {
            ErrorRow errorRow = new ErrorRow();
            errorRow.setRowNo(rowNo);
            errorRow.setErrMsg(errMsg);
            this.importErrorRows.add(errorRow);
        }
    }

    /**
     * 导入excel文件错误结果行
     */
    @Data
    public static class ErrorRow implements Serializable {
        private static final long serialVersionUID = 3968251167626669331L;
        /**
         * 行号
         */
        private Integer rowNo;
        /**
         * 错误信息
         */
        private String errMsg;
    }
}
