package com.facishare.open.feishu.sync.manager;

import com.facishare.open.oa.base.dbproxy.mongo.dao.FsDepartmentInfoMongoDao;
import com.facishare.open.oa.base.dbproxy.mongo.document.FsDepartmentInfoDoc;
import com.facishare.open.oa.base.dbproxy.mongo.document.FsUserInfoDoc;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.result.DeleteResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component("fsDepartmentInfoManager")
public class FsDepartmentInfoManager {
    @Autowired
    private FsDepartmentInfoMongoDao fsDepartmentInfoMongoDao;

    public BulkWriteResult batchReplace(List<FsDepartmentInfoDoc> docs) {
        BulkWriteResult bulkWriteResult = fsDepartmentInfoMongoDao.batchReplace(docs);
        return bulkWriteResult;
    }

    public Long countDocuments(String fsEa) {
        Long counts = fsDepartmentInfoMongoDao.countDocuments(fsEa);
        return counts;
    }

    public List<FsDepartmentInfoDoc> queryUserInfos(String fsEa, Integer status) {
        List<FsDepartmentInfoDoc> docs = fsDepartmentInfoMongoDao.queryDepartmentInfos(fsEa, status);
        return docs;
    }

    public List<FsDepartmentInfoDoc> queryDepartmentInfosByIds(String fsEa, Integer status, List<Integer> departmentIds) {
        List<FsDepartmentInfoDoc> docs = fsDepartmentInfoMongoDao.queryDepartmentInfosByIds(fsEa, status, departmentIds);
        return docs;
    }

    public DeleteResult deleteNotInCollectionDocs(String fsEa, List<FsDepartmentInfoDoc> docs) {
        return fsDepartmentInfoMongoDao.deleteNotInCollectionDocs(fsEa, docs);
    }
}
