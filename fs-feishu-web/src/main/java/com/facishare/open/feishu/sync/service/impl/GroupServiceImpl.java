package com.facishare.open.feishu.sync.service.impl;

import com.alibaba.fastjson.TypeReference;
import com.facishare.open.feishu.sync.manager.FeishuAppManager;
import com.facishare.open.feishu.sync.manager.UrlManager;
import com.facishare.open.order.contacts.proxy.api.enums.FeishuUrlEnum;
import com.facishare.open.order.contacts.proxy.api.network.ProxyHttpClient;
import com.facishare.open.feishu.syncapi.model.Group.*;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.service.GroupService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;

/**
 * 飞书对接-用户组接口实现类
 * 作者：周健鑫
 * 时间：2022/5/26 10:03:26
 */
@Service("groupService")
public class GroupServiceImpl implements GroupService {
    @Resource
    private ProxyHttpClient proxyHttpClient;

    @Resource
    private FeishuAppManager feishuAppManager;
    @Autowired
    private UrlManager urlManager;

    /**
     * 飞书对接-自建项目-获取用户组
     * @param appId 应用唯一标识，创建应用后获得
     * @param groupId   用户组id
     * @return  用户组信息
     */
    @Override
    public Result<Group> getGroup(String appId,String groupId) {
        return getGroup(appId, null, groupId);
    }

    @Override
    public Result<Group> getGroup(String appId, String tenantKey, String groupId) {

        String url = urlManager.getFeishuUrl(tenantKey, appId, FeishuUrlEnum.QUERY_GROUP_SIMPLE_LIST.getUrl())+groupId;
        Result<String> tenantAccessToken = feishuAppManager.getTenantAccessToken(appId, tenantKey);
        if(tenantAccessToken.isSuccess()==false) {
            return Result.newError(tenantAccessToken.getCode(),tenantAccessToken.getMsg());
        }

        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("Authorization","Bearer "+tenantAccessToken.getData());
        GetGroupResult getGroupResult = proxyHttpClient.getUrl(url, headerMap, new TypeReference<GetGroupResult>() {
        });
        System.out.println(getGroupResult);

        return Result.newSuccess(getGroupResult.getData().getGroup());
    }

    /**
     * 飞书对接-自建项目-获取用户组列表
     * @param appId 应用唯一标识，创建应用后获得
     * @param type  用户组类型 可选值有：1->普通用户组 默认值：1
     * @param pageSize  分页大小 最大值：50
     * @param pageToken 分页标记，第一次请求不填，表示从头开始遍历；分页查询结果还有更多项时会同时返回新的 page_token，下次遍历可采用该 page_token 获取查询结果
     * @return  用户组列表信息
     */
    @Override
    public Result<GetGroupListData> getGroupList(String appId,Integer type, Integer pageSize, String pageToken) {
        return getGroupList(appId, null, type, pageSize, pageToken);
    }

    @Override
    public Result<GetGroupListData> getGroupList(String appId, String tenantKey, Integer type, Integer pageSize, String pageToken) {
        String url = urlManager.getFeishuUrl(tenantKey,appId,FeishuUrlEnum.QUERY_GROUP_SIMPLE_LIST.getUrl());
        url += "?page_size=" + pageSize.toString();

        if (StringUtils.isNotEmpty(pageToken)) {
            url += "&page_token=" + pageToken;
        }
        url += "&type=" + type;
        Result<String> tenantAccessToken = feishuAppManager.getTenantAccessToken(appId, tenantKey);
        if(tenantAccessToken.isSuccess()==false) {
            return Result.newError(tenantAccessToken.getCode(),tenantAccessToken.getMsg());
        }

        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("Authorization","Bearer "+tenantAccessToken.getData());
        GetGroupListResult getGroupListResult = proxyHttpClient.getUrl(url, headerMap, new TypeReference<GetGroupListResult>() {
        });

        return Result.newSuccess(getGroupListResult.getData());
    }

    /**
     * 飞书对接-自建项目-获取用户组成员信息
     * @param appId 应用唯一标识，创建应用后获得
     * @param groupId   用户组id
     * @param pageSize  分页大小 最大值：50
     * @param pageToken 分页标记，第一次请求不填，表示从头开始遍历；分页查询结果还有更多项时会同时返回新的 page_token，下次遍历可采用该 page_token 获取查询结果
     * @param memberIdType  欲获取成员ID类型。当member_type =user时候，member_id_type表示user_id_type，可选值为open_id：member_type =user时候，表示用户的open_id union_id：member_type =user时候，表示用户的union_id user_id：member_type =user时候，表示用户的user_id，默认值为open_id
     * @param memberType    期待获取的用户组成员的类型，取值为 user 默认值为 user
     * @return  用户组中成员信息
     */
    @Override
    public Result<GetGroupMemberData> getGroupMemberList(String appId,String groupId, Integer pageSize, String pageToken, String memberIdType, String memberType) {
        return getGroupMemberList(appId,null, groupId, pageSize, pageToken, memberIdType, memberType);
    }

    @Override
    public Result<GetGroupMemberData> getGroupMemberList(String appId, String tenantKey, String groupId, Integer pageSize, String pageToken, String memberIdType, String memberType) {

        String url = urlManager.getFeishuUrl(tenantKey,appId,FeishuUrlEnum.QUERY_GROUP_SIMPLE_LIST.getUrl())+groupId+"/member/simplelist";
        url += "?page_size=" + pageSize.toString();

        if (StringUtils.isNotEmpty(pageToken)) {
            url += "&page_token=" + pageToken;
        }
        if (StringUtils.isNotEmpty(memberIdType)) {
            url += "&member_id_type=" + memberIdType;
        }
        if (StringUtils.isNotEmpty(memberType)) {
            url += "&member_type=" + memberType;
        }

        Result<String> tenantAccessToken = feishuAppManager.getTenantAccessToken(appId, tenantKey);
        if(tenantAccessToken.isSuccess()==false) {
            return Result.newError(tenantAccessToken.getCode(),tenantAccessToken.getMsg());
        }

        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("Authorization","Bearer "+tenantAccessToken.getData());
        GetGroupMemberResult getGroupMemberResult = proxyHttpClient.getUrl(url, headerMap, new TypeReference<GetGroupMemberResult>() {
        });

        return Result.newSuccess(getGroupMemberResult.getData());
    }
}
