const { ref, onMounted } = Vue;
const { ElMessage, ElMessageBox } = ElementPlus;

export default {
    template: `
        <div class="config-container">
            <div class="search-form">
                <el-form :inline="true" :model="searchForm" class="demo-form-inline">
                    <el-form-item label="渠道">
                        <el-select v-model="searchForm.channel" placeholder="请选择渠道" clearable style="width: 160px">
                            <el-option
                                v-for="item in channelOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="数据中心ID">
                        <el-input v-model="searchForm.dcId" placeholder="请输入数据中心ID" clearable></el-input>
                    </el-form-item>
                    <el-form-item label="配置类型">
                        <el-select v-model="searchForm.type" placeholder="请选择配置类型" clearable style="width: 160px">
                            <el-option
                                v-for="item in configTypeOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="handleSearch">查询</el-button>
                        <el-button @click="handleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </div>

            <div class="operation-bar">
                <el-button type="primary" @click="handleAdd">新增配置</el-button>
            </div>

            <el-table
                :data="tableData"
                border
                style="width: 100%"
                :header-cell-style="{background:'#f5f7fa'}"
                v-loading="loading">
                <el-table-column prop="id" label="ID" width="80" fixed></el-table-column>
                <el-table-column prop="channel" label="渠道" width="120">
                    <template #default="scope">
                        <el-tag type="info">{{ getChannelLabel(scope.row.channel) }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="dcId" label="数据中心ID" width="120" show-overflow-tooltip></el-table-column>
                <el-table-column prop="fsEa" label="纷享企业EA" width="120" show-overflow-tooltip></el-table-column>
                <el-table-column prop="outEa" label="外部企业EA" width="120" show-overflow-tooltip></el-table-column>
                <el-table-column prop="appId" label="应用ID" width="120" show-overflow-tooltip></el-table-column>
                <el-table-column prop="type" label="配置类型" width="120">
                    <template #default="scope">
                        <el-tag type="success">{{ getConfigTypeLabel(scope.row.type) }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="configInfo" label="配置值" min-width="300">
                    <template #default="scope">
                        <div class="config-info">{{ formatConfigInfo(scope.row.configInfo) }}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="creator" label="创建人" width="120" show-overflow-tooltip></el-table-column>
                <el-table-column prop="createTime" label="创建时间" width="160">
                    <template #default="scope">
                        {{ formatTime(scope.row.createTime) }}
                    </template>
                </el-table-column>
                <el-table-column prop="updater" label="更新人" width="120" show-overflow-tooltip></el-table-column>
                <el-table-column prop="updateTime" label="更新时间" width="160">
                    <template #default="scope">
                        {{ formatTime(scope.row.updateTime) }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="120" fixed="right">
                    <template #default="scope">
                        <div class="operation-buttons">
                            <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
                            <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>

            <div class="pagination">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="page"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="size"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                    background>
                </el-pagination>
            </div>

            <el-dialog
                :title="dialogTitle"
                v-model="dialogVisible"
                width="600px"
                :close-on-click-modal="false"
                destroy-on-close>
                <el-form
                    :model="form"
                    label-width="120px"
                    :rules="rules"
                    ref="formRef"
                    class="dialog-form">
                    <el-form-item label="渠道" prop="channel">
                        <el-select v-model="form.channel" placeholder="请选择渠道" style="width: 100%">
                            <el-option
                                v-for="item in channelOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="数据中心ID" prop="dcId">
                        <el-input v-model="form.dcId" placeholder="请输入数据中心ID"></el-input>
                    </el-form-item>
                    <el-form-item label="纷享企业EA" prop="fsEa">
                        <el-input v-model="form.fsEa" placeholder="请输入纷享企业EA"></el-input>
                    </el-form-item>
                    <el-form-item label="外部企业EA" prop="outEa">
                        <el-input v-model="form.outEa" placeholder="请输入外部企业EA"></el-input>
                    </el-form-item>
                    <el-form-item label="应用ID" prop="appId">
                        <el-input v-model="form.appId" placeholder="请输入应用ID"></el-input>
                    </el-form-item>
                    <el-form-item label="配置类型" prop="type">
                        <el-select v-model="form.type" placeholder="请选择配置类型" style="width: 100%">
                            <el-option
                                v-for="item in configTypeOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="配置值" prop="configInfo">
                        <el-input
                            type="textarea"
                            v-model="form.configInfo"
                            :rows="10"
                            placeholder="请输入JSON格式的配置值"
                            :autosize="{ minRows: 10, maxRows: 20 }">
                        </el-input>
                    </el-form-item>
                </el-form>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="dialogVisible = false">取 消</el-button>
                        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确 定</el-button>
                    </span>
                </template>
            </el-dialog>
        </div>
    `,
    setup() {
        const loading = ref(false);
        const submitLoading = ref(false);
        const formRef = ref(null);

        const searchForm = ref({
            channel: '',
            dcId: '',
            type: ''
        });

        const tableData = ref([]);
        const page = ref(1);
        const size = ref(10);
        const total = ref(0);

        const dialogVisible = ref(false);
        const dialogTitle = ref('新增配置');
        const form = ref({
            id: '',
            channel: '',
            dcId: '',
            fsEa: '',
            outEa: '',
            appId: '',
            type: '',
            configInfo: ''
        });

        const rules = {
            channel: [{ required: true, message: '请选择渠道', trigger: 'change' }],
            dcId: [{ required: true, message: '请输入数据中心ID', trigger: 'blur' }],
            type: [{ required: true, message: '请选择配置类型', trigger: 'change' }],
            configInfo: [
                { required: true, message: '请输入配置值', trigger: 'blur' },
                {
                    validator: (rule, value, callback) => {
                        if (value) {
                            try {
                                JSON.parse(value);
                                callback();
                            } catch (e) {
                                callback(new Error('请输入有效的JSON格式'));
                            }
                        } else {
                            callback();
                        }
                    },
                    trigger: 'blur'
                }
            ]
        };

        const channelOptions = ref([]);
        const configTypeOptions = ref([]);

        const getChannelLabel = (value) => {
            const option = channelOptions.value.find(opt => opt.value === value);
            return option ? option.label : value;
        };

        const getConfigTypeLabel = (value) => {
            const option = configTypeOptions.value.find(opt => opt.value === value);
            return option ? option.label : value;
        };

        const loadEnums = async () => {
            try {
                const [channelResponse, typeResponse] = await Promise.all([
                    axios.get('/admin/config/enums/channel'),
                    axios.get('/admin/config/enums/type')
                ]);
                if (channelResponse.data) {
                    channelOptions.value = channelResponse.data;
                }
                if (typeResponse.data) {
                    configTypeOptions.value = typeResponse.data;
                }
            } catch (error) {
                console.error('加载枚举值失败:', error);
                ElMessage.error('加载枚举值失败');
            }
        };

        const fetchData = async () => {
            loading.value = true;
            try {
                const params = {
                    page: page.value,
                    size: size.value,
                    channel: searchForm.value.channel,
                    dcId: searchForm.value.dcId,
                    type: searchForm.value.type
                };
                const response = await axios.get('/admin/config/list', { params });
                if (response.data.success) {
                    const pageResult = response.data.data;
                    tableData.value = pageResult.data || [];
                    total.value = pageResult.total || 0;
                } else {
                    ElMessage.error(response.data.msg || '获取数据失败');
                }
            } catch (error) {
                console.error('获取数据失败:', error);
                ElMessage.error('获取数据失败');
            } finally {
                loading.value = false;
            }
        };

        const handleSearch = () => {
            page.value = 1;
            fetchData();
        };

        const handleReset = () => {
            searchForm.value = {
                channel: '',
                dcId: '',
                type: ''
            };
            handleSearch();
        };

        const resetForm = () => {
            form.value = {
                id: '',
                channel: '',
                dcId: '',
                fsEa: '',
                outEa: '',
                appId: '',
                type: '',
                configInfo: ''
            };
            if (formRef.value) {
                formRef.value.resetFields();
            }
        };

        const handleAdd = () => {
            dialogTitle.value = '新增配置';
            resetForm();
            dialogVisible.value = true;
        };

        const handleEdit = (row) => {
            dialogTitle.value = '编辑配置';
            resetForm();
            form.value = {
                id: row.id,
                channel: row.channel,
                dcId: row.dcId,
                fsEa: row.fsEa,
                outEa: row.outEa,
                appId: row.appId,
                type: row.type,
                configInfo: ''
            };

            if (row.configInfo) {
                try {
                    const parsedConfig = typeof row.configInfo === 'string' ?
                        JSON.parse(row.configInfo) : row.configInfo;
                    form.value.configInfo = JSON.stringify(parsedConfig, null, 2);
                } catch (e) {
                    form.value.configInfo = row.configInfo;
                }
            }

            dialogVisible.value = true;
        };

        const handleSubmit = async () => {
            if (!formRef.value) return;

            try {
                await formRef.value.validate();
                submitLoading.value = true;

                const data = { ...form.value };
                if (data.configInfo) {
                    try {
                        const jsonObj = typeof data.configInfo === 'string' ?
                            JSON.parse(data.configInfo) : data.configInfo;
                        data.configInfo = typeof jsonObj === 'string' ?
                            jsonObj : JSON.stringify(jsonObj);
                    } catch (e) {
                        ElMessage.error('配置值JSON格式不正确');
                        submitLoading.value = false;
                        return;
                    }
                }

                const url = form.value.id ? '/admin/config/update' : '/admin/config/add';
                const response = await axios.post(url, data);

                if (response.data.success) {
                    ElMessage.success(form.value.id ? '更新成功' : '添加成功');
                    dialogVisible.value = false;
                    fetchData();
                } else {
                    ElMessage.error(response.data.message || '操作失败');
                }
            } catch (error) {
                console.error('表单验证失败:', error);
            } finally {
                submitLoading.value = false;
            }
        };

        const handleDelete = async (row) => {
            try {
                await ElMessageBox.confirm('确认要删除该配置吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                });

                const response = await axios.post('/admin/config/delete', { id: row.id });
                if (response.data.success) {
                    ElMessage.success('删除成功');
                    if (tableData.value.length === 1 && page.value > 1) {
                        page.value--;
                    }
                    fetchData();
                } else {
                    ElMessage.error(response.data.message || '删除失败');
                }
            } catch (error) {
                if (error !== 'cancel') {
                    console.error('删除失败:', error);
                    ElMessage.error('删除失败');
                }
            }
        };

        const handleSizeChange = (val) => {
            size.value = val;
            page.value = 1;
            fetchData();
        };

        const handleCurrentChange = (val) => {
            page.value = val;
            fetchData();
        };

        const formatTime = (timestamp) => {
            if (!timestamp) return '';
            const date = new Date(timestamp);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });
        };

        const formatConfigInfo = (configInfo) => {
            if (!configInfo) return '';
            try {
                let jsonObj = configInfo;
                if (typeof configInfo === 'string') {
                    try {
                        jsonObj = JSON.parse(configInfo);
                    } catch (e) {
                        return configInfo;
                    }
                }
                return JSON.stringify(jsonObj, null, 2);
            } catch (e) {
                console.error('格式化配置信息失败:', e);
                return String(configInfo);
            }
        };

        onMounted(() => {
            loadEnums();
            fetchData();
        });

        return {
            loading,
            submitLoading,
            formRef,
            searchForm,
            tableData,
            page,
            size,
            total,
            dialogVisible,
            dialogTitle,
            form,
            rules,
            channelOptions,
            configTypeOptions,
            getChannelLabel,
            getConfigTypeLabel,
            handleSearch,
            handleReset,
            handleAdd,
            handleEdit,
            handleDelete,
            handleSubmit,
            handleSizeChange,
            handleCurrentChange,
            formatTime,
            formatConfigInfo
        };
    }
};

// 添加需要的样式
const style = document.createElement('style');
style.textContent = `
    .config-container {
        padding: 0;
    }
    
    .search-form {
        margin-bottom: 24px;
        background: #fff;
        padding: 24px 24px 0;
    }
    
    .operation-bar {
        margin-bottom: 16px;
    }
    
    .config-info {
        white-space: pre-wrap;
        font-family: monospace;
        max-height: 100px;
        overflow-y: auto;
    }
    
    .operation-buttons {
        display: flex;
        justify-content: center;
        gap: 8px;
    }
    
    .dialog-form {
        padding: 20px;
    }
    
    .el-dialog__body {
        padding: 0;
    }
    
    .pagination {
        margin-top: 16px;
        display: flex;
        justify-content: flex-end;
    }
`;
document.head.appendChild(style); 