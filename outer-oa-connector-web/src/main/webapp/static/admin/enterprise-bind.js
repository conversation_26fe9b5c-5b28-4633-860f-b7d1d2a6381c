const { ref, onMounted } = Vue;
const { ElMessage, ElMessageBox } = ElementPlus;

export default {
    template: `
        <div class="enterprise-bind-container">
            <div class="search-form">
                <el-form :inline="true" :model="searchForm" class="demo-form-inline">
                    <el-form-item label="纷享企业EA">
                        <el-input v-model="searchForm.fsEa" placeholder="请输入纷享企业EA" clearable></el-input>
                    </el-form-item>
                    <el-form-item label="外部企业EA">
                        <el-input v-model="searchForm.outerEa" placeholder="请输入外部企业EA" clearable></el-input>
                    </el-form-item>
                    <el-form-item label="绑定状态">
                        <el-select v-model="searchForm.bindStatus" placeholder="请选择状态" clearable style="width: 160px">
                            <el-option
                                v-for="item in bindStatusOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="handleSearch">查询</el-button>
                        <el-button @click="handleReset">重置</el-button>
                    </el-form-item>
                </el-form>
            </div>

            <div class="operation-bar">
                <el-button type="primary" @click="handleAdd">新增绑定</el-button>
            </div>

            <el-table
                :data="tableData"
                border
                style="width: 100%"
                :header-cell-style="{background:'#f5f7fa'}"
                v-loading="loading">
                <el-table-column prop="id" label="ID" width="80" fixed></el-table-column>
                <el-table-column prop="channel" label="渠道" width="100">
                    <template #default="scope">
                        {{ getChannelLabel(scope.row.channel) }}
                    </template>
                </el-table-column>
                <el-table-column prop="fsEa" label="纷享企业EA" width="120" show-overflow-tooltip></el-table-column>
                <el-table-column prop="outEa" label="外部企业EA" width="120" show-overflow-tooltip></el-table-column>
                <el-table-column prop="appId" label="应用ID" width="120" show-overflow-tooltip></el-table-column>
                <el-table-column prop="connectInfo" label="连接信息" min-width="200">
                    <template #default="scope">
                        <div class="connect-info">{{ formatConnectInfo(scope.row.connectInfo) }}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="bindType" label="绑定类型" width="100">
                    <template #default="scope">
                        <el-tag :type="scope.row.bindType === 'manual' ? 'primary' : 'success'">
                            {{ getBindTypeLabel(scope.row.bindType) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="bindStatus" label="绑定状态" width="100">
                    <template #default="scope">
                        <el-tag :type="getBindStatusType(scope.row.bindStatus)">
                            {{ getBindStatusLabel(scope.row.bindStatus) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="createTime" label="创建时间" width="160">
                    <template #default="scope">
                        {{ formatTime(scope.row.createTime) }}
                    </template>
                </el-table-column>
                <el-table-column prop="updateTime" label="更新时间" width="160">
                    <template #default="scope">
                        {{ formatTime(scope.row.updateTime) }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="120" fixed="right">
                    <template #default="scope">
                        <div class="operation-buttons">
                            <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
                            <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>

            <div class="pagination">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="page"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="size"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                    background>
                </el-pagination>
            </div>

            <el-dialog
                :title="dialogTitle"
                v-model="dialogVisible"
                width="600px"
                :close-on-click-modal="false"
                destroy-on-close>
                <el-form
                    :model="form"
                    label-width="120px"
                    :rules="rules"
                    ref="formRef"
                    class="dialog-form">
                    <el-form-item label="渠道" prop="channel">
                        <el-select v-model="form.channel" placeholder="请选择渠道" style="width: 100%">
                            <el-option
                                v-for="item in channelOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="纷享企业EA" prop="fsEa">
                        <el-input v-model="form.fsEa" placeholder="请输入纷享企业EA"></el-input>
                    </el-form-item>
                    <el-form-item label="外部企业EA" prop="outEa">
                        <el-input v-model="form.outEa" placeholder="请输入外部企业EA"></el-input>
                    </el-form-item>
                    <el-form-item label="应用ID" prop="appId">
                        <el-input v-model="form.appId" placeholder="请输入应用ID"></el-input>
                    </el-form-item>
                    <el-form-item label="连接信息" prop="connectInfo">
                        <el-input
                            type="textarea"
                            v-model="form.connectInfo"
                            :rows="5"
                            placeholder="请输入JSON格式的连接信息"
                            :autosize="{ minRows: 5, maxRows: 10 }">
                        </el-input>
                    </el-form-item>
                    <el-form-item label="绑定类型" prop="bindType">
                        <el-select v-model="form.bindType" placeholder="请选择绑定类型" style="width: 100%">
                            <el-option
                                v-for="item in bindTypeOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="绑定状态" prop="bindStatus">
                        <el-select v-model="form.bindStatus" placeholder="请选择绑定状态" style="width: 100%">
                            <el-option
                                v-for="item in bindStatusOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="dialogVisible = false">取 消</el-button>
                        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确 定</el-button>
                    </span>
                </template>
            </el-dialog>
        </div>
    `,
    setup() {
        const loading = ref(false);
        const submitLoading = ref(false);
        const formRef = ref(null);

        const searchForm = ref({
            fsEa: '',
            outerEa: '',
            bindStatus: ''
        });

        const tableData = ref([]);
        const page = ref(1);
        const size = ref(10);
        const total = ref(0);

        const dialogVisible = ref(false);
        const dialogTitle = ref('新增企业绑定');
        const form = ref({
            id: '',
            channel: '',
            fsEa: '',
            outEa: '',
            appId: '',
            connectInfo: '',
            bindType: '',
            bindStatus: ''
        });

        const rules = {
            channel: [{ required: true, message: '请选择渠道', trigger: 'change' }],
            fsEa: [{ required: true, message: '请输入纷享企业EA', trigger: 'blur' }],
            outEa: [{ required: true, message: '请输入外部企业EA', trigger: 'blur' }],
            bindType: [{ required: true, message: '请选择绑定类型', trigger: 'change' }],
            bindStatus: [{ required: true, message: '请选择绑定状态', trigger: 'change' }],
            connectInfo: [
                { required: true, message: '请输入连接信息', trigger: 'blur' },
                {
                    validator: (rule, value, callback) => {
                        if (value) {
                            try {
                                JSON.parse(value);
                                callback();
                            } catch (e) {
                                callback(new Error('请输入有效的JSON格式'));
                            }
                        } else {
                            callback();
                        }
                    },
                    trigger: 'blur'
                }
            ]
        };

        const channelOptions = ref([]);
        const bindTypeOptions = [
            { value: 'manual', label: '手动绑定' },
            { value: 'auto', label: '自动绑定' }
        ];
        const bindStatusOptions = [
            { value: 'create', label: '创建状态' },
            { value: 'normal', label: '正常状态' },
            { value: 'stop', label: '停用状态' }
        ];

        const getChannelLabel = (value) => {
            const option = channelOptions.value.find(opt => opt.value === value);
            return option ? option.label : value;
        };

        const getBindStatusType = (status) => {
            const statusMap = {
                'create': 'info',
                'normal': 'success',
                'stop': 'danger'
            };
            return statusMap[status] || 'info';
        };

        const loadChannelEnums = async () => {
            try {
                const response = await axios.get('/admin/config/enums/channel');
                if (response.data) {
                    channelOptions.value = response.data;
                }
            } catch (error) {
                console.error('获取渠道枚举失败:', error);
                ElMessage.error('获取渠道枚举失败');
            }
        };

        const fetchData = async () => {
            loading.value = true;
            try {
                const params = {
                    page: page.value,
                    size: size.value,
                    fsEa: searchForm.value.fsEa,
                    outerEa: searchForm.value.outerEa,
                    bindStatus: searchForm.value.bindStatus
                };
                const response = await axios.get('/admin/enterprise/list', { params });
                if (response.data.success) {
                    const result = response.data.data;
                    if (result && result.success) {
                        tableData.value = result.data || [];
                        total.value = result.total || 0;
                    }
                }
            } catch (error) {
                console.error('获取数据失败:', error);
                ElMessage.error('获取数据失败');
            } finally {
                loading.value = false;
            }
        };

        const handleSearch = () => {
            page.value = 1;
            fetchData();
        };

        const handleReset = () => {
            searchForm.value = {
                fsEa: '',
                outerEa: '',
                bindStatus: ''
            };
            handleSearch();
        };

        const resetForm = () => {
            form.value = {
                id: '',
                channel: '',
                fsEa: '',
                outEa: '',
                appId: '',
                connectInfo: '',
                bindType: '',
                bindStatus: ''
            };
            if (formRef.value) {
                formRef.value.resetFields();
            }
        };

        const handleAdd = () => {
            dialogTitle.value = '新增企业绑定';
            resetForm();
            dialogVisible.value = true;
        };

        const handleEdit = (row) => {
            dialogTitle.value = '编辑企业绑定';
            resetForm();
            form.value = {
                id: row.id,
                channel: row.channel,
                fsEa: row.fsEa,
                outEa: row.outEa,
                appId: row.appId,
                connectInfo: typeof row.connectInfo === 'string' ? row.connectInfo : JSON.stringify(row.connectInfo, null, 2),
                bindType: row.bindType,
                bindStatus: row.bindStatus
            };
            dialogVisible.value = true;
        };

        const handleSubmit = async () => {
            if (!formRef.value) return;

            try {
                await formRef.value.validate();
                submitLoading.value = true;

                const data = { ...form.value };
                if (data.connectInfo) {
                    try {
                        data.connectInfo = JSON.parse(data.connectInfo);
                    } catch (e) {
                        ElMessage.error('连接信息JSON格式不正确');
                        return;
                    }
                }

                const url = form.value.id ? '/admin/enterprise/update' : '/admin/enterprise/add';
                const response = await axios.post(url, data);

                if (response.data.success) {
                    ElMessage.success(form.value.id ? '更新成功' : '添加成功');
                    dialogVisible.value = false;
                    fetchData();
                } else {
                    ElMessage.error(response.data.message || '操作失败');
                }
            } catch (error) {
                console.error('表单验证失败:', error);
            } finally {
                submitLoading.value = false;
            }
        };

        const handleDelete = async (row) => {
            try {
                await ElMessageBox.confirm('确认要删除该绑定吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                });

                const response = await axios.post('/admin/enterprise/delete', { id: row.id });
                if (response.data.success) {
                    ElMessage.success('删除成功');
                    if (tableData.value.length === 1 && page.value > 1) {
                        page.value--;
                    }
                    fetchData();
                } else {
                    ElMessage.error(response.data.message || '删除失败');
                }
            } catch (error) {
                if (error !== 'cancel') {
                    console.error('删除失败:', error);
                    ElMessage.error('删除失败');
                }
            }
        };

        const handleSizeChange = (val) => {
            size.value = val;
            page.value = 1;
            fetchData();
        };

        const handleCurrentChange = (val) => {
            page.value = val;
            fetchData();
        };

        const getBindTypeLabel = (value) => {
            const option = bindTypeOptions.find(opt => opt.value === value);
            return option ? option.label : value;
        };

        const getBindStatusLabel = (value) => {
            const option = bindStatusOptions.find(opt => opt.value === value);
            return option ? option.label : value;
        };

        const formatTime = (timestamp) => {
            if (!timestamp) return '';
            const date = new Date(timestamp);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });
        };

        const formatConnectInfo = (connectInfo) => {
            if (!connectInfo) return '';
            try {
                return typeof connectInfo === 'string' ?
                    JSON.stringify(JSON.parse(connectInfo), null, 2) :
                    JSON.stringify(connectInfo, null, 2);
            } catch (e) {
                return connectInfo;
            }
        };

        onMounted(() => {
            loadChannelEnums();
            fetchData();
        });

        return {
            loading,
            submitLoading,
            formRef,
            searchForm,
            tableData,
            page,
            size,
            total,
            dialogVisible,
            dialogTitle,
            form,
            rules,
            channelOptions,
            bindTypeOptions,
            bindStatusOptions,
            getChannelLabel,
            getBindStatusType,
            handleSearch,
            handleReset,
            handleAdd,
            handleEdit,
            handleDelete,
            handleSubmit,
            handleSizeChange,
            handleCurrentChange,
            getBindTypeLabel,
            getBindStatusLabel,
            formatTime,
            formatConnectInfo
        };
    }
};

// 添加需要的样式
const style = document.createElement('style');
style.textContent = `
    .enterprise-bind-container {
        padding: 0;
    }
    
    .search-form {
        margin-bottom: 24px;
        background: #fff;
        padding: 24px 24px 0;
    }
    
    .operation-bar {
        margin-bottom: 16px;
    }
    
    .connect-info {
        white-space: pre-wrap;
        font-family: monospace;
        max-height: 100px;
        overflow-y: auto;
    }
    
    .operation-buttons {
        display: flex;
        justify-content: center;
        gap: 8px;
    }
    
    .dialog-form {
        padding: 20px;
    }
    
    .el-dialog__body {
        padding: 0;
    }
    
    .pagination {
        margin-top: 16px;
        display: flex;
        justify-content: flex-end;
    }
`;
document.head.appendChild(style); 