/**
 * 飞书网关管理系统共享JavaScript
 */
document.addEventListener('DOMContentLoaded', function () {
    // 获取菜单项元素
    var enterpriseMenuItem = document.getElementById('enterprise-menu-item');
    var configMenuItem = document.getElementById('config-menu-item');

    if (enterpriseMenuItem && configMenuItem) {
        // 设置菜单点击事件
        enterpriseMenuItem.addEventListener('click', function () {
            window.location.href = 'enterprise.html';
        });

        configMenuItem.addEventListener('click', function () {
            window.location.href = 'config.html';
        });
    }
}); 