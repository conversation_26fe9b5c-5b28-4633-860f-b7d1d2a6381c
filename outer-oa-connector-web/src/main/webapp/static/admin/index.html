<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>飞书网关管理系统</title>
    <!-- 引入通用样式 -->
    <link rel="stylesheet" href="common.css">
    <!-- 引入Vue3生产版本 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.prod.js"></script>
    <!-- 引入Element Plus样式和组件库 -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/element-plus"></script>
    <!-- 引入Axios -->
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <!-- 引入图标库 -->
    <link rel="stylesheet" href="//at.alicdn.com/t/font_3273147_f8kh0cxnj6.css">
</head>

<body>
    <div id="app">
        <div class="app-container">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="sidebar-title">飞书网关管理系统</div>
                <div v-for="menu in menus" :key="menu.key" class="menu-item"
                    :class="{ active: activeMenu === menu.key }" @click="activeMenu = menu.key">
                    <i :class="menu.icon"></i>
                    {{ menu.title }}
                </div>
            </div>

            <!-- 主要内容区 -->
            <div class="main-content">
                <div class="header">
                    <div class="header-title">{{ getCurrentMenuTitle }}</div>
                </div>
                <div class="content-area">
                    <component :is="getCurrentComponent"></component>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        import ConfigComponent from './config.js';
        import EnterpriseBind from './enterprise-bind.js';

        const { createApp, ref, computed } = Vue;

        const app = createApp({
            components: {
                'config-component': ConfigComponent,
                'enterprise-bind-component': EnterpriseBind
            },
            setup() {
                const activeMenu = ref('enterprise');

                // 菜单配置
                const menus = [
                    {
                        key: 'enterprise',
                        title: '企业绑定管理',
                        icon: 'el-icon-office-building',
                        component: 'enterprise-bind-component'
                    },
                    {
                        key: 'config',
                        title: '配置管理',
                        icon: 'el-icon-setting',
                        component: 'config-component'
                    }
                ];

                // 计算当前菜单标题
                const getCurrentMenuTitle = computed(() => {
                    const currentMenu = menus.find(menu => menu.key === activeMenu.value);
                    return currentMenu ? currentMenu.title : '';
                });

                // 计算当前组件
                const getCurrentComponent = computed(() => {
                    const currentMenu = menus.find(menu => menu.key === activeMenu.value);
                    return currentMenu ? currentMenu.component : null;
                });

                return {
                    activeMenu,
                    menus,
                    getCurrentMenuTitle,
                    getCurrentComponent
                };
            }
        });

        app.use(ElementPlus);
        app.mount('#app');
    </script>
</body>

</html>