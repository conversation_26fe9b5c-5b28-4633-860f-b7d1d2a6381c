<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>配置管理</title>
    <!-- 引入通用样式 -->
    <link rel="stylesheet" href="common.css">
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@mdi/font@5.9.55/css/materialdesignicons.min.css">
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <!-- 引入通用脚本 -->
    <script src="common.js"></script>
</head>

<body>
    <div id="app">
        <div class="app-container">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="sidebar-title">
                    飞书网关管理系统
                </div>
                <div id="enterprise-menu-item" class="menu-item">
                    <i class="mdi mdi-office-building"></i>
                    企业绑定管理
                </div>
                <div id="config-menu-item" class="menu-item active">
                    <i class="mdi mdi-cog"></i>
                    配置管理
                </div>
            </div>

            <!-- 主要内容区 -->
            <div class="main-content">
                <div class="header">
                    <div class="header-title">配置管理</div>
                </div>
                <div class="content-area">
                    <!-- 页面头部 -->
                    <div class="page-header">
                        <h1 class="page-title">
                            <i class="mdi mdi-cog"></i>
                            配置管理
                        </h1>
                    </div>

                    <div class="config-content">
                        <!-- 搜索表单 -->
                        <div class="search-form">
                            <el-form :inline="true" :model="searchForm" size="small">
                                <el-form-item label="渠道">
                                    <el-select v-model="searchForm.channel" placeholder="请选择渠道" clearable>
                                        <el-option v-for="item in channelOptions" :key="item.value" :label="item.label"
                                            :value="item.value">
                                            <i :class="'mdi ' + item.icon" style="margin-right: 8px;"></i>
                                            {{ item.label }}
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="数据中心ID">
                                    <el-input v-model="searchForm.dcId" placeholder="请输入数据中心ID" clearable></el-input>
                                </el-form-item>
                                <el-form-item label="配置类型">
                                    <el-select v-model="searchForm.type" placeholder="请选择配置类型" clearable>
                                        <el-option v-for="item in typeOptions" :key="item.value" :label="item.label"
                                            :value="item.value">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item>
                                    <el-button type="primary" @click="handleSearch" icon="el-icon-search">查询</el-button>
                                    <el-button @click="handleReset" icon="el-icon-refresh">重置</el-button>
                                </el-form-item>
                            </el-form>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="operation-btns">
                            <el-button type="primary" @click="handleAdd" icon="el-icon-plus"
                                size="small">新增配置</el-button>
                        </div>

                        <!-- 数据表格 -->
                        <el-table :data="tableData" border style="width: 100%" v-loading="loading">
                            <el-table-column prop="id" label="ID" width="100" show-overflow-tooltip></el-table-column>
                            <el-table-column prop="channel" label="渠道" width="120">
                                <template slot-scope="scope">
                                    <el-tag :type="getChannelTagType(scope.row.channel)">
                                        <i :class="getChannelIcon(scope.row.channel)"></i>
                                        {{ getChannelLabel(scope.row.channel) }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="dcId" label="数据中心ID" width="120"></el-table-column>
                            <el-table-column prop="fsEa" label="纷享企业EA" width="120"></el-table-column>
                            <el-table-column prop="outEa" label="外部企业EA" width="120"></el-table-column>
                            <el-table-column prop="appId" label="应用ID" width="120"></el-table-column>
                            <el-table-column prop="type" label="配置类型" width="180">
                                <template slot-scope="scope">
                                    <el-tooltip :content="getTypeLabel(scope.row.type)" placement="top">
                                        <el-tag size="medium">{{ scope.row.type }}</el-tag>
                                    </el-tooltip>
                                </template>
                            </el-table-column>
                            <el-table-column prop="configInfo" label="配置信息">
                                <template slot-scope="scope">
                                    <div class="json-viewer">
                                        <pre>{{ formatJson(scope.row.configInfo) }}</pre>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column prop="creator" label="创建人" width="120"
                                show-overflow-tooltip></el-table-column>
                            <el-table-column prop="createTime" label="创建时间" width="160"></el-table-column>
                            <el-table-column prop="updater" label="更新人" width="120"
                                show-overflow-tooltip></el-table-column>
                            <el-table-column prop="updateTime" label="更新时间" width="160"></el-table-column>
                            <el-table-column label="操作" width="150" fixed="right">
                                <template slot-scope="scope">
                                    <div class="table-operations">
                                        <el-button size="mini" type="primary" @click="handleEdit(scope.row)"
                                            icon="el-icon-edit">编辑</el-button>
                                        <el-button size="mini" type="danger" @click="handleDelete(scope.row)"
                                            icon="el-icon-delete">删除</el-button>
                                    </div>
                                </template>
                            </el-table-column>
                        </el-table>

                        <!-- 分页 -->
                        <div style="margin-top: 20px; text-align: right;">
                            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                                :current-page="currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="pageSize"
                                layout="total, sizes, prev, pager, next, jumper" :total="total">
                            </el-pagination>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 编辑对话框 -->
        <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="60%" :close-on-click-modal="false">
            <el-form :model="form" :rules="rules" ref="form" label-width="120px" size="small">
                <el-form-item label="渠道" prop="channel">
                    <el-select v-model="form.channel" placeholder="请选择渠道" style="width: 100%">
                        <el-option v-for="item in channelOptions" :key="item.value" :label="item.label"
                            :value="item.value">
                            <i :class="'mdi ' + item.icon" style="margin-right: 8px;"></i>
                            {{ item.label }}
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="数据中心ID" prop="dcId">
                    <el-input v-model="form.dcId"></el-input>
                </el-form-item>
                <el-form-item label="纷享企业EA" prop="fsEa">
                    <el-input v-model="form.fsEa"></el-input>
                </el-form-item>
                <el-form-item label="外部企业EA" prop="outEa">
                    <el-input v-model="form.outEa"></el-input>
                </el-form-item>
                <el-form-item label="应用ID" prop="appId">
                    <el-input v-model="form.appId"></el-input>
                </el-form-item>
                <el-form-item label="配置类型" prop="type">
                    <el-select v-model="form.type" placeholder="请选择配置类型" style="width: 100%">
                        <el-option v-for="item in typeOptions" :key="item.value" :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="配置信息" prop="configInfo">
                    <el-input type="textarea" v-model="form.configInfo" :rows="10" class="config-info-editor"
                        placeholder="请输入JSON格式的配置信息"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false" size="small">取消</el-button>
                <el-button type="primary" @click="handleSave" size="small" :loading="saveLoading">确定</el-button>
            </div>
        </el-dialog>
    </div>

    <script>
        new Vue({
            el: '#app',
            data: {
                // 搜索表单
                searchForm: {
                    channel: '',
                    dcId: '',
                    type: ''
                },
                // 表格数据
                tableData: [],
                loading: false,
                // 分页
                currentPage: 1,
                pageSize: 10,
                total: 0,
                // 对话框
                dialogVisible: false,
                dialogTitle: '',
                saveLoading: false,
                // 表单数据
                form: {
                    id: '',
                    channel: '',
                    dcId: '',
                    fsEa: '',
                    outEa: '',
                    appId: '',
                    type: '',
                    configInfo: ''
                },
                // 表单验证规则
                rules: {
                    channel: [{ required: true, message: '请选择渠道', trigger: 'change' }],
                    dcId: [{ required: true, message: '请输入数据中心ID', trigger: 'blur' }],
                    type: [{ required: true, message: '请选择配置类型', trigger: 'change' }],
                    configInfo: [
                        { required: true, message: '请输入配置信息', trigger: 'blur' },
                        { validator: this.validateJson, trigger: 'blur' }
                    ]
                },
                // 渠道选项
                channelOptions: [
                    { value: 'feishu', label: '飞书', icon: 'mdi-feather', color: '#00A870' },
                    { value: 'dingding', label: '钉钉', icon: 'mdi-bell-ring', color: '#1890FF' },
                    { value: 'qywx', label: '企业微信', icon: 'mdi-wechat', color: '#07C160' }
                ],
                // 配置类型选项
                typeOptions: [
                    { value: 'EMPLOYEE_UNIQUE_IDENTITY', label: '自动绑定，用于匹配CRM人员' },
                    { value: 'SETTING_BIND_RULES', label: '账号绑定设置' },
                    { value: 'OUTER_SYSTEM_OBJECT_FIELDS', label: '外部系统字段描述' },
                    { value: 'DEPARTMENT_OBJECT_LAYOUT_MAPPING', label: '部门对象布局映射' },
                    { value: 'CONTACTS_USER_DEFINED_MOBILE_KEY', label: '通讯录用户自定义手机号码字段key' },
                    { value: 'CONVERSATION_ARCHIVE_CONFIG', label: '会话存档配置' },
                    { value: 'INTERFACE_CALL_SERVICE_AUTH', label: '接口调用服务权限' },
                    { value: 'DEPARTMENT_OBJECT_FIELDS_MAPPING', label: '部门字段ID映射' },
                    { value: 'ROUTE_ERPDSS_NORMAL_LOGIN_ENTERPRISE_ACCOUNTS', label: '路由统一基座登录页' }
                ]
            },
            created() {
                this.loadData();
            },
            methods: {
                // 加载数据
                loadData() {
                    this.loading = true;
                    axios.get('/admin/config/list', {
                        params: {
                            ...this.searchForm,
                            page: this.currentPage,
                            size: this.pageSize
                        }
                    }).then(response => {
                        this.loading = false;
                        if (response.data.success) {
                            this.tableData = response.data.data;
                            this.total = response.data.total;
                        } else {
                            this.$message.error(response.data.message);
                        }
                    }).catch(error => {
                        this.loading = false;
                        this.$message.error('加载数据失败');
                    });
                },
                // 搜索
                handleSearch() {
                    this.currentPage = 1;
                    this.loadData();
                },
                // 重置
                handleReset() {
                    this.searchForm = {
                        channel: '',
                        dcId: '',
                        type: ''
                    };
                    this.handleSearch();
                },
                // 新增
                handleAdd() {
                    this.dialogTitle = '新增配置';
                    this.form = {
                        id: '',
                        channel: '',
                        dcId: '',
                        fsEa: '',
                        outEa: '',
                        appId: '',
                        type: '',
                        configInfo: ''
                    };
                    this.dialogVisible = true;
                    this.$nextTick(() => {
                        this.$refs.form && this.$refs.form.clearValidate();
                    });
                },
                // 编辑
                handleEdit(row) {
                    this.dialogTitle = '编辑配置';
                    // 使用深拷贝避免直接修改原始数据
                    this.form = JSON.parse(JSON.stringify(row));
                    this.dialogVisible = true;
                    this.$nextTick(() => {
                        this.$refs.form && this.$refs.form.clearValidate();
                    });
                },
                // 删除
                handleDelete(row) {
                    this.$confirm('确认删除该配置?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        axios.delete('/admin/config/delete/' + row.id).then(response => {
                            if (response.data.success) {
                                this.$message.success('删除成功');
                                this.loadData();
                            } else {
                                this.$message.error(response.data.message);
                            }
                        }).catch(error => {
                            this.$message.error('删除失败');
                        });
                    }).catch(() => { });
                },
                // 保存
                handleSave() {
                    this.$refs.form.validate((valid) => {
                        if (valid) {
                            // 创建一个表单副本
                            const formData = JSON.parse(JSON.stringify(this.form));

                            // 检查configInfo是否为对象类型，如果是则转换为JSON字符串
                            if (typeof formData.configInfo === 'object') {
                                formData.configInfo = JSON.stringify(formData.configInfo);
                            }

                            this.saveLoading = true;
                            axios.post('/admin/config/save', formData).then(response => {
                                this.saveLoading = false;
                                if (response.data.success) {
                                    this.$message.success('保存成功');
                                    this.dialogVisible = false;
                                    this.loadData();
                                } else {
                                    this.$message.error(response.data.message);
                                }
                            }).catch(error => {
                                this.saveLoading = false;
                                this.$message.error('保存失败');
                            });
                        }
                    });
                },
                // 分页大小改变
                handleSizeChange(val) {
                    this.pageSize = val;
                    this.loadData();
                },
                // 当前页改变
                handleCurrentChange(val) {
                    this.currentPage = val;
                    this.loadData();
                },
                // 格式化JSON
                formatJson(json) {
                    try {
                        return JSON.stringify(JSON.parse(json), null, 2);
                    } catch (e) {
                        return json;
                    }
                },
                // 验证JSON格式
                validateJson(rule, value, callback) {
                    if (value) {
                        try {
                            JSON.parse(value);
                            callback();
                        } catch (e) {
                            callback(new Error('请输入有效的JSON格式'));
                        }
                    } else {
                        callback();
                    }
                },
                // 获取渠道标签类型
                getChannelTagType(channel) {
                    const map = {
                        feishu: 'success',
                        dingding: 'primary',
                        qywx: ''
                    };
                    return map[channel] || 'info';
                },
                // 获取渠道图标
                getChannelIcon(channel) {
                    const map = {
                        feishu: 'mdi mdi-feather',
                        dingding: 'mdi mdi-bell-ring',
                        qywx: 'mdi mdi-wechat'
                    };
                    return map[channel] || '';
                },
                // 获取渠道标签
                getChannelLabel(channel) {
                    const option = this.channelOptions.find(item => item.value === channel);
                    return option ? option.label : channel;
                },
                // 获取类型标签
                getTypeLabel(type) {
                    const option = this.typeOptions.find(item => item.value === type);
                    return option ? option.label : type;
                }
            }
        });
    </script>
</body>

</html>