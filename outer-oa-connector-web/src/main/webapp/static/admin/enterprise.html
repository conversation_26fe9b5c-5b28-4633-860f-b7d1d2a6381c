<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>企业绑定管理</title>
    <!-- 引入通用样式 -->
    <link rel="stylesheet" href="common.css">
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@mdi/font@5.9.55/css/materialdesignicons.min.css">
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <!-- 引入通用脚本 -->
    <script src="common.js"></script>
</head>

<body>
    <div id="app">
        <div class="app-container">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="sidebar-title">
                    飞书网关管理系统
                </div>
                <div id="enterprise-menu-item" class="menu-item active">
                    <i class="mdi mdi-office-building"></i>
                    企业绑定管理
                </div>
                <div id="config-menu-item" class="menu-item">
                    <i class="mdi mdi-cog"></i>
                    配置管理
                </div>
            </div>

            <!-- 主要内容区 -->
            <div class="main-content">
                <div class="header">
                    <div class="header-title">企业绑定管理</div>
                </div>
                <div class="content-area">
                    <div class="container">
                        <!-- 搜索表单 -->
                        <div class="search-form">
                            <el-form :inline="true" :model="searchForm" ref="searchForm">
                                <el-form-item label="企业ID">
                                    <el-input v-model="searchForm.fsEa" placeholder="请输入企业ID"></el-input>
                                </el-form-item>
                                <el-form-item label="外部企业ID">
                                    <el-input v-model="searchForm.outerEa" placeholder="请输入外部企业ID"></el-input>
                                </el-form-item>
                                <el-form-item>
                                    <el-button type="primary" @click="handleSearch">查询</el-button>
                                    <el-button @click="resetForm('searchForm')">重置</el-button>
                                </el-form-item>
                            </el-form>
                        </div>

                        <!-- 操作栏 -->
                        <div class="operation-bar">
                            <el-button type="primary" @click="handleAdd">新增绑定</el-button>
                        </div>

                        <!-- 数据表格 -->
                        <el-table :data="tableData" border style="width: 100%">
                            <el-table-column prop="id" label="ID" width="80"></el-table-column>
                            <el-table-column prop="fsEa" label="企业ID" width="120"></el-table-column>
                            <el-table-column prop="outerEa" label="外部企业ID" width="120"></el-table-column>
                            <el-table-column prop="channel" label="渠道" width="100"></el-table-column>
                            <el-table-column prop="createTime" label="创建时间" width="180"></el-table-column>
                            <el-table-column prop="updateTime" label="更新时间" width="180"></el-table-column>
                            <el-table-column label="操作" width="150">
                                <template slot-scope="scope">
                                    <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
                                    <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
                                </template>
                            </el-table-column>
                        </el-table>

                        <!-- 分页 -->
                        <div class="pagination" style="margin-top: 20px; text-align: right;">
                            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                                :current-page="currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="pageSize"
                                layout="total, sizes, prev, pager, next, jumper" :total="total">
                            </el-pagination>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 新增/编辑对话框 -->
        <el-dialog :title="dialogTitle" :visible.sync="dialogVisible">
            <el-form :model="form" :rules="rules" ref="form" label-width="120px">
                <el-form-item label="企业ID" prop="fsEa">
                    <el-input v-model="form.fsEa"></el-input>
                </el-form-item>
                <el-form-item label="外部企业ID" prop="outerEa">
                    <el-input v-model="form.outerEa"></el-input>
                </el-form-item>
                <el-form-item label="渠道" prop="channel">
                    <el-input v-model="form.channel"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="submitForm('form')">确 定</el-button>
            </div>
        </el-dialog>
    </div>

    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    searchForm: {
                        fsEa: '',
                        outerEa: ''
                    },
                    tableData: [],
                    currentPage: 1,
                    pageSize: 10,
                    total: 0,
                    dialogVisible: false,
                    dialogTitle: '新增绑定',
                    form: {
                        id: null,
                        fsEa: '',
                        outerEa: '',
                        channel: ''
                    },
                    rules: {
                        fsEa: [
                            { required: true, message: '请输入企业ID', trigger: 'blur' }
                        ],
                        outerEa: [
                            { required: true, message: '请输入外部企业ID', trigger: 'blur' }
                        ],
                        channel: [
                            { required: true, message: '请输入渠道', trigger: 'blur' }
                        ]
                    }
                }
            },
            created() {
                this.fetchData();
            },
            methods: {
                fetchData() {
                    // 获取列表数据
                    axios.get('/admin/enterprise/list', {
                        params: {
                            page: this.currentPage,
                            size: this.pageSize,
                            fsEa: this.searchForm.fsEa,
                            outerEa: this.searchForm.outerEa
                        }
                    }).then(response => {
                        if (response.data.success) {
                            this.tableData = response.data.data.list;
                            this.total = response.data.data.total;
                        } else {
                            this.$message.error(response.data.message);
                        }
                    }).catch(error => {
                        console.error('获取数据失败:', error);
                        this.$message.error('获取数据失败');
                    });
                },
                handleSearch() {
                    this.currentPage = 1;
                    this.fetchData();
                },
                resetForm(formName) {
                    this.$refs[formName].resetFields();
                    this.handleSearch();
                },
                handleSizeChange(val) {
                    this.pageSize = val;
                    this.fetchData();
                },
                handleCurrentChange(val) {
                    this.currentPage = val;
                    this.fetchData();
                },
                handleAdd() {
                    this.dialogTitle = '新增绑定';
                    this.form = {
                        id: null,
                        fsEa: '',
                        outerEa: '',
                        channel: ''
                    };
                    this.dialogVisible = true;
                },
                handleEdit(row) {
                    this.dialogTitle = '编辑绑定';
                    this.form = { ...row };
                    this.dialogVisible = true;
                },
                handleDelete(row) {
                    this.$confirm('确认删除该绑定关系?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        axios.delete(`/admin/enterprise/${row.id}`).then(response => {
                            if (response.data.success) {
                                this.$message.success('删除成功');
                                this.fetchData();
                            } else {
                                this.$message.error(response.data.message);
                            }
                        }).catch(error => {
                            console.error('删除失败:', error);
                            this.$message.error('删除失败');
                        });
                    }).catch(() => { });
                },
                submitForm(formName) {
                    this.$refs[formName].validate((valid) => {
                        if (valid) {
                            const method = this.form.id ? 'put' : 'post';
                            const url = this.form.id ? `/admin/enterprise/${this.form.id}` : '/admin/enterprise';

                            axios[method](url, this.form).then(response => {
                                if (response.data.success) {
                                    this.$message.success(this.form.id ? '更新成功' : '添加成功');
                                    this.dialogVisible = false;
                                    this.fetchData();
                                } else {
                                    this.$message.error(response.data.message);
                                }
                            }).catch(error => {
                                console.error('操作失败:', error);
                                this.$message.error('操作失败');
                            });
                        }
                    });
                }
            }
        });
    </script>
</body>

</html>