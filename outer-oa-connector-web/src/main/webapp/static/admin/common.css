/* 飞书网关管理系统通用样式 */

/* 基础样式 */
:root {
    --primary-color: #1890ff;
    --menu-bg: #001529;
    --menu-text: #fff;
    --header-height: 64px;
}

body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
    background-color: #f5f7fa;
    color: #333;
}

.app-container {
    display: flex;
    min-height: 100vh;
}

/* 侧边栏样式 - 关键宽度设置为256px */
.sidebar {
    width: 256px;
    min-width: 256px;
    background-color: #001529;
    color: #fff;
    transition: all 0.3s;
    box-shadow: 2px 0 8px 0 rgba(29, 35, 41, .05);
    z-index: 1000;
    flex-shrink: 0;
}

.sidebar-title {
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    font-weight: 600;
    color: #fff;
    border-bottom: 1px solid rgba(255, 255, 255, .1);
    background-color: #001529;
}

.menu-item {
    padding: 16px 24px;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    font-size: 14px;
    color: rgba(255, 255, 255, .65);
    text-align: left;
    height: auto;
    line-height: 1.4;
    word-break: break-all;
}

.menu-item:hover {
    color: #fff;
    background-color: #1890ff;
}

.menu-item.active {
    color: #fff;
    background-color: #1890ff;
}

.menu-item i {
    font-size: 18px;
    margin-right: 12px;
}

/* 主内容区样式 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #f0f2f5;
    overflow-x: auto;
}

.header {
    height: 64px;
    background-color: #fff;
    box-shadow: 0 1px 4px rgba(0, 21, 41, .08);
    display: flex;
    align-items: center;
    padding: 0 24px;
    position: relative;
    z-index: 999;
}

.header-title {
    font-size: 20px;
    color: #1f1f1f;
    font-weight: 500;
}

.content-area {
    flex: 1;
    margin: 24px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, .03);
    padding: 24px;
    overflow-y: auto;
}

/* 表单和表格样式 */
.search-form {
    margin-bottom: 20px;
    padding: 18px;
    background: #f9fafc;
    border-radius: 4px;
}

.operation-bar,
.operation-btns {
    margin-bottom: 20px;
}

/* Element UI 组件美化 */
.el-button--primary {
    background-color: #1890ff;
}

.el-table {
    border-radius: 4px;
    margin-top: 20px;
}

.el-pagination {
    margin-top: 16px;
    justify-content: flex-end;
}

.el-form-item {
    margin-bottom: 16px;
}

/* JSON格式化 */
.json-viewer {
    max-height: 300px;
    overflow-y: auto;
    background: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
}

.config-info-editor {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
}