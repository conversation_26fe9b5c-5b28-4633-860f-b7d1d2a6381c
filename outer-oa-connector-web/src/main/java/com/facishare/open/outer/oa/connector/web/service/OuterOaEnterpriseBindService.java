package com.facishare.open.outer.oa.connector.web.service;



import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.web.result.PageResult;

import java.util.List;

public interface OuterOaEnterpriseBindService {
    
    /**
     * 分页查询企业绑定列表
     *
     * @param page 页码
     * @param size 每页大小
     * @param fsEa 企业ID
     * @param outerEa 外部企业ID
     * @param bindStatus 绑定状态
     * @return 分页结果
     */
    PageResult<OuterOaEnterpriseBindEntity> queryList(Integer page, Integer size, String fsEa, String outerEa, BindStatusEnum bindStatus);

    /**
     * 根据ID查询企业绑定
     *
     * @param id ID
     * @return 企业绑定实体
     */
    OuterOaEnterpriseBindEntity getById(Long id);

    /**
     * 保存企业绑定（新增或更新）
     *
     * @param entity 企业绑定实体
     * @return 保存后的实体
     */
    OuterOaEnterpriseBindEntity save(OuterOaEnterpriseBindEntity entity);

    /**
     * 删除企业绑定
     *
     * @param id ID
     */
    void delete(Long id);
} 