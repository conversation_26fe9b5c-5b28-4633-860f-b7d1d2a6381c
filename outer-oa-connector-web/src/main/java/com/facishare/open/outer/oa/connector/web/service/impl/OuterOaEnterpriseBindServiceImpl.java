package com.facishare.open.outer.oa.connector.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaEnterpriseBindMapper;
import com.facishare.open.outer.oa.connector.web.result.PageResult;
import com.facishare.open.outer.oa.connector.web.service.OuterOaEnterpriseBindService;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.fxiaoke.api.IdGenerator;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class OuterOaEnterpriseBindServiceImpl implements OuterOaEnterpriseBindService {

    @Autowired
    private OuterOaEnterpriseBindMapper outerOaEnterpriseBindMapper;

    @Override
    public PageResult<OuterOaEnterpriseBindEntity> queryList(Integer page, Integer size, String fsEa, String outerEa, BindStatusEnum bindStatus) {
        // 参数校验
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }

        try {
            // 使用LambdaQueryWrapper构建查询条件
            LambdaQueryWrapper<OuterOaEnterpriseBindEntity> wrapper = new LambdaQueryWrapper<>();
            if (StringUtils.isNotEmpty(fsEa)) {
                wrapper.eq(OuterOaEnterpriseBindEntity::getFsEa, fsEa);
            }
            if (StringUtils.isNotEmpty(outerEa)) {
                wrapper.eq(OuterOaEnterpriseBindEntity::getOutEa, outerEa);
            }
            if (bindStatus != null) {
                wrapper.eq(OuterOaEnterpriseBindEntity::getBindStatus, bindStatus);
            }
            
            // 查询总数
            Long total = outerOaEnterpriseBindMapper.selectCount(wrapper);
            
            // 设置分页
            wrapper.last(String.format("LIMIT %d OFFSET %d", size, (page - 1) * size));
            
            // 查询数据
            List<OuterOaEnterpriseBindEntity> list = outerOaEnterpriseBindMapper.selectList(wrapper);
            
            // 创建分页结果对象并设置属性
            PageResult<OuterOaEnterpriseBindEntity> pageResult = PageResult.newSuccess(list, page, size, total);
            
            
            // 返回分页结果
            return pageResult;
        } catch (Exception e) {
            log.error("查询企业绑定列表失败", e);
            throw new RuntimeException("查询企业绑定列表失败", e);
        }
    }

    @Override
    public OuterOaEnterpriseBindEntity getById(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("id不能为空");
        }
        return outerOaEnterpriseBindMapper.selectById(id);
    }

    @Override
    public OuterOaEnterpriseBindEntity save(OuterOaEnterpriseBindEntity entity) {
        if (entity == null) {
            throw new IllegalArgumentException("实体不能为空");
        }
        
        // 设置必要字段
        long now = System.currentTimeMillis();
        if (entity.getCreateTime() == null) {
            entity.setCreateTime(now);
        }
        entity.setUpdateTime(now);
        
        // 如果是新增
        if (StringUtils.isEmpty(entity.getId())) {
            entity.setId(IdGenerator.get());
            List<OuterOaEnterpriseBindEntity> list = Lists.newArrayList(entity);
            outerOaEnterpriseBindMapper.batchUpsert(list);
        } else {
            // 如果是更新
            List<OuterOaEnterpriseBindEntity> list = Lists.newArrayList(entity);
            outerOaEnterpriseBindMapper.batchUpsertById(list);
        }
        return entity;
    }

    @Override
    public void delete(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("id不能为空");
        }
        OuterOaEnterpriseBindEntity entity = new OuterOaEnterpriseBindEntity();
        entity.setId(String.valueOf(id));
        outerOaEnterpriseBindMapper.deleteById(entity);
    }
} 