package com.facishare.open.outer.oa.connector.web.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaConfigInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaConfigInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaConfigInfoParams;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaConfigInfoTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.result.ResultCodeEnum;
import com.facishare.open.outer.oa.connector.web.model.OuterOaConfigInfo;
import com.facishare.open.outer.oa.connector.web.model.PageResult;
import com.facishare.open.outer.oa.connector.web.model.Result;
import com.facishare.open.outer.oa.connector.web.service.OuterOaConfigInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 外部OA配置信息服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
//IgnoreI18nFile
public class OuterOaConfigInfoServiceImpl implements OuterOaConfigInfoService {

    @Autowired
    private OuterOaConfigInfoManager outerOaConfigInfoManager;

    @Override
    public Result<PageResult<OuterOaConfigInfo>> queryConfigList(String channel, String dcId, String type, int page, int size) {
        try {
            // 参数校验
            if (page <= 0) {
                return Result.newError(ResultCodeEnum.PARAMETER_ERROR,"页码必须大于0");
            }
            if (size <= 0 || size > 100) {
                return Result.newError(ResultCodeEnum.PARAMETER_ERROR,"每页大小必须在1-100之间");
            }

            // 构建查询参数
            OuterOaConfigInfoParams params = new OuterOaConfigInfoParams();
            if(StringUtils.isNotBlank(channel)){
                params.setChannel(ChannelEnum.valueOf(channel));
            }
            if(StringUtils.isNotBlank(dcId)){
                params.setDcId(dcId);
            }
            if(StringUtils.isNotBlank(type)){
                params.setType(OuterOaConfigInfoTypeEnum.valueOf(type));
            }

            // 调用Manager查询配置列表（带分页）
            IPage<OuterOaConfigInfoEntity> pageResult = outerOaConfigInfoManager.getEntitiesPage(params, new Page<>(page, size));
            
            // 转换为OuterOaConfigInfo对象
            List<OuterOaConfigInfo> configList = pageResult.getRecords().stream()
                    .map(entity -> {
                        OuterOaConfigInfo config = new OuterOaConfigInfo();
                        BeanUtils.copyProperties(entity, config);
                        // 转换时间戳为Date
                        if (entity.getCreateTime() != null) {
                            config.setCreateTime(new Date(entity.getCreateTime()));
                        }
                        if (entity.getUpdateTime() != null) {
                            config.setUpdateTime(new Date(entity.getUpdateTime()));
                        }
                        return config;
                    })
                    .collect(Collectors.toList());
            
            return Result.newSuccess(PageResult.of(configList, pageResult.getTotal()));
        } catch (DataAccessException e) {
            log.error("数据库查询配置列表失败, channel={}, dcId={}, type={}, page={}, size={}", channel, dcId, type, page, size, e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR,"数据库查询失败，请稍后重试");
        } catch (Exception e) {
            log.error("查询配置列表发生未知错误, channel={}, dcId={}, type={}, page={}, size={}", channel, dcId, type, page, size, e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR,"系统异常，请联系管理员");
        }
    }

    @Override
    public Result<Void> saveConfig(OuterOaConfigInfo config, String operator) {
        try {
            // 参数校验
            Result<Void> validateResult = validateConfig(config);
            if (!validateResult.isSuccess()) {
                return validateResult;
            }

            // 转换为Entity对象
            OuterOaConfigInfoEntity entity = new OuterOaConfigInfoEntity();
            BeanUtils.copyProperties(config, entity);

            // 设置操作人和时间
            long nowTime = System.currentTimeMillis();
            
            // 判断是新增还是更新
            boolean isNew = StringUtils.isEmpty(config.getId());
            
            if (isNew) {
                // 新增
                entity.setCreateTime(nowTime);
            } else {
                // 更新 - 检查配置是否存在
                OuterOaConfigInfoParams params = OuterOaConfigInfoParams.builder()
                        .id(String.valueOf(config.getId()))
                        .build();
                List<OuterOaConfigInfoEntity> existEntities = outerOaConfigInfoManager.getEntities(params);
                if (existEntities.isEmpty()) {
                    return Result.newError(ResultCodeEnum.PARAMETER_ERROR,"待更新的配置信息不存在");
                }
            }
            
            // 设置更新时间
            entity.setUpdateTime(nowTime);


            // 调用Manager保存配置
            if (isNew) {
                // 检查是否已存在相同配置
                if (isConfigExists(config, null)) {
                    return Result.newError(ResultCodeEnum.PARAMETER_ERROR,"已存在相同渠道、数据中心和配置类型的配置");
                }
                outerOaConfigInfoManager.insert(entity);
            } else {
                // 检查是否与其他配置冲突（排除自身）
                if (isConfigExists(config, config.getId())) {
                    return Result.newError(ResultCodeEnum.PARAMETER_ERROR,"已存在相同渠道、数据中心和配置类型的配置");
                }
                outerOaConfigInfoManager.updateById(entity);
            }

            return Result.newSuccess();
        } catch (DuplicateKeyException e) {
            log.error("保存配置信息失败，存在重复配置, config={}, operator={}", config, operator, e);
            return Result.newError(ResultCodeEnum.PARAMETER_ERROR,"已存在相同配置信息");
        } catch (DataAccessException e) {
            log.error("数据库保存配置信息失败, config={}, operator={}", config, operator, e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR,"数据库操作失败，请稍后重试");
        } catch (Exception e) {
            log.error("保存配置信息发生未知错误, config={}, operator={}", config, operator, e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR,"系统异常，请联系管理员");
        }
    }

    @Override
    public Result<Void> deleteConfig(Long id, String operator) {
        try {
            if (id == null) {
                return Result.newError(ResultCodeEnum.PARAMETER_ERROR,"配置ID不能为空");
            }

            // 检查配置是否存在
            OuterOaConfigInfoParams params = OuterOaConfigInfoParams.builder()
                    .id(String.valueOf(id))
                    .build();
            List<OuterOaConfigInfoEntity> entities = outerOaConfigInfoManager.getEntities(params);
            if (entities.isEmpty()) {
                return Result.newError(ResultCodeEnum.PARAMETER_ERROR, "待删除的配置信息不存在");
            }

            // 调用Manager删除配置
            OuterOaConfigInfoEntity entity = new OuterOaConfigInfoEntity();
            entity.setId(String.valueOf(id));
            outerOaConfigInfoManager.deleteById(entity);

            return Result.newSuccess();
        } catch (DataAccessException e) {
            log.error("数据库删除配置信息失败, id={}, operator={}", id, operator, e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR, "数据库操作失败，请稍后重试");
        } catch (Exception e) {
            log.error("删除配置信息发生未知错误, id={}, operator={}", id, operator, e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR, "系统异常，请联系管理员");
        }
    }

    @Override
    public Result<OuterOaConfigInfo> getConfigById(Long id) {
        try {
            if (id == null) {
                return Result.newError(ResultCodeEnum.PARAMETER_ERROR, "配置ID不能为空");
            }

            // 构建查询参数
            OuterOaConfigInfoParams params = OuterOaConfigInfoParams.builder()
                    .id(String.valueOf(id))
                    .build();

            // 调用Manager查询配置
            List<OuterOaConfigInfoEntity> entities = outerOaConfigInfoManager.getEntities(params);
            if (entities.isEmpty()) {
                return Result.newError(ResultCodeEnum.PARAMETER_ERROR, "配置信息不存在");
            }

            // 转换为OuterOaConfigInfo对象
            OuterOaConfigInfo config = new OuterOaConfigInfo();
            BeanUtils.copyProperties(entities.get(0), config);

            return Result.newSuccess(config);
        } catch (DataAccessException e) {
            log.error("数据库查询配置信息失败, id={}", id, e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR, "数据库查询失败，请稍后重试");
        } catch (Exception e) {
            log.error("查询配置信息发生未知错误, id={}", id, e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR, "系统异常，请联系管理员");
        }
    }

    /**
     * 验证配置信息
     *
     * @param config 配置信息
     * @return 验证结果
     */
    private Result<Void> validateConfig(OuterOaConfigInfo config) {
        if (config == null) {
            return Result.newError(ResultCodeEnum.PARAMETER_ERROR, "配置信息不能为空");
        }
        if (ObjectUtils.isEmpty(config.getChannel())) {
            return Result.newError(ResultCodeEnum.PARAMETER_ERROR, "渠道不能为空");
        }

        if (StringUtils.isBlank(config.getDcId())) {
            return Result.newError(ResultCodeEnum.PARAMETER_ERROR, "数据中心ID不能为空");
        }
        if (ObjectUtils.isEmpty(config.getType())) {
            return Result.newError(ResultCodeEnum.PARAMETER_ERROR, "配置类型不能为空");
        }

        if (StringUtils.isBlank(config.getConfigInfo())) {
            return Result.newError(ResultCodeEnum.PARAMETER_ERROR, "配置信息不能为空");
        }
        try {
            // 验证configInfo是否为有效的JSON
            com.alibaba.fastjson.JSON.parse(config.getConfigInfo());
        } catch (Exception e) {
            return Result.newError(ResultCodeEnum.PARAMETER_ERROR, "配置信息必须是有效的JSON格式");
        }
        return Result.newSuccess();
    }

    /**
     * 检查是否存在相同配置
     *
     * @param config 配置信息
     * @param excludeId 排除的配置ID（用于更新时排除自身）
     * @return 是否存在
     */
    private boolean isConfigExists(OuterOaConfigInfo config, String excludeId) {
        OuterOaConfigInfoParams params = new OuterOaConfigInfoParams();
        if (ObjectUtils.isNotEmpty(config.getChannel())) {
            params.setChannel(config.getChannel());
        }
        if (StringUtils.isNotBlank(config.getDcId())) {
            params.setDcId(config.getDcId());
        }
        if (ObjectUtils.isNotEmpty(config.getType())) {
            params.setType(config.getType());
        }
        if (ObjectUtils.isNotEmpty(config.getId())) {
            params.setId(config.getId());
        }
        
        List<OuterOaConfigInfoEntity> existConfigs = outerOaConfigInfoManager.getEntities(params);
        
        // 如果没有排除ID，直接判断是否有结果
        if (StringUtils.isEmpty(excludeId)) {
            return !existConfigs.isEmpty();
        }
        
        // 如果有排除ID，排除该ID对应的配置后判断
        return existConfigs.stream()
                .anyMatch(entity -> !entity.getId().equals(excludeId));
    }

} 