package com.facishare.open.outer.oa.connector.web.result;

import com.facishare.open.outer.oa.connector.common.api.result.Result;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 分页结果类
 *
 * @param <T> 数据类型
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PageResult<T> extends Result<List<T>> {
    /**
     * 当前页码
     */
    private Integer page;

    /**
     * 每页大小
     */
    private Integer pageSize;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 创建成功的分页结果
     *
     * @param list      数据列表
     * @param page      当前页码
     * @param pageSize  每页大小
     * @param total     总记录数
     * @param <T>       数据类型
     * @return 分页结果
     */
    public static <T> PageResult<T> newSuccess(List<T> list, Integer page, Integer pageSize, Long total) {
        PageResult<T> result = new PageResult<>();
        result.setData(list);
        result.setPage(page);
        result.setPageSize(pageSize);
        result.setTotal(total);
        return result;
    }
} 