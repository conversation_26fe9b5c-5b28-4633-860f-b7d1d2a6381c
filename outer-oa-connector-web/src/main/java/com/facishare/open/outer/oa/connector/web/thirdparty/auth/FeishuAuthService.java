package com.facishare.open.outer.oa.connector.web.thirdparty.auth;

import com.facishare.dubbo.plugin.annotation.RestService;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.web.model.FeishuJsapiSignature;
import org.springframework.web.bind.annotation.RequestParam;

@RestService("erpdss/API/v1/rest/inner/feishu/internal")
public interface FeishuAuthService {

    Result<FeishuJsapiSignature> getJsApiSignature(@RequestParam("appId") String appId, @RequestParam("url") String url, @RequestParam("fsEa") String fsEa, @RequestParam("outEa") String outEa);
}
