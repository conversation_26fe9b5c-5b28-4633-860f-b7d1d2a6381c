package com.facishare.open.outer.oa.connector.web.utils;

import com.facishare.uc.api.model.usertoken.User;

/**
 * 用户上下文工具类
 */
public class UserContextHolder {
    private static final ThreadLocal<User> userContext = new ThreadLocal<>();

    /**
     * 设置用户信息
     *
     * @param user 用户信息
     */
    public static void setUser(User user) {
        userContext.set(user);
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    public static User getUser() {
        return userContext.get();
    }

    /**
     * 清除用户信息
     */
    public static void clear() {
        userContext.remove();
    }
} 