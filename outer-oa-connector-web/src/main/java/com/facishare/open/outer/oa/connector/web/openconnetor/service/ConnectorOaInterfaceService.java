package com.facishare.open.outer.oa.connector.web.openconnetor.service;



import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.outer.oa.connector.common.api.info.SystemParams;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.web.model.GetConnectorIntroArg;
import com.facishare.open.outer.oa.connector.web.model.OaConnectorAuthType;
import com.facishare.open.outer.oa.connector.web.model.admin.*;

import com.facishare.open.outer.oa.connector.web.openconnetor.model.*;
import com.fxiaoke.message.extrnal.platform.model.arg.*;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2025/5/14
 */
public interface ConnectorOaInterfaceService extends ConnectorService {

    Result<List<OaConnectorAuthType>> getConnectorAuthTypeList(String tenantId, String dataCenterId, String connectorApiName);

    Result<String> getOAuth2AuthUrl(String tenantId, String dataCenterId, String connectorApiName, SystemParams arg);

    Result<SystemParams> processUserInputSystemParams(String tenantId, String dataCenterId, String connectorApiName, SystemParams arg);
    /**
     * 获取连接器信息，包含连接参数表单模式
     */
    Result<ConnectorIntro> getOaConnectorIntro(String tenantId, String dataCenterId, String connectorApiName, GetConnectorIntroArg arg);

    /**
     * 无身份登录地址
     */
    Result<OuterRedirectMsg> getDoAuthUrlMsg(String tenantId, String dataCenterId, String connectorApiName, DoAuthArg arg);

    /**
     * 带身份登录
     */
    Result<OuterRedirectMsg> getLoginAuthUrlMsg(String tenantId, String dataCenterId, String connectorApiName, OriginRequestArg arg);

    /**
     * 处理回调事件
     */
    Result<OuterCallBackEventMsg> dealWithCallBackEvent(String tenantId, String dataCenterId, String connectorApiName, OriginRequestArg arg);
    /**
     * 单条查询
     */
    Result<StdData> queryOaMasterById(String tenantId, String dataCenterId, String connectorApiName, IdArg arg);
    /**
     *批量查询
     */
    Result<StdListData> queryOaMasterBatch(String tenantId, String dataCenterId, String connectorApiName, TimeFilterArg arg);


}
