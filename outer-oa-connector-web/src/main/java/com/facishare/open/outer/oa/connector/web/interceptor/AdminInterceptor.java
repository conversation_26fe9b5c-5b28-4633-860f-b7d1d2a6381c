package com.facishare.open.outer.oa.connector.web.interceptor;

import com.alibaba.fastjson.JSON;
import com.facishare.asm.api.auth.AuthXC;
import com.facishare.asm.api.enums.ValidateStatus;
import com.facishare.asm.api.model.CookieToAuth;
import com.facishare.asm.api.service.ActiveSessionAuthorizeService;
import com.facishare.open.outer.oa.connector.web.config.ConfigCenter;
import com.facishare.open.outer.oa.connector.web.exception.NoPermissionException;
import com.facishare.open.outer.oa.connector.web.utils.UserContextHolder;
import com.facishare.uc.api.model.usertoken.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 管理员权限验证拦截器
 */
@Slf4j
@Component
public class AdminInterceptor extends HandlerInterceptorAdapter {

    @Autowired
    private ActiveSessionAuthorizeService activeSessionAuthorizeService;

    /**
     * 默认企业EA
     */
    private static final String DEFAULT_EA = "83952";
    
    /**
     * 默认企业ID
     */
    private static final Integer DEFAULT_EI = 83952;
    
    /**
     * 默认员工ID
     */
    private static final Integer DEFAULT_EMPLOYEE_ID = 1000;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 清除之前的用户上下文，避免数据污染
        UserContextHolder.clear();
        
        try {
            // 测试环境判断，跳过权限验证，使用默认用户
            if (isTestEnvironment()) {
                log.info("AdminInterceptor.preHandle in test environment, use default user");
                User defaultUser = createDefaultUser();
                UserContextHolder.setUser(defaultUser);
                return true;
            }
            
            // 从cookie中获取用户身份
            User user = tryGetUserFromCookie(request);
            if (user == null) {
                return responseNoPermission(response, "admin check fail");
            }
            
            String ea = user.getEnterpriseAccount();
            Integer employeeId = user.getEmployeeId();
            
            // 系统内部用户(ei=1)可以直接通过验证
            if (user.getEnterpriseId() != null && user.getEnterpriseId() == 1) {
                log.info("AdminInterceptor.preHandle enterprise id is 1, skip admin check");
                UserContextHolder.setUser(user);
                return true;
            }
            
            // 验证是否是配置中心管理员
            if (!ConfigCenter.isConfigCenterAdmin(ea, employeeId)) {
                log.warn("AdminInterceptor.preHandle failed, not admin, ea={}, employeeId={}", ea, employeeId);
                return responseNoPermission(response, "非配置中心管理员，无权访问");
            }
            
            // 设置用户上下文
            UserContextHolder.setUser(user);
            return true;
        } catch (Exception e) {
            log.error("AdminInterceptor.preHandle exception", e);
            return responseNoPermission(response, "系统异常，请稍后重试");
        }
    }
    
    /**
     * 判断当前是否为测试环境
     * 
     * @return true:测试环境 false:非测试环境
     */
    private boolean isTestEnvironment() {
        return Objects.equals(System.getProperty("process.profile"), "fstest") 
                || Objects.equals(System.getProperty("spring.profiles.active"), "fstest");
    }
    
    /**
     * 创建默认用户信息
     * 
     * @return 默认用户信息
     */
    private User createDefaultUser() {
        return new User();
    }
    
    /**
     * 从Cookie获取用户信息
     * 
     * @param request HTTP请求
     * @return 用户信息，如果获取失败则返回null
     */
    private User tryGetUserFromCookie(HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();
        if (Objects.isNull(cookies)) {
            log.warn("AdminInterceptor.tryGetUserFromCookie failed, cookies is null");
            return null;
        }

        String fsAuthXCCookie = null;
        for (Cookie cookie : cookies) {
            if ("FSAuthXC".equalsIgnoreCase(cookie.getName())) {
                fsAuthXCCookie = cookie.getValue();
                break;
            }
        }

        if (StringUtils.isEmpty(fsAuthXCCookie)) {
            log.warn("AdminInterceptor.tryGetUserFromCookie failed, FSAuthXC cookie not found");
            return null;
        }

        try {
            CookieToAuth.Argument argument = new CookieToAuth.Argument();
            argument.setCookie(fsAuthXCCookie);
            CookieToAuth.Result<AuthXC> result = activeSessionAuthorizeService.cookieToAuthXC(argument);
            
            if (!result.isSucceed() || result.getValidateStatus() != ValidateStatus.NORMAL || Objects.isNull(result.getBody())) {
                log.warn("AdminInterceptor.tryGetUserFromCookie failed, result={}", JSON.toJSONString(result));
                return null;
            }

            AuthXC authXC = result.getBody();
            log.info("AdminInterceptor.tryGetUserFromCookie success, authXC={}", JSON.toJSONString(authXC));
            return new User(authXC.getEnterpriseId(), authXC.getEnterpriseAccount(), 
                    authXC.getEmployeeId(), authXC.getDeviceId());
        } catch (Exception e) {
            log.error("AdminInterceptor.tryGetUserFromCookie exception", e);
            return null;
        }
    }
    
    /**
     * 返回无权限响应
     * 
     * @param response HTTP响应
     * @param message 错误信息
     * @return false，表示拦截请求
     */
    private boolean responseNoPermission(HttpServletResponse response, String message) {
        response.setContentType(MediaType.APPLICATION_JSON_UTF8_VALUE);
        Map<String, Object> result = new HashMap<>();
        result.put("code", 403);
        result.put("message", message);
        
        try {
            PrintWriter writer = response.getWriter();
            writer.write(JSON.toJSONString(result));
            writer.flush();
            writer.close();
        } catch (IOException e) {
            log.error("AdminInterceptor.responseNoPermission exception", e);
        }
        
        return false;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        UserContextHolder.clear();
    }
} 