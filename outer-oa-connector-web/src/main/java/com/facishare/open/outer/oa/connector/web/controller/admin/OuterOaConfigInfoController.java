package com.facishare.open.outer.oa.connector.web.controller.admin;

import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaConfigInfoTypeEnum;
import com.facishare.open.outer.oa.connector.web.model.OuterOaConfigInfo;
import com.facishare.open.outer.oa.connector.web.model.PageResult;
import com.facishare.open.outer.oa.connector.web.model.Result;
import com.facishare.open.outer.oa.connector.web.service.OuterOaConfigInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 外部OA配置信息控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("//erpdss/oabase/admin/config")
public class OuterOaConfigInfoController {

    @Autowired
    private OuterOaConfigInfoService configInfoService;

    /**
     * 查询配置列表
     *
     * @param channel 渠道
     * @param dcId 数据中心ID
     * @param type 配置类型
     * @param page 页码
     * @param size 每页大小
     * @return 配置列表
     */
    @GetMapping("/list")
    public Result<PageResult<OuterOaConfigInfo>> list(
            @RequestParam(required = false) String channel,
            @RequestParam(required = false) String dcId,
            @RequestParam(required = false) String type,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        return configInfoService.queryConfigList(channel, dcId, type, page, size);
    }

    /**
     * 保存配置信息
     *
     * @param config 配置信息
     * @param request HTTP请求
     * @return 保存结果
     */
    @PostMapping("/save")
    public Result<Void> save(@RequestBody OuterOaConfigInfo config, HttpServletRequest request) {
        // TODO: 从请求中获取当前操作人
        String operator = "admin";
        return configInfoService.saveConfig(config, operator);
    }

    /**
     * 添加配置信息（与/save接口功能相同，用于处理/add请求）
     *
     * @param config 配置信息
     * @param request HTTP请求
     * @return 保存结果
     */
    @PostMapping("/add")
    public Result<Void> add(@RequestBody OuterOaConfigInfo config, HttpServletRequest request) {
        log.info("接收到添加配置请求: {}", config);
        return save(config, request);
    }

    /**
     * 更新配置信息
     *
     * @param config 配置信息
     * @param request HTTP请求
     * @return 更新结果
     */
    @PostMapping("/update")
    public Result<Void> update(@RequestBody OuterOaConfigInfo config, HttpServletRequest request) {
        log.info("接收到更新配置请求: {}", config);
        if (config.getId() == null) {
            log.error("更新配置失败：配置ID不能为空");
            return Result.newError(com.facishare.open.outer.oa.connector.common.api.result.ResultCodeEnum.PARAMETER_ERROR, "配置ID不能为空");
        }
        // TODO: 从请求中获取当前操作人
        String operator = "admin";
        // 设置更新人
        config.setUpdater(operator);
        return configInfoService.saveConfig(config, operator);
    }

    /**
     * 删除配置信息
     *
     * @param id 配置ID
     * @param request HTTP请求
     * @return 删除结果
     */
    @DeleteMapping("/delete/{id}")
    public Result<Void> delete(@PathVariable Long id, HttpServletRequest request) {
        // TODO: 从请求中获取当前操作人
        String operator = "admin";
        return configInfoService.deleteConfig(id, operator);
    }

    /**
     * 删除配置信息 (POST方式，支持表单提交)
     *
     * @param param 包含ID的参数
     * @param request HTTP请求
     * @return 删除结果
     */
    @PostMapping("/delete")
    public Result<Void> deleteByPost(@RequestBody Map<String, Object> param, HttpServletRequest request) {
        log.info("接收到删除配置请求(POST): {}", param);
        if (param == null || !param.containsKey("id")) {
            return Result.newError(com.facishare.open.outer.oa.connector.common.api.result.ResultCodeEnum.PARAMETER_ERROR, "配置ID不能为空");
        }
        
        Long id;
        try {
            id = Long.valueOf(String.valueOf(param.get("id")));
        } catch (NumberFormatException e) {
            log.error("配置ID格式错误: {}", param.get("id"));
            return Result.newError(com.facishare.open.outer.oa.connector.common.api.result.ResultCodeEnum.PARAMETER_ERROR, "配置ID格式错误");
        }
        
        // TODO: 从请求中获取当前操作人
        String operator = "admin";
        return configInfoService.deleteConfig(id, operator);
    }

    /**
     * 获取配置信息
     *
     * @param id 配置ID
     * @return 配置信息
     */
    @GetMapping("/get/{id}")
    public Result<OuterOaConfigInfo> get(@PathVariable Long id) {
        return configInfoService.getConfigById(id);
    }

    /**
     * 获取渠道枚举值
     */
    @GetMapping("/enums/channel")
    public List<Map<String, String>> getChannelEnums() {
        List<Map<String, String>> collect = Arrays.stream(ChannelEnum.values())
                .map(channel -> {
                    Map<String, String> map = new HashMap<>(2);
                    map.put("value", channel.name());
                    map.put("label", channel.getEnumName());
                    return map;
                })
                .collect(Collectors.toList());
        //需要补充一个all渠道
        collect.add(new HashMap<String, String>() {{
            put("value", "*");
            put("label", "所有渠道");
        }});
        return collect;
    }

    /**
     * 获取配置类型枚举值
     */
    @GetMapping("/enums/type")
    public List<Map<String, String>> getConfigTypeEnums() {
        return Arrays.stream(OuterOaConfigInfoTypeEnum.values())
                .map(type -> {
                    Map<String, String> map = new HashMap<>(2);
                    map.put("value", type.name());
                    map.put("label", type.getDesc());
                    return map;
                })
                .collect(Collectors.toList());
    }
} 