package com.facishare.open.outer.oa.connector.web.exception;

import com.facishare.open.outer.oa.connector.common.api.result.ResultCodeEnum;
import com.facishare.open.outer.oa.connector.web.model.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 全局异常处理器
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理无权限异常
     */
    @ExceptionHandler(NoPermissionException.class)
    public Result<Void> handleNoPermissionException(NoPermissionException e) {
        log.warn("notPermission: {}", e.getMessage());
        return Result.newError(ResultCodeEnum.NOT_PERMISSION_LOGIN_MANAGER, e.getMessage());
    }

    /**
     * 处理其他异常
     */
    @ExceptionHandler(Exception.class)
    public Result<Void> handleException(Exception e) {
        log.error("系统异常", e);
        return Result.newError(ResultCodeEnum.PARAMETER_ERROR, "系统异常，请稍后重试");
    }
} 