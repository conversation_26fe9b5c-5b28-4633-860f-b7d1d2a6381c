package com.facishare.open.outer.oa.connector.web.service;

import com.facishare.open.outer.oa.connector.web.model.OuterOaConfigInfo;
import com.facishare.open.outer.oa.connector.web.model.PageResult;
import com.facishare.open.outer.oa.connector.web.model.Result;

import java.util.List;

/**
 * 外部OA配置信息服务接口
 *
 * <AUTHOR>
 */
public interface OuterOaConfigInfoService {

    /**
     * 分页查询配置列表
     *
     * @param channel 渠道
     * @param dcId 数据中心ID
     * @param type 配置类型
     * @param page 页码
     * @param size 每页大小
     * @return 配置列表和总数
     */
    Result<PageResult<OuterOaConfigInfo>> queryConfigList(String channel, String dcId, String type, int page, int size);

    /**
     * 保存配置信息
     *
     * @param config 配置信息
     * @param operator 操作人
     * @return 保存结果
     */
    Result<Void> saveConfig(OuterOaConfigInfo config, String operator);

    /**
     * 删除配置信息
     *
     * @param id 配置ID
     * @param operator 操作人
     * @return 删除结果
     */
    Result<Void> deleteConfig(Long id, String operator);

    /**
     * 根据ID查询配置信息
     *
     * @param id 配置ID
     * @return 配置信息
     */
    Result<OuterOaConfigInfo> getConfigById(Long id);
} 