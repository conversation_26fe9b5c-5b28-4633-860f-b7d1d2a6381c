package com.facishare.open.outer.oa.connector.web.model;

import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaConfigInfoTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 外部OA配置信息实体类
 *
 * <AUTHOR>
 */
@Data
public class OuterOaConfigInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private String id;

    /**
     * 渠道（feishu-飞书, dingding-钉钉, qywx-企业微信）
     */
    private ChannelEnum channel;

    /**
     * 数据中心ID
     */
    private String dcId;

    /**
     * 纷享企业EA
     */
    private String fsEa;

    /**
     * 外部企业EA
     */
    private String outEa;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 配置类型
     * @see OuterOaConfigInfoTypeEnum
     */
    private OuterOaConfigInfoTypeEnum type;

    /**
     * 配置信息(JSON格式)
     */
    private String configInfo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新人
     */
    private String updater;
} 