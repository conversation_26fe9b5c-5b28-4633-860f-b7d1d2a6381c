package com.facishare.open.qywx.web.mq.listener;

import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import com.facishare.open.qywx.accountbind.result.Result;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountinner.service.ContactBindInnerService;
import com.facishare.open.qywx.web.config.ConfigCenter;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.web.template.inner.listener.QyweixinFsOrganizationChangedListenerTemplate;
import com.facishare.organization.api.event.OrganizationChangedListener;
import com.facishare.organization.api.event.organizationChangeEvent.EmployeeChangeEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.UUID;


/**
 * 1、目前只用于账号同步绑定
 * <AUTHOR>
 * @Version 1.0
 */
@Component
@Slf4j
public class UserRoleEventListener extends OrganizationChangedListener {

    @Autowired
    private QyweixinFsOrganizationChangedListenerTemplate qyweixinFsOrganizationChangedListenerTemplate;

    public UserRoleEventListener() {
        super("fs-open-qywx-app-config");
    }

    @Override
    protected void onEmployeeChanged(EmployeeChangeEvent event) {
        String traceId = TraceUtils.getTraceId();
        if(StringUtils.isEmpty(traceId)) {
            TraceUtils.initTraceId(UUID.randomUUID()+"_"+event.getEnterpriseAccount());
        }
        log.info("UserRoleEventListener.onEmployeeChanged.event={}.", event);

        MethodContext context = MethodContext.newInstance(event);
        qyweixinFsOrganizationChangedListenerTemplate.onEmployeeChanged(context);
    }
}
