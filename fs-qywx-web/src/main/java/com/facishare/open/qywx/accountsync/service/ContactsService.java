package com.facishare.open.qywx.accountsync.service;

import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.order.contacts.proxy.api.model.contacts.OutEmpModel;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaAppInfoTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.RemoveEmployeeEventType;
import com.facishare.open.outer.oa.connector.common.api.info.EnterpriseManualAccountSyncInfo;
import com.facishare.open.qywx.accountsync.model.ContactScopeModel;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.accountsync.utils.xml.ContactsXml;
import com.facishare.open.qywx.accountsync.utils.xml.QyweixinMsgBaseXml;
import com.facishare.open.qywx.accountsync.utils.xml.TagChangeEventXml;
import com.facishare.open.qywx.web.model.qyweixin.QyweixinGetPermenantCodeRsp;

import java.io.Serializable;
import java.util.List;

/**
 * 纷享通讯录服务
 *
 * <AUTHOR>
 * @date ********
 */
public interface ContactsService extends Serializable {
    String qywxRootDepId = "0";
    /**
     * 初始化纷享通讯录
     *
     * @param enterpriseBindEntity
     * @return
     */
    Result<Void> initContactsAsync(OuterOaEnterpriseBindEntity enterpriseBindEntity);

    /**
     * 应用可见范围就更事件处理逻辑
     * 对应企微的 change_auth 事件
     *
     * @param appId
     * @param outEa
     * @return
     */
    Result<Void> onAvailableRangeChanged(String appId,String outEa);

    /**
     * 通讯录变更事件
     * 对应企微的 change_contact 事件
     *
     * @return
     */
    Result<Void> onChangeContacts(ContactsXml contactsXml, String appId);

    /**
     * 获取CRM应用可见范围内的人员和部门信息
     * @return
     */
    Result<ContactScopeModel> getContactScopeData(String appId, String outEa);

    /**
     * 批量添加纷享员工
     *
     * @param outUserIdList
     * @return
     */
    Result<Void> addUserList(OuterOaEnterpriseBindEntity enterpriseBindEntity, List<String> outUserIdList);

    /**
     * 批量停用纷享员工
     *
     * @param outUserIdList
     * @return
     */
    Result<Void> stopUserList(OuterOaEnterpriseBindEntity enterpriseBindEntity, List<String> outUserIdList, RemoveEmployeeEventType removeEmployeeEventType);

    /**
     * 批量新增纷享部门
     *
     * @param outDepIdList
     * @return
     */
    Result<Void> addDepList(OuterOaEnterpriseBindEntity enterpriseBindEntity, List<String> outDepIdList);

    /**
     * 批量更新纷享部门，只支持部门名称的更新
     *
     * @param outDepIdList
     * @return
     */
    Result<Void> updateDepList(OuterOaEnterpriseBindEntity enterpriseBindEntity, List<String> outDepIdList);

    /**
     * 批量停用纷享部门
     *
     * @param outDepIdList 需要停用的企微部门ID列表
     * @return
     */
    Result<Void> stopDepList(OuterOaEnterpriseBindEntity enterpriseBindEntity, List<String> outDepIdList);

//    /**
//     * 重置管理员角色
//     * @return
//     */
//    Result<Void> resetAdminRole(String appId, String outEa);

    /**
     * 新增员工，对应add_user事件
     * @param outEmpModel
     * @return
     */
    Result<Void> addUser(OuterOaEnterpriseBindEntity enterpriseBindEntity, OutEmpModel outEmpModel);

    /**
     * 更新员工，对应update_user事件
     * 目前只支持用户名称的更新，用户ID的更新，用户部门的更新
     * @param outUserId
     * @return
     */
    Result<Void> updateUser(OuterOaEnterpriseBindEntity enterpriseBindEntity, String outUserId, String newOutUserId);

    /**
     * 停用员工，对应于delete_user事件
     * @param outUserId
     * @return
     */
    Result<Void> stopUser(OuterOaEnterpriseBindEntity enterpriseBindEntity, String outUserId, RemoveEmployeeEventType removeEmployeeEventType);

    /**
     * 添加纷享员工
     *
     * @param outUserId
     * @return
     */
    Result<Void> addUser(OuterOaEnterpriseBindEntity enterpriseBindEntity, String outUserId);

    Result<Void> repVisibleRangeChange(String outEa, String appId, TagChangeEventXml tagChangeEventXml, String infoType, String plainMsg);

    Result<Void> syncOuterContacts(String outEa, String appId, String eventType, String plainMsg);

    Result<Void> syncOuterContactsInfo(String outEa, String appId);

    Result<Void> syncOuterAppInitInfo(String outEa, String appId, String fsEa, QyweixinGetPermenantCodeRsp corpAuthInfo);
}
