package com.facishare.open.qywx.web.manager;

import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.QyweixinBusinessInfoBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.QyweixinBusinessInfoBindManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEnterpriseBindParams;
import com.facishare.open.oa.base.dbproxy.pg.params.QyweixinBusinessInfoBindParams;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.common.api.result.ResultCodeEnum;
import com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService;
import com.facishare.open.qywx.accountsync.constant.Constant;
import com.facishare.open.qywx.web.model.qyweixin.QywxActiveCodeDetail;
import com.facishare.open.qywx.web.model.qyweixin.QywxPermissionOrderAccountDetail;
import com.facishare.open.qywx.web.model.qyweixin.QywxPermissionOrderDetail;
import com.facishare.open.qywx.web.model.qyweixin.QywxPermissionOrderList;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 *  处理企微系统处理事件
 */
@Slf4j
@Component
public class QywxPermissionManager {

    @Resource
    private QYWeixinManager qyWeixinManager;
    @Autowired
    private CrmObjManager crmObjManager;
    @Autowired
    private QyweixinAccountBindInnerService qyweixinAccountBindInnerService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Resource
    private OANewBaseManager oaNewBaseManager;
    @Resource
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private QyweixinBusinessInfoBindManager qyweixinBusinessInfoBindManager;

    public void handlerOrderPaySuccess(String orderId, String appId){
        try {
            QywxPermissionOrderDetail permitOrder = qyWeixinManager.getPermitOrder(orderId);
            if(permitOrder.isSuccess()&&permitOrder.getOrder().getOrder_status()!=1){
                //1：已支付
                log.info("handler notpaysuccess:{}", JSONObject.toJSONString(permitOrder));
                return;
            }
            String corpId=permitOrder.getOrder().getCorpid();

            List<QywxPermissionOrderAccountDetail.OrderAccountList> permitOrderAccount = qyWeixinManager.getPermitOrderAccount(orderId);
            log.info("qywx permission oder id:{}",permitOrderAccount.size());

            //根据账号查询
            List<List<QywxPermissionOrderAccountDetail.OrderAccountList>> partition = Lists.partition(permitOrderAccount, 100);//企业微信最多1000
            for (List<QywxPermissionOrderAccountDetail.OrderAccountList> accountLists : partition) {
                //根据corpId查询绑定fsea
                List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).outEa(corpId).appId(appId).build());
                //有时候企业是不使用crm应用，类似营销通用户
                if(CollectionUtils.isEmpty(enterpriseBindEntities)){
                    List<QyweixinBusinessInfoBindEntity> businessInfoBindEntities = qyweixinBusinessInfoBindManager.getEntities(QyweixinBusinessInfoBindParams.builder().outEa(corpId).build());
                    if(CollectionUtils.isEmpty(businessInfoBindEntities)){
                        log.info("handler message tenantid notexist:{}",corpId);
                        return;
                    }
                    enterpriseBindEntities = convertMappings(businessInfoBindEntities);
                }
                for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
                    String fsEa = enterpriseBindEntity.getFsEa();
                    int tenantId = eieaConverter.enterpriseAccountToId(fsEa);
                    log.info("handler message tenantid data:{}",fsEa);
//                    if(!ConfigCenter.USE_QYWX_PERMISSION_EALISTS.contains(fsEa)){
//                        log.info("handler message tenantid notexist:{}",fsEa);
//                        return;
//                    }
                    if(permitOrder.getOrder().getOrder_type().equals(2)){
                        //续期账号，没有返回activecode.
                        Set<String> renewUserIds = accountLists.stream().map(QywxPermissionOrderAccountDetail.OrderAccountList::getUserid).collect(Collectors.toSet());
                        List<QywxActiveCodeDetail.ActiveCodeInfo> activeCodeInfoList=Lists.newArrayList();
                        List<String> activeCodes=Lists.newArrayList();
                        QywxActiveCodeDetail activeInfoByUserId=null;
                        //没有批量查询员工，只能先查询数据到列表再批量调用CRM。
                        for (String renewUserId : renewUserIds) {
                            activeInfoByUserId = qyWeixinManager.getActiveInfoByUserId(corpId, renewUserId);
                            activeCodeInfoList.addAll(activeInfoByUserId.getActive_info_list());
                            activeCodes.addAll(activeInfoByUserId.getActive_info_list().stream().filter(item ->ObjectUtils.isNotEmpty(item.getActive_code())).map(QywxActiveCodeDetail.ActiveCodeInfo::getActive_code).collect(Collectors.toList()));
                            if(activeCodeInfoList.size()>=100){
                                activeInfoByUserId.setActive_info_list(activeCodeInfoList);
                                upsertCrmActiveCodes(orderId, permitOrder, tenantId, activeCodes, activeInfoByUserId);
                                activeCodeInfoList.clear();
                                activeCodes.clear();
                            }
                        }
                        if(CollectionUtils.isNotEmpty(activeCodeInfoList)){
                            upsertCrmActiveCodes(orderId, permitOrder, tenantId, activeCodes, activeInfoByUserId);
                        }
                        break;
                    }
                    List<String> activeCodes = accountLists.stream().map(QywxPermissionOrderAccountDetail.OrderAccountList::getActive_code).collect(Collectors.toList());
                    QywxActiveCodeDetail activeInfoByCode = qyWeixinManager.getActiveInfoByCode(permitOrder.getOrder().getCorpid(), activeCodes);
                    log.info("permit order :{}", JSONObject.toJSONString(activeInfoByCode));
                    upsertCrmActiveCodes(orderId, permitOrder, tenantId, activeCodes, activeInfoByCode);
                }

            }
        } catch (Exception e) {
            log.error("system handler paysuccess fail :{}",e.getMessage());
        }

    }

    public Result<Void> handlerOrderPaySuccessEa(String fsEa, String orderId){
        try {
            QywxPermissionOrderDetail permitOrder = qyWeixinManager.getPermitOrder(orderId);
            if(permitOrder.isSuccess()&&permitOrder.getOrder().getOrder_status()!=1){
                //1：已支付
                log.info("handler notpaysuccess:{}", JSONObject.toJSONString(permitOrder));
                return Result.newError(ResultCodeEnum.QYWX_ORDER_NOT_SUPPORT);
            }
            String corpId=permitOrder.getOrder().getCorpid();
            int tenantId = eieaConverter.enterpriseAccountToId(fsEa);
            List<QywxPermissionOrderAccountDetail.OrderAccountList> permitOrderAccount = qyWeixinManager.getPermitOrderAccount(orderId);
            log.info("qywx permission oder id:{}",permitOrderAccount.size());
            List<List<QywxPermissionOrderAccountDetail.OrderAccountList>> partition = Lists.partition(permitOrderAccount, 100);//企业微信最多1000
            for (List<QywxPermissionOrderAccountDetail.OrderAccountList> accountLists : partition) {
                if(permitOrder.getOrder().getOrder_type().equals(2)){
                    //续期账号，没有返回activecode.
                    Set<String> renewUserIds = accountLists.stream().map(QywxPermissionOrderAccountDetail.OrderAccountList::getUserid).collect(Collectors.toSet());
                    List<QywxActiveCodeDetail.ActiveCodeInfo> activeCodeInfoList=Lists.newArrayList();
                    List<String> activeCodes=Lists.newArrayList();
                    QywxActiveCodeDetail activeInfoByUserId=null;
                    //没有批量查询员工，只能先查询数据到列表再批量调用CRM。
                    for (String renewUserId : renewUserIds) {
                        activeInfoByUserId = qyWeixinManager.getActiveInfoByUserId(corpId, renewUserId);
                        activeCodeInfoList.addAll(activeInfoByUserId.getActive_info_list());
                        activeCodes.addAll(activeInfoByUserId.getActive_info_list().stream().filter(item ->ObjectUtils.isNotEmpty(item.getActive_code())).map(QywxActiveCodeDetail.ActiveCodeInfo::getActive_code).collect(Collectors.toList()));
                        if(activeCodeInfoList.size()>=100){
                            activeInfoByUserId.setActive_info_list(activeCodeInfoList);
                            upsertCrmActiveCodes(orderId, permitOrder, tenantId, activeCodes, activeInfoByUserId);
                            activeCodeInfoList.clear();
                            activeCodes.clear();
                        }
                    }
                    if(CollectionUtils.isNotEmpty(activeCodeInfoList)){
                        upsertCrmActiveCodes(orderId, permitOrder, tenantId, activeCodes, activeInfoByUserId);
                    }

                }else{
                    List<String> activeCodes = accountLists.stream().map(QywxPermissionOrderAccountDetail.OrderAccountList::getActive_code).collect(Collectors.toList());
                    QywxActiveCodeDetail activeInfoByCode = qyWeixinManager.getActiveInfoByCode(permitOrder.getOrder().getCorpid(), activeCodes);
                    log.info("permit order :{}", JSONObject.toJSONString(activeInfoByCode));
                    upsertCrmActiveCodes(orderId, permitOrder, tenantId, activeCodes, activeInfoByCode);
                }

            }
            log.info("handler message tenantid data:{}",fsEa);
//                    if(!ConfigCenter.USE_QYWX_PERMISSION_EALISTS.contains(fsEa)){
//                        log.info("handler message tenantid notexist:{}",fsEa);
//                        return;
//                    }

        } catch (Exception e) {
            log.error("system handler paysuccess fail :{}",e.getMessage());
        }
        return  Result.newSuccess();
    }



    private List<OuterOaEnterpriseBindEntity>  convertMappings(List<QyweixinBusinessInfoBindEntity> businessInfoBindEntities){
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = Lists.newArrayList();
        for (QyweixinBusinessInfoBindEntity qyweixinBusinessInfoBindBo : businessInfoBindEntities) {
            OuterOaEnterpriseBindEntity enterpriseBindEntity = new OuterOaEnterpriseBindEntity();
            enterpriseBindEntity.setFsEa(qyweixinBusinessInfoBindBo.getFsEa());
            enterpriseBindEntity.setOutEa(qyweixinBusinessInfoBindBo.getOutEa());
            enterpriseBindEntities.add(enterpriseBindEntity);
        }
        return enterpriseBindEntities;
    }
    private void upsertCrmActiveCodes(String orderId, QywxPermissionOrderDetail permitOrder, int tenantId, List<String> activeCodes, QywxActiveCodeDetail activeInfoByCode) {
        //批量创建
        List<ObjectData> createCrmWechatInterfaceLicenseObjs=Lists.newArrayList();
        List<ObjectData> updateCrmWechatInterfaceLicenseObjs=Lists.newArrayList();
        //批量获取activeInfo的userid.查询关联企业微信员工id
        Map<String, String> crmobjDataMap = crmObjManager.queryByActiveCodes(tenantId, activeCodes, Constant.WechatInterfaceLicenseObj);
        List<String> wechatEmpData = activeInfoByCode.getActive_info_list().stream().map(QywxActiveCodeDetail.ActiveCodeInfo::getUserid).collect(Collectors.toList());
        Map<String, String> weChatEmployeeIds = crmObjManager.queryWeChatEmployeeIds(tenantId, wechatEmpData, Constant.WechatEmployeeObj);
        for (QywxActiveCodeDetail.ActiveCodeInfo activeCodeInfo : activeInfoByCode.getActive_info_list()) {
            ObjectData crmWechatInterfaceLicenseObj=new ObjectData();
            crmWechatInterfaceLicenseObj.put("account_status",activeCodeInfo.getStatus());
            crmWechatInterfaceLicenseObj.put("name",activeCodeInfo.getActive_code());
            crmWechatInterfaceLicenseObj.put("account_type",activeCodeInfo.getType());
            crmWechatInterfaceLicenseObj.put("bind_wechat_userid",activeCodeInfo.getUserid());
            if(activeCodeInfo.getActive_time()!=null){
                //首次激活绑定用户的时间，未激活则不返回该字段
                crmWechatInterfaceLicenseObj.put("active_time",activeCodeInfo.getActive_time()*1000);
            }
            if(activeCodeInfo.getExpire_time()!=null){
                //过期时间。为首次激活绑定的时间加上购买时长。未激活则不返回该字段
                crmWechatInterfaceLicenseObj.put("expire_time",activeCodeInfo.getExpire_time()*1000);
            }
            if(activeCodeInfo.getUserid()!=null){
                crmWechatInterfaceLicenseObj.put("wechat_employee_id",weChatEmployeeIds.get(activeCodeInfo.getUserid()));
            }
            if(activeCodeInfo.getMerge_info()!=null){
                crmWechatInterfaceLicenseObj.put("to_active_code",activeCodeInfo.getMerge_info().getTo_active_code());
                crmWechatInterfaceLicenseObj.put("from_active_code",activeCodeInfo.getMerge_info().getFrom_active_code());
            }
            crmWechatInterfaceLicenseObj.put("pay_time", permitOrder.getOrder().getPay_time()*1000);
            crmWechatInterfaceLicenseObj.put("order_id", orderId);
            crmWechatInterfaceLicenseObj.put("account_duration", permitOrder.calculateDuration());
            crmWechatInterfaceLicenseObj.put("active_expire_time", permitOrder.calculateActiveExpireTime());
            crmWechatInterfaceLicenseObj.put("owner",Lists.newArrayList("-10000"));
            if(crmobjDataMap.get(activeCodeInfo.getActive_code())!=null){
                //更新
                String dataId = crmobjDataMap.get(activeCodeInfo.getActive_code());
                crmWechatInterfaceLicenseObj.put("_id",dataId);
                crmWechatInterfaceLicenseObj.remove("owner");
                updateCrmWechatInterfaceLicenseObjs.add(crmWechatInterfaceLicenseObj);
            }else{
                //新增
                createCrmWechatInterfaceLicenseObjs.add(crmWechatInterfaceLicenseObj);
            }
        }
        if(CollectionUtils.isNotEmpty(createCrmWechatInterfaceLicenseObjs)){
            crmObjManager.batchCreateData(tenantId, createCrmWechatInterfaceLicenseObjs, Constant.WechatInterfaceLicenseObj);
        }else {
            crmObjManager.batchUpdateObject(tenantId, updateCrmWechatInterfaceLicenseObjs,Constant.WechatInterfaceLicenseObj);
        }
    }

    private void commonHandlerEvent(List<String> activeCodes, Integer tenantId, String corpId, List<QywxActiveCodeDetail.ActiveCodeInfo> activeCodeInfos){
        if(ObjectUtils.isEmpty(activeCodes)&&ObjectUtils.isEmpty(activeCodeInfos)){
            return;
        }
        List<ObjectData> createCrmWechatInterfaceLicenseObjs=Lists.newArrayList();
        List<ObjectData> updateCrmWechatInterfaceLicenseObjs=Lists.newArrayList();

        if(CollectionUtils.isNotEmpty(activeCodes)){
            QywxActiveCodeDetail activeInfoByCode = qyWeixinManager.getActiveInfoByCode(corpId, activeCodes);
            activeCodeInfos=activeInfoByCode.getActive_info_list();
        }
        if(CollectionUtils.isNotEmpty(activeCodeInfos)&&CollectionUtils.isEmpty(activeCodes)){
            activeCodes= activeCodeInfos.stream().map(QywxActiveCodeDetail.ActiveCodeInfo::getActive_code).collect(Collectors.toList());
        }
        Map<String, String> crmobjDataMap = crmObjManager.queryByActiveCodes(tenantId, activeCodes, Constant.WechatInterfaceLicenseObj);
        List<String> wechatEmpData = activeCodeInfos.stream().map(QywxActiveCodeDetail.ActiveCodeInfo::getUserid).collect(Collectors.toList());
        Map<String, String> weChatEmployeeIds = crmObjManager.queryWeChatEmployeeIds(tenantId, wechatEmpData, Constant.WechatEmployeeObj);
        for (QywxActiveCodeDetail.ActiveCodeInfo activeCodeInfo : activeCodeInfos) {
            ObjectData crmWechatInterfaceLicenseObj=new ObjectData();
            crmWechatInterfaceLicenseObj.put("account_status",activeCodeInfo.getStatus());
            crmWechatInterfaceLicenseObj.put("name",activeCodeInfo.getActive_code());
            crmWechatInterfaceLicenseObj.put("account_type",activeCodeInfo.getType());
            crmWechatInterfaceLicenseObj.put("bind_wechat_userid",activeCodeInfo.getUserid());
            if(activeCodeInfo.getActive_time()!=null){
                crmWechatInterfaceLicenseObj.put("active_time",activeCodeInfo.getActive_time()*1000);
            }
            if(activeCodeInfo.getExpire_time()!=null){
                crmWechatInterfaceLicenseObj.put("expire_time",activeCodeInfo.getExpire_time()*1000);
            }
            if(activeCodeInfo.getUserid()!=null){
                String objectId = weChatEmployeeIds.get(activeCodeInfo.getUserid());
                crmWechatInterfaceLicenseObj.put("wechat_employee_id",objectId);
            }

            if(activeCodeInfo.getMerge_info()!=null){
                crmWechatInterfaceLicenseObj.put("to_active_code",activeCodeInfo.getMerge_info().getTo_active_code());
                crmWechatInterfaceLicenseObj.put("from_active_code",activeCodeInfo.getMerge_info().getFrom_active_code());
            }

            crmWechatInterfaceLicenseObj.put("owner",Lists.newArrayList("-10000"));
            if(crmobjDataMap.get(activeCodeInfo.getActive_code())!=null){
                //更新
                String dataId = crmobjDataMap.get(activeCodeInfo.getActive_code());
                crmWechatInterfaceLicenseObj.put("_id",dataId);
                crmWechatInterfaceLicenseObj.remove("owner");
                updateCrmWechatInterfaceLicenseObjs.add(crmWechatInterfaceLicenseObj);
            }else{
                //新增
                createCrmWechatInterfaceLicenseObjs.add(crmWechatInterfaceLicenseObj);
            }
        }
        if(CollectionUtils.isNotEmpty(createCrmWechatInterfaceLicenseObjs)){
            crmObjManager.batchCreateData(tenantId,createCrmWechatInterfaceLicenseObjs, Constant.WechatInterfaceLicenseObj);
        }else {
            crmObjManager.batchUpdateObject(tenantId,updateCrmWechatInterfaceLicenseObjs,Constant.WechatInterfaceLicenseObj);
        }
    }

    public void handlerAutoActive(List<String> activeCodes,String corpId, String appId){
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).outEa(corpId).appId(appId).build());
        if(CollectionUtils.isEmpty(enterpriseBindEntities)){
            log.info("handler message tenantid notexist:{}",corpId);
            return;
        }
        //需要判断下有没有其他应用
        if(CollectionUtils.isEmpty(enterpriseBindEntities)){
            List<QyweixinBusinessInfoBindEntity> businessInfoBindEntities = qyweixinBusinessInfoBindManager.getEntities(QyweixinBusinessInfoBindParams.builder().outEa(corpId).build());
            if(CollectionUtils.isEmpty(businessInfoBindEntities)){
                log.info("handler message tenantid notexist:{}",corpId);
                return;
            }
            enterpriseBindEntities = convertMappings(businessInfoBindEntities);
        }
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            String  fsEa = enterpriseBindEntity.getFsEa();
            int tenantId = eieaConverter.enterpriseAccountToId(fsEa);
            log.info("handler message tenantid data:{}",fsEa);
//            if(!ConfigCenter.USE_QYWX_PERMISSION_EALISTS.contains(fsEa)){
//                log.info("handler message tenantid notexist:{}",fsEa);
//                return;
//            }
            //批量创建
            commonHandlerEvent(activeCodes,tenantId,corpId,null);
        }
    }
    //支持指定企业账号列表，存储激活信息
    public void handlerAccountListByCorpId(String corpId, String appId){
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).outEa(corpId).appId(appId).build());
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            String  fsEa = enterpriseBindEntity.getFsEa();
            int tenantId = eieaConverter.enterpriseAccountToId(fsEa);
            //批量创建
            List<QywxPermissionOrderAccountDetail.OrderAccountList> accountLists = qyWeixinManager.getEnterpriseActiveAccount(corpId);
            Set<String> distinctAccounts = accountLists.stream().map(item -> item.getUserid()).collect(Collectors.toSet());
            List<QywxActiveCodeDetail.ActiveCodeInfo> details=Lists.newArrayList();
            List<String> activeCodesList = Lists.newArrayList();
            for (String distinctAccount : distinctAccounts) {
                QywxActiveCodeDetail userAccountList = qyWeixinManager.getActiveInfoByUserId(corpId, distinctAccount);
                activeCodesList.addAll(userAccountList.getActive_info_list().stream().map(item -> item.getActive_code()).collect(Collectors.toList()));
//                userAccountList.getActive_info_list().forEach(item ->item.setStatus(userAccountList.getActive_status()));
//                details.addAll(userAccountList.getActive_info_list());
            }
            List<List<String>> partition = Lists.partition(activeCodesList, 100);
            //保护底层
            for (List<String> activeCodes : partition) {
                commonHandlerEvent(activeCodes,tenantId,corpId,null);
            }

        }
    }

    //支持指定企业，查询时间内的订单
    //企业id。若指定corpid且corpid为服务商测试企业，则返回的订单列表为测试订单列表。否则只返回正式订单列表
    public void handlerBetweenTime(String corpId,Long startTime,Long endTime){
        QywxPermissionOrderList orderList = qyWeixinManager.getPayOrderList(startTime, endTime, corpId);
        if(orderList.isSuccess()){
            for (QywxPermissionOrderList.OrderList list : orderList.getOrder_list()) {
                handlerOrderPaySuccess(list.getOrder_id(), null);
            }
        }
    }
}

