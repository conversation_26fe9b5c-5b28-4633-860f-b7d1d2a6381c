package com.facishare.open.qywx.web.utils.url;

public class UrlUtils {
    public static String buildUrl(String baseUrl, String... params) {
        StringBuilder sb = new StringBuilder(baseUrl);
        boolean first = true;
        for (int i = 0; i < params.length; i += 2) {
            String key = params[i];
            String value = params[i + 1];
            if (value != null) {
                if (first) {
                    sb.append("?");
                    first = false;
                } else {
                    sb.append("&");
                }
                sb.append(key).append("=").append(value);
            }
        }
        return sb.toString();
    }

    public static void main(String[] args) {
        String state = null;
        String userInfo2 = "{}";
        String lang = "zh_CN";
        String from_origin = null;
        System.out.println(buildUrl("/qyweixin/callback/loginAuth",
                "state", state,
                "userInfo", userInfo2,
                "lang", lang,
                "from_origin", from_origin));
    }
}

