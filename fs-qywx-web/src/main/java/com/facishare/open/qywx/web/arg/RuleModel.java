package com.facishare.open.qywx.web.arg;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 规则参数实体类
 */
@Data
public class RuleModel implements Serializable {
    /**
     * 规则id
     */
    private String rule_id;
    /**
     * 规则名称
     */
    private String name;

    /**
     * 关键词配置
     */
    private Keyword keyword;

    /**
     * 语义配置
     */
    private Semantics semantics;

    /**
     * 适用范围配置
     */
    private ApplicableRange applicable_range;

    @Data
    public static class Keyword {
        /**
         * 关键词列表
         */
        private List<String> word_list;

        /**
         * 是否大小写敏感
         */
        private Integer is_case_sensitive;

        /**
         * 匹配规则
         */
        private Integer match_rule;

        /**
         * 是否去除消息空格
         */
        private Integer is_trim_msg;
    }

    @Data
    public static class Semantics {
        /**
         * 语义列表
         */
        private List<Integer> semantics_list;
    }

    @Data
    public static class ApplicableRange {
        /**
         * 目标类型配置
         */
        private TargetType target_type;

        /**
         * 部门配置
         */
        private Department department;

        /**
         * 用户配置
         */
        private User user;

        /**
         * 外部联系人配置
         */
        private ExternalContact external_contact;

        /**
         * 会话类型配置
         */
        private ChatType chat_type;

        /**
         * 会话配置
         */
        private Chat chat;

        /**
         * 消息范围
         */
        private Integer msg_scope;

        /**
         * 排除手机号配置
         */
        private ExcludeMobile exclude_mobile;

        /**
         * 排除邮箱配置
         */
        private ExcludeEmail exclude_email;

        /**
         * 排除银行卡配置
         */
        private ExcludeBankCard exclude_bank_card;

        /**
         * 排除关键词配置
         */
        private ExcludeKeyword exclude_keyword;

        /**
         * 会话类型配置
         */
        private SessionType session_type;

        @Data
        public static class TargetType {
            /**
             * 类型列表
             */
            private List<Integer> type_list;
        }

        @Data
        public static class Department {
            /**
             * 部门ID列表
             */
            private List<Integer> id_list;
        }

        @Data
        public static class User {
            /**
             * 用户ID列表
             */
            private List<String> id_list;
        }

        @Data
        public static class ExternalContact {
            /**
             * 外部联系人ID列表
             */
            private List<String> id_list;
        }

        @Data
        public static class ChatType {
            /**
             * 会话类型列表
             */
            private List<Integer> type_list;
        }

        @Data
        public static class Chat {
            /**
             * 会话ID列表
             */
            private List<String> id_list;
        }

        @Data
        public static class ExcludeMobile {
            /**
             * 排除的手机号列表
             */
            private List<String> mobile_list;
        }

        @Data
        public static class ExcludeEmail {
            /**
             * 排除的邮箱列表
             */
            private List<String> email_list;
        }

        @Data
        public static class ExcludeBankCard {
            /**
             * 排除的银行卡列表
             */
            private List<String> bank_card_list;
        }

        @Data
        public static class ExcludeKeyword {
            /**
             * 排除的关键词列表
             */
            private List<String> word_list;
        }

        @Data
        public static class SessionType {
            /**
             * 会话类型列表
             */
            private List<Integer> session_type_list;
        }
    }
}