package com.facishare.open.qywx.web.enums;

import com.facishare.uc.api.model.usertoken.User;
import lombok.extern.slf4j.Slf4j;

/**
 * Created by fengyh on 2018/7/20.
 */

@Slf4j
public enum UserContextSingleton {
    INSTANCE;

    private static final ThreadLocal<User> curUserContext = ThreadLocal.withInitial(() -> {
        log.info(Thread.currentThread().getName() + "start set init value \n" +  "\n");
        return null;
    });

    public User getUserContext() {
        return curUserContext.get();
    }

    public void setUserContext(User user) {
        curUserContext.set(user);
    }

    public void removeUserContext(){
        curUserContext.remove();
    }
}
