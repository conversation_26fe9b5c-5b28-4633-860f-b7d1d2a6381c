package com.facishare.open.qywx.web.arg;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/3/25 11:29
 * @Version 1.0
 */
@Data
public class ActviceCodeArgExecuteTypeArg implements Serializable {

   private String type;
   private QywxActviceCodeArg qywxActviceCodeArg;
   private PullOrderData pullOrderData;
   private QywxTransferUserIdArg qywxTransferUserIdArg;

    @Data
    public static class QywxTransferUserIdArg implements Serializable {
        private String fsEa;

       private List<QywxTransferUserItem> transferList;
    }
    @Data
    public static class QywxTransferUserItem implements Serializable {
        @JSONField(name = "handover_userid")
        private String handoverUserid;
        @JSONField(name = "takeover_userid")
        private String takeoverUserid;

    }
    @Data
    public static class QywxActviceCodeArg implements Serializable {
        private String fsEa;
        private String activeCode;
        private String userId;

    }


    @Data
    public static class PullOrderData implements Serializable {

        private String fsEa;

        private String orderId;

    }


}
