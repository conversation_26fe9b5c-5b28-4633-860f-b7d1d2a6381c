package com.facishare.open.qywx.web.arg;

import com.facishare.common.fsi.ProtoBase;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class HitKeyWordArg extends ProtoBase {

    private String ea;
    private String qywxCropId;
    private HitKeyWordMessage message;

    @Data
    public static class HitKeyWordMessage implements Serializable {
        private String event_type;
        private Long timestamp;
        private HitKeyWord hit_keyword;

    }
    @Data
    public static class HitKeyWord implements Serializable {
        private String token;
    }
}