package com.facishare.open.qywx.web.utils;

import com.alibaba.fastjson.TypeReference;
import com.facishare.open.order.contacts.proxy.api.network.ProxyHttpClient;
import com.facishare.open.qywx.web.arg.HttpArg;
import com.facishare.open.qywx.web.arg.HttpResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
// IgnoreI18nFile
public class HttpProxyUtil {
    @Autowired
    private ProxyHttpClient proxyHttpClient;

    /**
     * 原有的简单HTTP代理请求方法（保持向后兼容）
     */
    public String httpProxyRequest(HttpArg body) {
        if(body.getMethod().equals("GET")) {
            return proxyHttpClient.getUrl(body.getUrl(), body.getHeader());
        }
        return proxyHttpClient.postUrl(body.getUrl(), body.getBody(), body.getHeader());
    }

    /**
     * 完整的HTTP代理请求方法，返回包含响应头的完整响应
     */
    public HttpResponse httpProxyRequestWithHeaders(HttpArg httpArg) {
        HttpResponse httpResponse = new HttpResponse();

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpRequestBase request;

            // 根据HTTP方法创建请求
            if ("GET".equalsIgnoreCase(httpArg.getMethod())) {
                request = new HttpGet(httpArg.getUrl());
            } else if ("POST".equalsIgnoreCase(httpArg.getMethod())) {
                HttpPost postRequest = new HttpPost(httpArg.getUrl());
                if (httpArg.getBody() != null && !httpArg.getBody().isEmpty()) {
                    StringEntity entity = new StringEntity(httpArg.getBody(), StandardCharsets.UTF_8);
                    postRequest.setEntity(entity);
                }
                request = postRequest;
            } else {
                // 其他HTTP方法可以在这里扩展
                throw new UnsupportedOperationException("不支持的HTTP方法: " + httpArg.getMethod());
            }

            // 设置请求头
            if (httpArg.getHeader() != null) {
                for (Map.Entry<String, String> entry : httpArg.getHeader().entrySet()) {
                    request.setHeader(entry.getKey(), entry.getValue());
                }
            }

            // 执行请求
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                // 设置状态码和状态消息
                httpResponse.setStatusCode(response.getStatusLine().getStatusCode());
                httpResponse.setStatusMessage(response.getStatusLine().getReasonPhrase());

                // 提取响应头
                Map<String, String> responseHeaders = new HashMap<>();
                for (Header header : response.getAllHeaders()) {
                    responseHeaders.put(header.getName(), header.getValue());
                }
                httpResponse.setHeaders(responseHeaders);

                // 提取Content-Type
                Header contentTypeHeader = response.getFirstHeader("Content-Type");
                if (contentTypeHeader != null) {
                    String contentType = contentTypeHeader.getValue();
                    httpResponse.setContentType(contentType);

                    // 提取字符编码
                    if (contentType.contains("charset=")) {
                        String charset = contentType.substring(contentType.indexOf("charset=") + 8);
                        if (charset.contains(";")) {
                            charset = charset.substring(0, charset.indexOf(";"));
                        }
                        httpResponse.setCharset(charset.trim());
                    }
                }

                // 提取响应体
                HttpEntity entity = response.getEntity();
                if (entity != null) {
                    String responseBody = EntityUtils.toString(entity, StandardCharsets.UTF_8);
                    httpResponse.setBody(responseBody);
                }

                log.info("HTTP代理请求成功: {} {}, 状态码: {}, Content-Type: {}",
                        httpArg.getMethod(), httpArg.getUrl(),
                        httpResponse.getStatusCode(), httpResponse.getContentType());

            }
        } catch (IOException e) {
            log.error("HTTP代理请求失败: {} {}", httpArg.getMethod(), httpArg.getUrl(), e);
            // 设置错误响应
            httpResponse.setStatusCode(500);
            httpResponse.setStatusMessage("Internal Server Error");
            httpResponse.setBody("{\"error\":\"代理请求失败: " + e.getMessage() + "\"}");
            httpResponse.setContentType("application/json");
        }

        return httpResponse;
    }

    public <T> T httpProxyRequest(HttpArg body, TypeReference<T> typeReference) {
        if(body.getMethod().equals("GET")) {
            return proxyHttpClient.getUrl(body.getUrl(), body.getHeader(), typeReference);
        }
        return proxyHttpClient.postUrl(body.getUrl(), body.getBody(), body.getHeader(), typeReference);
    }
}
