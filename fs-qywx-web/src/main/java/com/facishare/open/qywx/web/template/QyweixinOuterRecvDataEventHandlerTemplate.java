package com.facishare.open.qywx.web.template;

import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.outer.OuterEventHandlerTemplate;
import com.facishare.open.oa.base.dbproxy.utils.SecurityUtil;
import com.facishare.open.qywx.accountsync.constant.EnterpriseWeChatEventTag;
import com.facishare.open.qywx.accountsync.model.EnterpriseWeChatEventProto;
import com.facishare.open.qywx.accountsync.model.qyweixin.AppConfigInfo;
import com.facishare.open.qywx.accountsync.utils.QYWxCryptHelper;
import com.facishare.open.qywx.accountsync.utils.xml.QYWeiXinDataEventBaseXml;
import com.facishare.open.qywx.accountsync.utils.xml.XStreamUtils;
import com.facishare.open.qywx.web.arg.PlainTextDataArg;
import com.facishare.open.qywx.web.eventHandler.ThirdDataEventHandler;
import com.facishare.open.qywx.web.mq.sender.MQSender;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Map;

/**
 * 企微第三方接收企业微信用户从应用session上行的消息。
 * <AUTHOR>
 * @date 2024-11-20
 */

@Slf4j
@Component
public class QyweixinOuterRecvDataEventHandlerTemplate extends OuterEventHandlerTemplate {
    @ReloadableProperty("appMetaInfoStr2")
    private String appMetaInfoCmsStr;

    @Resource
    private ThirdDataEventHandler thirdDataEventHandler;

    @Resource
    private MQSender mqSender;

    Map<String, AppConfigInfo> appMetaInfo = Maps.newHashMap();


    @PostConstruct
    public void initAppMeta() {
        //在cms上的格式配置格式 appMetaInfoStr=source,appid,appsecret   保存的时候key使用source_appid
        ArrayList<AppConfigInfo> appMetaComponent = new Gson().fromJson(appMetaInfoCmsStr, new TypeToken<ArrayList<AppConfigInfo>>() {
        }.getType());
        log.info("QyweixinOuterRecvDataEventHandlerTemplate.initAppMeta, appMetaInfoStr={}, appMetaComponent={} ", appMetaInfoCmsStr, appMetaComponent);
        appMetaComponent.stream().forEach(v -> {
            v.setEncodingAESKeyBase64(Base64.decodeBase64(v.getEncodingAESKey() + "="));
            v.setToken(SecurityUtil.decryptStr(v.getToken()));
            v.setSecret(SecurityUtil.decryptStr(v.getSecret()));
            appMetaInfo.put(v.getAppId(), v);
        });
    }

    @Override
    public void onEventDecode(MethodContext context) {
        log.info("QyweixinOuterRecvDataEventHandlerTemplate.onEventDecode,context={}",context);

        EnterpriseWeChatEventProto eventProto = context.getData();

        //校验
        if (StringUtils.isNotEmpty(eventProto.getEchoStr())) {
            String verified = verifyURL(eventProto.getSignature(), eventProto.getTimestamp(), eventProto.getNonce(), eventProto.getEchoStr(), eventProto.getAppId());
            //这里比较特殊，只是检验地址配置是否正确，返回错误就行
            context.setResult(TemplateResult.newErrorData(verified));
            return;
        }

        //解密
        PlainTextDataArg plainTextDataArg;
        try {
            String plainMsg = thirdDataEventHandler.decryptMsg(eventProto.getSignature(),
                    eventProto.getTimestamp(),
                    eventProto.getNonce(),
                    eventProto.getData(),
                    eventProto.getAppId());
            log.info("QyweixinOuterRecvDataEventHandlerTemplate.onMsgEvent,plainMsg={}", plainMsg);
            plainTextDataArg= PlainTextDataArg.builder().plainMsg(plainMsg).appId(eventProto.getAppId()).build();
        } catch (Exception e) {
            log.info("QyweixinOuterRecvDataEventHandlerTemplate.onMsgEvent,exception={}", e.getMessage(),e);
            context.setResult(TemplateResult.newErrorData("success"));
            return;
        }
        //准备下一步需要的context
        context.setData(plainTextDataArg);
        context.setResult(TemplateResult.newSuccess());
    }

    @Override
    public void onEventFilter(MethodContext context) {
        log.info("QyweixinOuterRecvDataEventHandlerTemplate.onEventFilter,context={}",context);
        String plainMsg = context.getData();
        QYWeiXinDataEventBaseXml baseMsgXml = XStreamUtils.parseXml(plainMsg, QYWeiXinDataEventBaseXml.class);
        log.info("QyweixinOuterRecvDataEventHandlerTemplate.handle,baseMsgXml={}",baseMsgXml);

        String event = baseMsgXml.getEvent();

        if(!ThirdDataEventHandler.supportedDataEvent.contains(event)) {
            log.info("QyweixinOuterRecvDataEventHandlerTemplate.handle,not supported event,event={}",event);
            context.setResult(TemplateResult.newErrorData("success"));
            return;
        }

        context.setResult(TemplateResult.newSuccess());
    }

    @Override
    public void onEventHandle(MethodContext context) {
        log.info("QyweixinOuterRecvDataEventHandlerTemplate.onEventHandle,context={}",context);
        PlainTextDataArg plainTextDataArg=context.getData();
        String plainMsg = plainTextDataArg.getPlainMsg();

        //非紧急事件，发送MQ，等待后面消费
        mqSender.sendEnterpriseWeChatMQ(EnterpriseWeChatEventTag.TAG_ENTERPRISE_WECHAT_DATA_EVENT_4_THIRD,plainMsg,plainTextDataArg.getAppId());

        context.setResult(TemplateResult.newSuccess("success"));
    }

    private String verifyURL(String msgSignature, String timeStamp, String nonce, String echostr, String appID) {
        String result = null;
        try {
            result = QYWxCryptHelper.VerifyURL(appMetaInfo.get(appID).getToken(), appMetaInfo.get(appID).getEncodingAESKeyBase64(), msgSignature, timeStamp, nonce, echostr);
            log.info("QyweixinOuterRecvDataEventHandlerTemplate.verifyURL,result={}", result);
        } catch (Exception e) {
            log.error("QyweixinOuterRecvDataEventHandlerTemplate.verifyURL,exception", e);
        }
        return result;
    }
}
