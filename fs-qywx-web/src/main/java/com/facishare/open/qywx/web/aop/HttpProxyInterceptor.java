package com.facishare.open.qywx.web.aop;

import com.alibaba.dubbo.common.URL;
import com.facishare.asm.api.auth.AuthXC;
import com.facishare.asm.api.model.CookieToAuth;
import com.facishare.asm.api.service.ActiveSessionAuthorizeService;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEnterpriseBindParams;
import com.facishare.open.oa.base.dbproxy.utils.SecurityUtil;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.qywx.web.arg.HttpArg;
import com.facishare.open.qywx.web.arg.HttpResponse;
import com.facishare.open.qywx.web.config.ConfigCenter;
import com.facishare.open.qywx.web.manager.OANewBaseManager;
import com.facishare.open.qywx.web.utils.HttpProxyUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;
import org.springframework.web.util.ContentCachingRequestWrapper;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.facishare.restful.server.FRestJacksonMapperBean.objectMapper;

@Component
@Slf4j
// IgnoreI18nFile
public class HttpProxyInterceptor extends HandlerInterceptorAdapter {
    @Autowired
    private HttpProxyUtil httpProxyUtil;
    @ReloadableProperty("qywx_ip_url")
    private String qywxIpUrl;

    @Resource
    private OANewBaseManager oANewBaseManager;
    @Autowired
    private ActiveSessionAuthorizeService activeSessionAuthorizeService;

    private static final String VER = "V1_";

    @Resource
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        LogUtils.info("HttpProxyInterceptor.preHandle,request={},response={},handler={}", request, response, handler);

        String uri = request.getRequestURI();

        for(String notSupportUri : ConfigCenter.qywxNotSupportUrl) {
            if(StringUtils.containsIgnoreCase(uri, notSupportUri)) {
                LogUtils.info("HttpProxyInterceptor.preHandle,uri={}", uri);
                return true;
            }
        }


        //拼接参数，转到纷享云
        HttpArg arg = new HttpArg();
        String param = request.getQueryString();
        String method = request.getMethod();
        String url = ConfigCenter.qywx_ip_url + uri;
        //其他环境的contextPath为 feishu-web，需要替换为

//        LogUtils.info("HttpProxyInterceptor.preHandle,param={},method={},url={}", param, method, url);
        if(StringUtils.isNotEmpty(param)) {
            url = url +  "?" + param;
        }

        // 获取到请求头
        Enumeration<String> headerNames = request.getHeaderNames();
        LogUtils.info("HttpProxyInterceptor.preHandle,headerNames={}", headerNames);
        Map<String, String> headerMap = new HashMap<>();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            // 过滤掉不应该转发的请求头
            if(shouldSkipRequestHeader(headerName)) {
                continue;
            }
            String header = request.getHeader(headerName);
            headerMap.put(headerName, header);
        }

        arg.setMethod(method);
        arg.setHeader(headerMap);
        arg.setUrl(url);

        boolean isNewBase = false;
        try {
            isNewBase = isNewBase(request, arg, uri);
        } catch (Exception e) {
            LogUtils.info("HttpProxyInterceptor.preHandle,error,arg={}", arg);
            return true;
        }
        log.info("HttpProxyInterceptor.preHandle,isNewBase={}",isNewBase);

        if(!isNewBase && ConfigCenter.MAIN_ENV) {
            try {
                arg.setBody(getParameters(request));

                // 使用新的完整响应转发方法
                HttpResponse httpResponse = httpProxyUtil.httpProxyRequestWithHeaders(arg);
                LogUtils.info("HttpProxyInterceptor.preHandle,httpResponse={},arg={}", httpResponse, arg);

                // 设置响应状态码
                response.setStatus(httpResponse.getStatusCode());

                // 转发响应头（排除一些不应该转发的头）
                if (httpResponse.getHeaders() != null) {
                    for (Map.Entry<String, String> headerEntry : httpResponse.getHeaders().entrySet()) {
                        String headerName = headerEntry.getKey();
                        String headerValue = headerEntry.getValue();

                        // 排除一些不应该转发的响应头
                        if (!shouldSkipResponseHeader(headerName)) {
                            response.setHeader(headerName, headerValue);
                            LogUtils.debug("转发响应头: {} = {}", headerName, headerValue);
                        }
                    }
                }

                // 设置Content-Type（如果后端有返回的话）
                if (httpResponse.getContentType() != null) {
                    response.setContentType(httpResponse.getContentType());
                } else {
                    // 默认设置为UTF-8编码的JSON
                    response.setContentType("application/json;charset=UTF-8");
                }

                // 写入响应体
                if (httpResponse.getBody() != null) {
                    ServletOutputStream outputStream = response.getOutputStream();
                    outputStream.write(httpResponse.getBody().getBytes(StandardCharsets.UTF_8));
                    outputStream.flush();
                }

                LogUtils.info("HTTP代理转发完成: 状态码={}, Content-Type={}, 响应体长度={}",
                        httpResponse.getStatusCode(),
                        httpResponse.getContentType(),
                        httpResponse.getBody() != null ? httpResponse.getBody().length() : 0);

                return false;
            } catch (IOException e) {
                LogUtils.error("HTTP代理转发失败", e);
                return true;
            }
        }
        return true;
    }

    /**
     *
     * @param request 入参
     * @return com.alibaba.fastjson.JSONObject
     * @Description: 作用:  获取参数
     */
    public static String getParameters(HttpServletRequest request) {
        try {
            // 优先从缓存中获取请求体
            String cachedBody = (String) request.getAttribute("cachedRequestBody");
            if (cachedBody != null) {
                LogUtils.info("HttpProxyInterceptor.preHandle,cachedBody={}", cachedBody);
                return cachedBody;
            }

            // 如果缓存中没有数据，则从输入流中读取

            BufferedReader streamReader = new BufferedReader(new InputStreamReader(request.getInputStream(), "UTF-8"));
            StringBuilder responseStrBuilder = new StringBuilder();
            String inputStr;
            while ((inputStr = streamReader.readLine()) != null) {
                responseStrBuilder.append(inputStr).append("\n"); // 添加换行符
            }
            if(ObjectUtils.isEmpty(responseStrBuilder)) {
                return null;
            }
            String bodyString = responseStrBuilder.toString().trim();
            LogUtils.info("HttpProxyInterceptor.preHandle,bodyString={}", bodyString);
            return bodyString;
        } catch (Exception e) {
            LogUtils.info("HttpProxyInterceptor.getParameters,error={}", e.getMessage());
            return null;
        }
    }

    private boolean isNewBase(HttpServletRequest request, HttpArg arg, String uri) {
        log.info("HttpProxyInterceptor.isNewBase,request={},arg={},uri={}", request, arg, uri);
        try {
            //有几个特殊接口，直接加密的数据，这里解密
            List<String> decodeUris = Lists.newArrayList("/qyweixin/getFsEaList", "/qyweixin/loginByFsEa");
            //param
            String fsEa = URL.valueOf(arg.getUrl()).getParameter("ea");
            if(StringUtils.isEmpty(fsEa)) {
                fsEa = URL.valueOf(arg.getUrl()).getParameter("fsEa");
            } else if(StringUtils.isEmpty(fsEa)) {
                fsEa = URL.valueOf(arg.getUrl()).getParameter("fsEnterpriseAccount");
            }

            if(StringUtils.isNotEmpty(fsEa)) {
                if(decodeUris.contains(uri)) {
                    fsEa = decodeData(URLDecoder.decode(URLDecoder.decode(fsEa, "utf-8"), "utf-8"));
                }
                return oANewBaseManager.canRunInNewBaseByFsEa(fsEa);
            }

            String outEa = URL.valueOf(arg.getUrl()).getParameter("outEa");
            if(StringUtils.isEmpty(outEa)) {
                outEa = URL.valueOf(arg.getUrl()).getParameter("corpId");
            }

            if (StringUtils.isNotEmpty(outEa)) {
                if(decodeUris.contains(uri)) {
                    outEa = decodeData(URLDecoder.decode(URLDecoder.decode(outEa, "utf-8"), "utf-8"));
                }
                return oANewBaseManager.canRunInNewBaseByOutEa(outEa);
            }


            //cookie
            Cookie[] cookies = request.getCookies();
            if(ObjectUtils.isNotEmpty(cookies)) {
                for (Cookie cookie : cookies) {
                    if ("FSAuthXC".equalsIgnoreCase(cookie.getName())) {
                        fsEa = getUserFromFsCookie(cookie.getValue());
                        if(StringUtils.isNotEmpty(fsEa)) {
                            return oANewBaseManager.canRunInNewBaseByFsEa(fsEa);
                        }
                    }
                }
            }

            //body
            // 只有 POST 或 PUT 请求才尝试读取请求体
            if ("POST".equalsIgnoreCase(request.getMethod()) || "PUT".equalsIgnoreCase(request.getMethod())) {
                // 包装原始的 HttpServletRequest
                try {
                    ContentCachingRequestWrapper wrappedRequest = new ContentCachingRequestWrapper(request);
                    // 显式读取请求体
                    StringBuilder bodyBuilder = new StringBuilder();
                    BufferedReader reader = wrappedRequest.getReader();
                    String line;
                    while ((line = reader.readLine()) != null) {
                        bodyBuilder.append(line).append("\n"); // 添加换行符
                    }
                    String requestBody = bodyBuilder.toString().trim(); // 去除末尾的换行符
                    wrappedRequest.setAttribute("cachedRequestBody", requestBody);

                    // 将包装后的 request 传递给后续处理
                    request = wrappedRequest;

                    byte[] cachedBody = wrappedRequest.getContentAsByteArray();
                    if (cachedBody != null && cachedBody.length > 0) {
                        JsonNode bodyNode = objectMapper.readTree(cachedBody);
                        log.info("HttpProxyInterceptor.isNewBase,bodyNode={}", bodyNode);
                        Map<String, String> extractEaFromJson = extractEaFromJson(bodyNode);
                        if (extractEaFromJson == null || extractEaFromJson.isEmpty()) {
                            return false;
                        }
                        log.info("HttpProxyInterceptor.isNewBase,extractEaFromJson={}", extractEaFromJson);

                        return oANewBaseManager.canRunInNewBaseEnv(
                                extractEaFromJson.getOrDefault("outEa", null),
                                extractEaFromJson.getOrDefault("fsEa", null));
                    }
                } catch (IOException e) {
                    log.error("HttpProxyInterceptor.isNewBase,error", e);
                    return false;
                }
            }
        } catch (UnsupportedEncodingException e) {
            log.error("HttpProxyInterceptor.isNewBase,error", e);
        }
        return false;
    }

    private String getUserFromFsCookie(String fsAuthXCCookie) {
        try {
            log.info("HttpProxyInterceptor.getUserFromFsCookie,fsAuthXCCookie={}",fsAuthXCCookie);
            CookieToAuth.Argument argument = new CookieToAuth.Argument();
            argument.setCookie(fsAuthXCCookie);
            CookieToAuth.Result<AuthXC> result = activeSessionAuthorizeService.cookieToAuthXC(argument);
            AuthXC authXC = result.getBody();
            String fsEa = authXC.getEnterpriseAccount();
            log.info("HttpProxyInterceptor.getUserFromFsCookie,fsEa={}",fsEa);
            return fsEa;
        } catch (Exception e) {
            log.error("HttpProxyInterceptor.getUserFromFsCookie,error", e);
            return null;
        }
    }

    /**
     * 从 JSON 中递归提取 fsEa 或 outEa 的值
     */
    private Map<String, String> extractEaFromJson(JsonNode jsonNode) {
        Map<String, String> results = new HashMap<>();

        if (jsonNode.isObject()) {
            if (jsonNode.has("ea")) {
                results.put("fsEa", jsonNode.get("ea").asText());
            } else if (jsonNode.has("fsEa")) {
                results.put("fsEa", jsonNode.get("fsEa").asText());
            } else if (jsonNode.has("fsEnterpriseAccount")) {
                results.put("fsEa", jsonNode.get("fsEnterpriseAccount").asText());
            }

            if (jsonNode.has("outEa")) {
                results.put("outEa", jsonNode.get("outEa").asText());
            } else if (jsonNode.has("corpId")) {
                results.put("outEa", jsonNode.get("corpId").asText());
            }

            for (JsonNode childNode : jsonNode) {
                Map<String, String> childResults = extractEaFromJson(childNode);
                if (childResults != null && !childResults.isEmpty()) {
                    results.putAll(childResults);
                }
            }
        } else if (jsonNode.isArray()) {
            for (JsonNode childNode : jsonNode) {
                Map<String, String> childResults = extractEaFromJson(childNode);
                if (childResults != null && !childResults.isEmpty()) {
                    results.putAll(childResults);
                }
            }
        }

        return results.isEmpty() ? null : results;
    }

    private String decodeData(String data) {
        log.info("HttpProxyInterceptor.decodeData,data={}",data);
        Pattern pattern = Pattern.compile(VER+"(.*)");
        Matcher matcher = pattern.matcher(SecurityUtil.decryptStr(data));
        if (matcher.find()) {
            return matcher.group(1);
        } else {
            return null;
        }
    }

    /**
     * 判断是否应该跳过某个请求头的转发
     * @param headerName 请求头名称
     * @return true表示跳过，false表示转发
     */
    private boolean shouldSkipRequestHeader(String headerName) {
        if (headerName == null) {
            return true;
        }

        String lowerHeaderName = headerName.toLowerCase();

        // 跳过这些请求头，因为它们应该由HTTP客户端自己设置或不适合转发
        return lowerHeaderName.equals("host") ||              // 目标主机头，应该由HTTP客户端设置
               lowerHeaderName.equals("accept-encoding") ||    // 接受编码，避免压缩问题
               lowerHeaderName.equals("content-length") ||     // 内容长度，由StringEntity自动计算
               lowerHeaderName.equals("transfer-encoding") ||  // 传输编码
               lowerHeaderName.equals("connection") ||         // 连接控制
               lowerHeaderName.equals("proxy-authenticate") || // 代理认证
               lowerHeaderName.equals("proxy-authorization") || // 代理授权
               lowerHeaderName.equals("te") ||                // 传输编码扩展
               lowerHeaderName.equals("trailers") ||          // 尾部字段
               lowerHeaderName.equals("upgrade") ||           // 协议升级
               lowerHeaderName.startsWith("x-forwarded-");    // 代理转发相关头
    }

    /**
     * 判断是否应该跳过某个响应头的转发
     * @param headerName 响应头名称
     * @return true表示跳过，false表示转发
     */
    private boolean shouldSkipResponseHeader(String headerName) {
        if (headerName == null) {
            return true;
        }

        String lowerHeaderName = headerName.toLowerCase();

        // 跳过这些响应头，因为它们应该由代理服务器自己设置
        return lowerHeaderName.equals("transfer-encoding") ||  // 传输编码
               lowerHeaderName.equals("connection") ||         // 连接控制
               lowerHeaderName.equals("proxy-authenticate") || // 代理认证
               lowerHeaderName.equals("proxy-authorization") || // 代理授权
               lowerHeaderName.equals("te") ||                // 传输编码扩展
               lowerHeaderName.equals("trailers") ||          // 尾部字段
               lowerHeaderName.equals("upgrade") ||           // 协议升级
               lowerHeaderName.startsWith("x-forwarded-");    // 代理转发相关头
    }

//    /**
//     * 判断当前企业是否可以走灰度环境
//     * @param outEa
//     * @return
//     */
//    public boolean canRunInNewBaseEnv(String outEa, String fsEa) {
//
//        List<OuterOaEnterpriseBindEntity> result;
//        if(StringUtils.isNotEmpty(outEa)) {
//            result = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).outEa(outEa).build());
//            log.info("OANewBaseManager.canRunInNewBaseEnv,query enterprise mapping,outEa={},result={}", outEa,result);
//        } else {
//            result = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).build());
//            log.info("OANewBaseManager.canRunInNewBaseEnv,query enterprise mapping,fsEa={},result={}", fsEa,result);
//        }
//
//        boolean isNew = Boolean.FALSE;
//        if (CollectionUtils.isEmpty(result)) {
//            //没有绑定关系
//            isNew = Boolean.TRUE;
//        } else {
//            Long cusTime = ConfigCenter.QYWX_GRAY_TIME_MILLIS;
//            log.info("OANewBaseManager.canRunInNewBaseEnv,query enterprise mapping,grayOutEaBindTimeGreater={}", cusTime);
//            if(result.get(0).getCreateTime() > cusTime) {
//                isNew = Boolean.TRUE;
//            }
//        }
//
//        if (ConfigCenter.MAIN_ENV) {
//            //如果是老服务，反转
//            isNew = !isNew;
//        }
//        return isNew;
//    }
//
//    public Boolean canRunInNewBaseByFsEa(String fsEa) {
//        boolean isNew = canRunInNewBaseEnv(null, fsEa);
//        log.info("OANewBaseManager.canRunInNewBaseByFsEa,fsEa={},isNew={}",fsEa,isNew);
//        return isNew;
//    }
//
//    public Boolean canRunInNewBaseByOutEa(String outEa) {
//        boolean isNew = canRunInNewBaseEnv(outEa, null);
//        log.info("OANewBaseManager.canRunInNewBaseByOutEa,outEa={},isNew={}",outEa,isNew);
//        return isNew;
//    }
}
