package com.facishare.open.qywx.web.manager;

import com.alibaba.fastjson.*;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.oa.base.dbproxy.mongo.dao.OaConnectorSyncEventDataMongoDao;
import com.facishare.open.oa.base.dbproxy.mongo.document.OaConnectorSyncEventDataDoc;
import com.facishare.open.oa.base.dbproxy.pg.entity.*;
import com.facishare.open.oa.base.dbproxy.pg.manager.*;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaAppInfoParams;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEnterpriseBindParams;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.order.contacts.proxy.limiter.CrmRateLimiter;
import com.facishare.open.outer.oa.connector.common.api.enums.*;
import com.facishare.open.outer.oa.connector.common.api.params.QyweixinAppInfoParams;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountinner.model.QyweixinGetAuthInfoRsp;
import com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService;
import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.accountsync.core.enums.CloudProxyEnum;
import com.facishare.open.qywx.accountsync.model.*;
import com.facishare.open.qywx.accountsync.model.qyweixin.*;
import com.facishare.open.qywx.web.config.ConfigCenter;
import com.facishare.open.qywx.web.core.enums.*;
import com.facishare.open.qywx.web.enums.PricePlanTypeEnum;
import com.facishare.open.qywx.web.info.ConversationArchiveInfo;
import com.facishare.open.qywx.web.model.qyweixin.QyweixinGetPermenantCodeRsp;
import com.facishare.open.qywx.web.mq.sender.MQSender;
import com.facishare.open.qywx.web.notify.AutoConfRocketMQProducer;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.accountsync.service.ContactsService;
import com.facishare.open.qywx.web.utils.HttpHelper;
import com.facishare.open.qywx.accountsync.utils.xml.ContactsXml;
import com.facishare.open.qywx.accountsync.utils.xml.QYWeiXinMemberEventXml;
import com.facishare.open.qywx.accountsync.utils.xml.XStreamUtils;
import com.facishare.open.qywx.save.service.MessageGeneratingService;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.result.FindEmployeeDtoByFullNameResult;
import com.facishare.organization.api.model.employee.result.GetEmployeesDtoByNameResult;
import com.fxiaoke.api.IdGenerator;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mongodb.bulk.BulkWriteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.rmi.RemoteException;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 处理企业信息存储，更新，企业开通，通讯录变更消息发送
 * Created by liuwei on 2018/09/06
 */
@Slf4j
@Component
public class CorpManager {

    ExecutorService corpManagerThreadPool = Executors.newCachedThreadPool();

    @Resource(name = "qywxEventNotifyMQSender")
    private AutoConfRocketMQProducer qywxEventNotifyMQSender;

//    @Autowired
//    private QyweixinCorpBindDao qyweixinCorpBindDao;
//
//    @Autowired
//    private QyweixinCorpInfoDao qyweixinCorpInfoDao;

    @Autowired
    private QYWeixinManager qyWeixinManager;

    @Autowired
    private QyweixinAccountBindService qyweixinAccountBindService;

    @Autowired
    private QyweixinAccountBindInnerService qyweixinAccountBindInnerService;

//    @Autowired
//    private PlatformAppAdminInfoDao platformAppAdminInfoDao;
//
//    @Autowired
//    private QyweixinOrderInfoDao qyweixinOrderInfoDao;

    @Autowired
    private OrderManager orderManager;

    @Autowired
    private MessageGeneratingService messageGeneratingService;

    @Autowired
    private FsManager fsManager;

    @Autowired
    private ContactsService contactsService;

    @Autowired
    private QyweixinGatewayInnerService qyweixinGatewayInnerService;

    @ReloadableProperty("contactAppId")
    private String contactAppId;

    @ReloadableProperty("repAppId")
    private String repAppId;

    @Autowired
    private MQSender mqSender;

    @Resource
    private OuterOaAppInfoManager outerOaAppInfoManager;
    @Resource
    private OuterOaOrderInfoManager outerOaOrderInfoManager;
    @Resource
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Resource
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;
    @Resource
    private OaConnectorSyncEventDataMongoDao oaConnectorSyncEventDataMongoDao;

    private HttpHelper httpHelper = new HttpHelper();

    public static final String TAG_ENTERPRISE = "qywx_enterprise_order";
    public static final String TAG_ORGANIZATION = "qywx_update_organization";
    public static final String TAG_MESSAGE = "qywx_send_msg";
    public static final String TAG_EXTERNAL_CONTACT = "tag_external_contact";
    public static final String TAG_DELETE_EXTERNAL_CONTACT = "tag_delete_external_contact";
    public static final String TAG_CREATE_USER = "tag_create_user";
    public static final String TAG_UPDATE_USER = "tag_update_user";
    public static final String TAG_DELETE_USER = "tag_delete_user";
    public static final String TAG_CREATE_EXTERNAL_CHAT = "tag_create_external_chat";
    public static final String TAG_UPDATE_EXTERNAL_CHAT = "tag_update_external_chat";
    public static final String TAG_DISMISS_EXTERNAL_CHAT = "tag_dismiss_external_chat";

    /**
     * 历史待办消息通知接口
     */
    @ReloadableProperty("historyToDoMessageUrl")
    private String historyToDoMessageUrl;

    /**
     * 审批流，业务流通知接口
     */
    @ReloadableProperty("flowTaskUrl")
    private String flowTaskUrl;

    @Autowired
    private EIEAConverter eieaConverter;
    /**
     * 各个应用和试用版本之间的关系
     */
    @ReloadableProperty("tryEditionMapping")
    private String tryEditionMapping;
    Map<String, String> tryEditionMap = Maps.newConcurrentMap();
    @Autowired
    private OuterOaConfigInfoManager outerOaConfigInfoManager;

    @PostConstruct
    public void init() {
        tryEditionMap = JSON.parseObject(tryEditionMapping, new TypeReference<Map<String, String>>(){});
        log.info("init tryEditionMapping success. tryEditionMapping:{}, tryEditionMap:{}", tryEditionMapping, tryEditionMap);
    }

    /**
     * 1.authCode获取企业信息
     * 2.发送开通企业mq
     * 3.异步保存企业信息
     * @param corpAuthInfo
     * @param appId
     * @return
     */
    public Result<QyweixinEnterpriseOrder> initCorpEvent(QyweixinGetPermenantCodeRsp corpAuthInfo, String appId, String authCode) {

        //发送开通企业通知消息
        QyweixinEnterpriseOrder qyweixinAddEnterprise = new QyweixinEnterpriseOrder();

        QyweixinAgentRsp qyweixinAgentRsp = null;
        if(corpAuthInfo.getAuth_info().getAgent()!=null && corpAuthInfo.getAuth_info().getAgent().size()>0) {
            qyweixinAgentRsp = corpAuthInfo.getAuth_info().getAgent().get(0);
            if(qyweixinAgentRsp.getAuth_mode()==1) { //成员授权
                com.facishare.open.qywx.accountinner.result.Result<QyweixinGetAdminListRsp> qyweixinGetAdminListRspResult = qyWeixinManager.getAdminList(corpAuthInfo.getAuth_corp_info().getCorpid(),
                        appId,
                        qyweixinAgentRsp.getAgentid());

                log.info("CorpManager.initCorpEvent,qyweixinGetAdminListRsp={}",qyweixinGetAdminListRspResult);

                if(qyweixinGetAdminListRspResult.isSuccess()
                        && ObjectUtils.isNotEmpty(qyweixinGetAdminListRspResult.getData())
                        && qyweixinGetAdminListRspResult.getData().getAdmin()!=null
                        && qyweixinGetAdminListRspResult.getData().getAdmin().size()>0) {
                    QyweixinGetAdminListRsp.AdminModel adminModel = null;
                    QyweixinGetAdminListRsp qyweixinGetAdminListRsp = qyweixinGetAdminListRspResult.getData();
                    for(QyweixinGetAdminListRsp.AdminModel item : qyweixinGetAdminListRsp.getAdmin()) {
                        if(item.getAuth_type()==1) {
                            adminModel = item;
                            break;
                        }
                    }
                    log.info("CorpManager.initCorpEvent,adminModel={}",adminModel);
                    if(adminModel==null) {
                        adminModel = qyweixinGetAdminListRsp.getAdmin().get(0);
                    }
                    log.info("CorpManager.initCorpEvent,adminModel2={}",adminModel);

                    com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> userDetailInfoRspResult = qyWeixinManager.getUserInfo(appId,
                            corpAuthInfo.getAuth_corp_info().getCorpid(),
                            adminModel.getUserid());
                    log.info("CorpManager.initCorpEvent,userDetailInfoRsp={}",userDetailInfoRspResult);

                    qyweixinAddEnterprise.setUserId(adminModel.getUserid());

                    if(userDetailInfoRspResult.isSuccess() && ObjectUtils.isNotEmpty(userDetailInfoRspResult.getData())) {
                        QyweixinUserDetailInfoRsp userDetailInfoRsp = userDetailInfoRspResult.getData();
                        qyweixinAddEnterprise.setUserName(userDetailInfoRsp.getName());
                    } else {
                        qyweixinAddEnterprise.setUserName(adminModel.getUserid());
                    }
                }
            }
        }

        if(StringUtils.isEmpty(qyweixinAddEnterprise.getUserId())) {
            qyweixinAddEnterprise.setUserId(corpAuthInfo.getAuth_user_info().getUserid());
            //成员授权模式下，name可能为null
            qyweixinAddEnterprise.setUserName("U-FSQYWX-"+ (StringUtils.isNotEmpty(corpAuthInfo.getAuth_user_info().getName()) ? corpAuthInfo.getAuth_user_info().getName() : corpAuthInfo.getAuth_user_info().getUserid()));
        }

        qyweixinAddEnterprise.setAvatar(corpAuthInfo.getAuth_user_info().getAvatar());
        qyweixinAddEnterprise.setCorpId(corpAuthInfo.getAuth_corp_info().getCorpid());
        qyweixinAddEnterprise.setCorpName(corpAuthInfo.getAuth_corp_info().getCorp_name());
        qyweixinAddEnterprise.setAppId(appId);

        //设置可见范围 （通讯录应用没有可见范围，做判空处理）
        QyweixinAgentPrivilege qyweixinAgentPrivilege = new QyweixinAgentPrivilege();
        if(null != corpAuthInfo.getAuth_info().getAgent() && ! corpAuthInfo.getAuth_info().getAgent().isEmpty()){
            qyweixinAgentPrivilege.setAllowParty(qyweixinAgentRsp.getPrivilege().getAllow_party());
            qyweixinAgentPrivilege.setAllowTag(qyweixinAgentRsp.getPrivilege().getAllow_tag());

            if(qyweixinAgentRsp.getAuth_mode()==1) {
                qyweixinAgentPrivilege.setAllowUser(Lists.newArrayList(qyweixinAddEnterprise.getUserId()));
            } else {
                qyweixinAgentPrivilege.setAllowUser(qyweixinAgentRsp.getPrivilege().getAllow_user());
            }
        }
        qyweixinAddEnterprise.setPrivilege(qyweixinAgentPrivilege);

        log.info("initCorpEvent,corpAuthInfo.getEdition_info()={}",corpAuthInfo.getEdition_info());
        //判断企业是否是试用，试用则直接推送试用企业开通消息，非试用则进一步查询订单(这里是后来发现问题才多了几个判断的，代码可以进一步优化）
        //增加一个试用过期
        if (null != corpAuthInfo.getEdition_info() && !corpAuthInfo.getEdition_info().getAgent().isEmpty()) {
            if (AppStatusEnum.LIMITED_TIME_TRIAL.getCode().equals(corpAuthInfo.getEdition_info().getAgent().get(0).getApp_status())
                    || AppStatusEnum.UNLIMITED_TRIAL.getCode().equals(corpAuthInfo.getEdition_info().getAgent().get(0).getApp_status())
                    || AppStatusEnum.TRIAL_EXPIRED.getCode().equals(corpAuthInfo.getEdition_info().getAgent().get(0).getApp_status())) {

                qyweixinAddEnterprise.setOrderType(OuterOaOrderInfoTypeEnum.buy);
                // 设置试用版本
                qyweixinAddEnterprise.setEditionId(tryEditionMap.get(appId));
                qyweixinAddEnterprise.setPricePlanType(PricePlanTypeEnum.trial);
                long currentTimestamp = System.currentTimeMillis();
                // 计算7天的毫秒数
                long sevenDaysInMillis = 7 * 24 * 60 * 60 * 1000;
                // 当前时间戳加上7天的毫秒数
                long newTimestamp = currentTimestamp + sevenDaysInMillis;
                qyweixinAddEnterprise.setBeginTime(currentTimestamp);
                qyweixinAddEnterprise.setEndTime(newTimestamp);
            } else {
                //查询是否该企业存在未处理的购买订单，存在则合并推送，不存在则只保存企业信息
                List<OuterOaOrderInfoEntity> paidOrders = outerOaOrderInfoManager.getPaidOrders(ChannelEnum.qywx, corpAuthInfo.getAuth_corp_info().getCorpid(), appId, null);
                log.info("CorpManager.initCorpEvent.paidOrders={}",paidOrders);
                if (CollectionUtils.isNotEmpty(paidOrders)) {
                    //是否存在非处理的普通订单
                    paidOrders = paidOrders.stream()
                            .sorted(Comparator.comparing(OuterOaOrderInfoEntity::getCreateTime).reversed())
                            .collect(Collectors.toList());
                    for (OuterOaOrderInfoEntity paidOrder : paidOrders) {
                        QyweixinOrderInfo qyweixinOrderInfo = JSON.parseObject(paidOrder.getOrderInfo(), QyweixinOrderInfo.class);
                        if (Objects.equals(qyweixinOrderInfo.getProcessingStatus(), ProcessingStatusEnum.NOT_PUSHED_UNPROCESSED.getCode())
                                && qyweixinOrderInfo.getOrderFrom() == 0) {
                            //存在订单信息,合并订单和企业信息
                            log.info("trace createCorpEvent from qywx and found order info, corpid:{}", corpAuthInfo.getAuth_corp_info().getCorpid());
                            qyweixinAddEnterprise = orderManager.convertQyweixinOrderEvent(qyweixinAddEnterprise, paidOrder);
                            //置订单处理状态为 已推送已处理
                            qyweixinOrderInfo.setProcessingStatus(ProcessingStatusEnum.PUSHED_PROCESSED.getCode());
                            paidOrder.setOrderInfo(JSON.toJSONString(qyweixinOrderInfo));
                            Integer count = outerOaOrderInfoManager.updateById(paidOrder);
                            log.info("trace payForAppSuccessEvent save success,qyweixinOrderInfo={},count={} ",qyweixinOrderInfo,  count);
                            break;
                        }
                    }
                }
            }
        } else {
            //兼容客户安装完CRM应用后，版本信息字段为空的场景
            qyweixinAddEnterprise.setOrderType(OuterOaOrderInfoTypeEnum.buy);
            // 设置试用版本
            qyweixinAddEnterprise.setPricePlanType(PricePlanTypeEnum.trial);
            qyweixinAddEnterprise.setEditionId(tryEditionMap.get(appId));
            // 使用默认七天
            long currentTimestamp = System.currentTimeMillis();
            // 计算7天的毫秒数
            long sevenDaysInMillis = 7 * 24 * 60 * 60 * 1000;
            // 当前时间戳加上7天的毫秒数
            long newTimestamp = currentTimestamp + sevenDaysInMillis;
            qyweixinAddEnterprise.setBeginTime(currentTimestamp);
            qyweixinAddEnterprise.setEndTime(newTimestamp);
            log.info("initCorpEvent,editionInfo=null,qyweixinAddEnterprise={}",qyweixinAddEnterprise);
        }

//        orderManager.openEnterprise(qyweixinAddEnterprise);

//        //合并订单和企业信息推送开通企业
//        Message msg = new Message();
//        msg.setTags(TAG_ENTERPRISE);
//        msg.setBody(qyweixinAddEnterprise.toProto());
//        SendResult sendResult = qywxEventNotifyMQSender.send(msg);
//        log.info("trace createCorpEvent from qywx send mq msg. authCode:{}, sendResult:{}, message:{}, body:{}",
//                authCode, sendResult, msg, JSONObject.toJSONString(qyweixinAddEnterprise));

        return new Result<>(qyweixinAddEnterprise);
    }

    /**
     * 获取管理员userid
     * @param corpId
     */
    public String getAdminUserId(String corpId){
        List<OuterOaAppInfoEntity> appInfoEntities = outerOaAppInfoManager.getEntities(OuterOaAppInfoParams.builder().channel(ChannelEnum.qywx).outEa(corpId).build());
        if(CollectionUtils.isEmpty(appInfoEntities)) {
            return null;
        }
        QyweixinAppInfoParams qyweixinAppInfoParams = JSON.parseObject(appInfoEntities.get(0).getAppInfo(), QyweixinAppInfoParams.class);
        return qyweixinAppInfoParams.getAuthAppUserInfo().getUserId();
    }


    /**
     * 保存企业信息，企业绑定信息，应用授权范围
     * @param qyweixinGetPermenantCodeRsp
     * @param appId
     */
    public void saveCorpInfoTask(QyweixinGetPermenantCodeRsp qyweixinGetPermenantCodeRsp, String appId, OuterOaAppInfoTypeEnum appInfoType) {
        String corpId = qyweixinGetPermenantCodeRsp.getAuth_corp_info().getCorpid();
        log.info("CorpManager.saveCorpInfoTask,corpId2={}",corpId);
        //保存绑定关系或更新
        OuterOaAppInfoEntity appInfoManagerEntity = outerOaAppInfoManager.getEntity(ChannelEnum.qywx, corpId, appId);
        log.info("CorpManager.saveCorpInfoTask,corpId={},appId={},appInfoManagerEntity={}",corpId,appId,appInfoManagerEntity);
        if (ObjectUtils.isEmpty(appInfoManagerEntity)) {
            appInfoManagerEntity = new OuterOaAppInfoEntity();
            appInfoManagerEntity.setId(IdGenerator.get());
            appInfoManagerEntity.setChannel(ChannelEnum.qywx);
            appInfoManagerEntity.setOutEa(corpId);
            appInfoManagerEntity.setAppId(appId);
            appInfoManagerEntity.setAppType(appInfoType);
            QyweixinAppInfoParams qyweixinAppInfoParams = convertQyweixinAppInfo(qyweixinGetPermenantCodeRsp, null);
            appInfoManagerEntity.setAppInfo(new Gson().toJson(qyweixinAppInfoParams));
            appInfoManagerEntity.setStatus(OuterOaAppInfoStatusEnum.normal);
            appInfoManagerEntity.setCreateTime(System.currentTimeMillis());
            appInfoManagerEntity.setUpdateTime(System.currentTimeMillis());
            Integer insert = outerOaAppInfoManager.insert(appInfoManagerEntity);
            log.info("CorpManager.saveCorpInfoTask,save success,insert={}",insert);
        } else {
            QyweixinAppInfoParams oldQyweixinAppInfoParams = JSON.parseObject(appInfoManagerEntity.getAppInfo(), QyweixinAppInfoParams.class);
            QyweixinAppInfoParams finalQyweixinAppInfoParams = convertQyweixinAppInfo(qyweixinGetPermenantCodeRsp, oldQyweixinAppInfoParams);
            appInfoManagerEntity.setAppInfo(new Gson().toJson(finalQyweixinAppInfoParams));
            appInfoManagerEntity.setStatus(OuterOaAppInfoStatusEnum.normal);
            appInfoManagerEntity.setUpdateTime(System.currentTimeMillis());
            Integer update = outerOaAppInfoManager.updateById(appInfoManagerEntity);
            log.info("CorpManager.saveCorpInfoTask,update success,update={}",update);
        }

        //初始化corpAccessToken
        qyWeixinManager.getCorpAccessToken(appId, corpId, true);
    }

    private QyweixinAppInfoParams convertQyweixinAppInfo(QyweixinGetPermenantCodeRsp qyweixinGetPermenantCodeRsp, QyweixinAppInfoParams qyweixinAppInfoParams) {
        if (ObjectUtils.isEmpty(qyweixinAppInfoParams)) {
            qyweixinAppInfoParams = new QyweixinAppInfoParams();
        }

        // 设置 permanentCode
        if (StringUtils.isNotEmpty(qyweixinGetPermenantCodeRsp.getPermanent_code())) {
            qyweixinAppInfoParams.setPermanentCode(qyweixinGetPermenantCodeRsp.getPermanent_code());
        }

        // 设置 authCorpInfo
        if (ObjectUtils.isNotEmpty(qyweixinGetPermenantCodeRsp.getAuth_corp_info())) {
            QyweixinAuthCorpInfoRsp authCorpInfoRsp = qyweixinGetPermenantCodeRsp.getAuth_corp_info();
            QyweixinAppInfoParams.AuthCorpInfo authCorpInfo = new QyweixinAppInfoParams.AuthCorpInfo();
            if (StringUtils.isNotEmpty(authCorpInfoRsp.getCorp_name())) {
                authCorpInfo.setCorpName(authCorpInfoRsp.getCorp_name());
            }
            if (StringUtils.isNotEmpty(authCorpInfoRsp.getCorp_type())) {
                authCorpInfo.setCorpType(authCorpInfoRsp.getCorp_type());
            }
            if (ObjectUtils.isNotEmpty(authCorpInfoRsp.getSubject_type())) {
                authCorpInfo.setSubjectType(authCorpInfoRsp.getSubject_type());
            }
            if (StringUtils.isNotEmpty(authCorpInfoRsp.getCorp_square_logo_url())) {
                authCorpInfo.setCorpSquareLogoUrl(authCorpInfoRsp.getCorp_square_logo_url());
            }
            if (StringUtils.isNotEmpty(authCorpInfoRsp.getCorp_full_name())) {
                authCorpInfo.setCorpFullName(authCorpInfoRsp.getCorp_full_name());
            }
            qyweixinAppInfoParams.setAuthCorpInfo(authCorpInfo);
        }

        // 设置 authAppInfo
        if (ObjectUtils.isNotEmpty(qyweixinGetPermenantCodeRsp.getAuth_info())
                && CollectionUtils.isNotEmpty(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent())) {
            QyweixinAgentRsp agentRsp = qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().get(0);
            QyweixinAppInfoParams.AuthAppInfo authAppInfo = new QyweixinAppInfoParams.AuthAppInfo();
            if (StringUtils.isNotEmpty(agentRsp.getName())) {
                authAppInfo.setName(agentRsp.getName());
            }
            authAppInfo.setAgentId(agentRsp.getAgentid());
            if (StringUtils.isNotEmpty(agentRsp.getRound_logo_url())) {
                authAppInfo.setRoundLogoUrl(agentRsp.getRound_logo_url());
            }
            if (StringUtils.isNotEmpty(agentRsp.getSquare_logo_url())) {
                authAppInfo.setSquareLogoUrl(agentRsp.getSquare_logo_url());
            }
            if (ObjectUtils.isNotEmpty(agentRsp.getAuth_mode())) {
                authAppInfo.setAuthMode(agentRsp.getAuth_mode());
            }
            if (ObjectUtils.isNotEmpty(agentRsp.getShared_from())) {
                QyweixinAppInfoParams.AuthAppInfo.AgentSharedFrom sharedFrom = new QyweixinAppInfoParams.AuthAppInfo.AgentSharedFrom();
                if (StringUtils.isNotEmpty(agentRsp.getShared_from().getCorpid())) {
                    sharedFrom.setCorpId(agentRsp.getShared_from().getCorpid());
                }
                if (ObjectUtils.isNotEmpty(agentRsp.getShared_from().getShare_type())) {
                    sharedFrom.setShareType(agentRsp.getShared_from().getShare_type());
                }
                authAppInfo.setSharedFrom(sharedFrom);
            }
            qyweixinAppInfoParams.setAuthAppInfo(authAppInfo);
        }

        // 设置 authAppUserInfo
        if (ObjectUtils.isNotEmpty(qyweixinGetPermenantCodeRsp.getAuth_user_info())) {
            QyweixinAuthUserInfoRsp authUserInfoRsp = qyweixinGetPermenantCodeRsp.getAuth_user_info();
            QyweixinAppInfoParams.AuthAppUserInfo authAppUserInfo = new QyweixinAppInfoParams.AuthAppUserInfo();
            if (StringUtils.isNotEmpty(authUserInfoRsp.getName())) {
                authAppUserInfo.setName(authUserInfoRsp.getName());
            }
            if (StringUtils.isNotEmpty(authUserInfoRsp.getAvatar())) {
                authAppUserInfo.setAvatar(authUserInfoRsp.getAvatar());
            }
            if (StringUtils.isNotEmpty(authUserInfoRsp.getUserid())) {
                authAppUserInfo.setUserId(authUserInfoRsp.getUserid());
            }
            qyweixinAppInfoParams.setAuthAppUserInfo(authAppUserInfo);
        }

        return qyweixinAppInfoParams;
    }


//    /**
//     * 保存企业代开发绑定信息
//     * @param qyweixinGetPermenantCodeRsp
//     * @param appId
//     */
//    public void saveRepCorpInfoTask(QyweixinGetPermenantCodeRsp qyweixinGetPermenantCodeRsp, String appId) {
//        String corpId = qyweixinGetPermenantCodeRsp.getAuth_corp_info().getCorpid();
//        corpId = qyWeixinManager.corpId2OpenCorpId(corpId).getData();
//        //保存绑定关系或更新
//        QyweixinCorpBindBo qyweixinCorpBindBo = qyweixinCorpBindDao.queryQyweixinCorpBind(corpId, appId);
//        if(null == qyweixinCorpBindBo){
//            qyweixinCorpBindBo = new QyweixinCorpBindBo();
//            qyweixinCorpBindBo.setCorpId(corpId);
//            qyweixinCorpBindBo.setCorpName(qyweixinGetPermenantCodeRsp.getAuth_corp_info().getCorp_name());
//            qyweixinCorpBindBo.setAppId(appId);
//            qyweixinCorpBindBo.setPermanentCode(qyweixinGetPermenantCodeRsp.getPermanent_code());
//            qyweixinCorpBindBo.setAgentId(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().isEmpty() ? null : String.valueOf(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().get(0).getAgentid()));
//            qyweixinCorpBindBo.setStatus(QyweixinBindStatusEnum.BIND.getCode());
//            int count = qyweixinCorpBindDao.save(qyweixinCorpBindBo);
//            log.info("CorpManager.saveRepCorpInfoTask,save,count={},qyweixinCorpBindBo={}",count,qyweixinCorpBindBo);
//        } else {
//            updateCorpRepSecret(qyweixinCorpBindBo, corpId, qyweixinGetPermenantCodeRsp, appId);
//            qyweixinCorpBindBo.setStatus(QyweixinBindStatusEnum.BIND.getCode());
//            qyweixinCorpBindBo.setCorpName(qyweixinGetPermenantCodeRsp.getAuth_corp_info().getCorp_name());
//            qyweixinCorpBindBo.setAgentId(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().isEmpty() ? null :String.valueOf(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().get(0).getAgentid()));
//            qyweixinCorpBindBo.setPermanentCode(qyweixinGetPermenantCodeRsp.getPermanent_code());
//            int count = qyweixinCorpBindDao.update(qyweixinCorpBindBo);
//            log.info("CorpManager.saveRepCorpInfoTask,update,count={},qyweixinCorpBindBo={}",count,qyweixinCorpBindBo);
//        }
//        //初始化corpAccessToken
//        String corpSecretCode = qyweixinCorpBindDao.getCorpSecretCode(corpId, appId);
//        corpSecretCode = SecurityUtil.decryptStr(corpSecretCode);
//        qyWeixinManager.getToken(corpSecretCode,corpId);
//    }

//    public void updateCorpRepSecret(QyweixinCorpBindBo qyweixinCorpBindBo, String corpId,
//                                    QyweixinGetPermenantCodeRsp qyweixinGetPermenantCodeRsp, String appId) {
//        //为了区分代开发和自建应用授权的会话存档，在secret未更新的时候做比对
//        com.facishare.open.qywx.accountbind.result.Result<String> result = qyweixinAccountBindService.outEaToFsEa("qywx", corpId,null);
//        String ea = result.getData();
//        com.facishare.open.qywx.save.result.Result<GenerateSettingVo> generateSettingVoResult = messageGeneratingService.querySetting(ea, null, corpId);
//        log.info("CorpManager.updateCorpRepSecret,generateSettingVoResult={}", generateSettingVoResult);
//        if(ObjectUtils.isNotEmpty(generateSettingVoResult.getData()) &&
//                generateSettingVoResult.getData().getCorpSecret().equals(qyweixinCorpBindBo.getPermanentCode())) {
//            //更新会话留存的secret和agentId
//            GenerateSettingVo generateSettingVo = new GenerateSettingVo();
//            generateSettingVo.setQywxCorpId(corpId);
//            generateSettingVo.setCorpSecret(qyweixinGetPermenantCodeRsp.getPermanent_code());
//            generateSettingVo.setAgentId(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().isEmpty() ? null :String.valueOf(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().get(0).getAgentid()));
//            log.info("CorpManager.updateCorpRepSecret,generateSettingVo={}", generateSettingVo);
//            messageGeneratingService.updateCorpRepSecret(generateSettingVo);
//        }
//    }

//    public void convertQyweixinCorpInfo(QyweixinGetPermenantCodeRsp qyweixinGetPermenantCodeRsp, QyweixinCorpInfoBo qyweixinCorpInfoBo) {
//        QyweixinAuthCorpInfoRsp qyweixinAuthCorpInfoRsp = qyweixinGetPermenantCodeRsp.getAuth_corp_info();
//        qyweixinCorpInfoBo.setCorpName(qyweixinAuthCorpInfoRsp.getCorp_name());
//        qyweixinCorpInfoBo.setAppLevel(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().isEmpty() ? "1" : String.valueOf(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().get(0).getPrivilege().getLevel()));
//        qyweixinCorpInfoBo.setCorpFullName(qyweixinAuthCorpInfoRsp.getCorp_full_name());
//        qyweixinCorpInfoBo.setCorpIndustry(qyweixinAuthCorpInfoRsp.getCorp_industry());
//
//        if(null != qyweixinGetPermenantCodeRsp.getAuth_user_info()){
//            qyweixinCorpInfoBo.setUserId(qyweixinGetPermenantCodeRsp.getAuth_user_info().getUserid());
//        }
//        qyweixinCorpInfoBo.setCorpScale(qyweixinAuthCorpInfoRsp.getCorp_scale());
//        qyweixinCorpInfoBo.setCorpSquareLogoUrl(qyweixinAuthCorpInfoRsp.getCorp_square_logo_url());
//        qyweixinCorpInfoBo.setCorpSubIndustry(qyweixinAuthCorpInfoRsp.getCorp_sub_industry());
//        qyweixinCorpInfoBo.setCorpType(qyweixinAuthCorpInfoRsp.getCorp_type());
//        qyweixinCorpInfoBo.setCorpUserMax(qyweixinAuthCorpInfoRsp.getCorp_user_max());
//        qyweixinCorpInfoBo.setCorpWxqrcode(qyweixinAuthCorpInfoRsp.getCorp_wxqrcode());
//        qyweixinCorpInfoBo.setSubjectType(qyweixinAuthCorpInfoRsp.getSubject_type());
//        qyweixinCorpInfoBo.setVerifiedEndTime(qyweixinAuthCorpInfoRsp.getVerified_end_time());
//        qyweixinCorpInfoBo.setLocation(qyweixinAuthCorpInfoRsp.getLocation());
//
//        //在线订单上线后加上获取企业当前版本信息
//        if (null != qyweixinGetPermenantCodeRsp.getEdition_info() && !qyweixinGetPermenantCodeRsp.getEdition_info().getAgent().isEmpty()) {
//            log.info("trace convertQyweixinCorpInfo convert editionInfo:{}", qyweixinGetPermenantCodeRsp.getEdition_info().getAgent().get(0));
//            qyweixinCorpInfoBo.setEditionId(qyweixinGetPermenantCodeRsp.getEdition_info().getAgent().get(0).getEdition_id());
//            qyweixinCorpInfoBo.setEditionName(qyweixinGetPermenantCodeRsp.getEdition_info().getAgent().get(0).getEdition_name());
//            qyweixinCorpInfoBo.setAppStatus(qyweixinGetPermenantCodeRsp.getEdition_info().getAgent().get(0).getApp_status());
//            qyweixinCorpInfoBo.setUserLimit(qyweixinGetPermenantCodeRsp.getEdition_info().getAgent().get(0).getUser_limit());
//            qyweixinCorpInfoBo.setExpiredTime(qyweixinGetPermenantCodeRsp.getEdition_info().getAgent().get(0).getExpired_time());
//        }
//
//    }

    public String getValidCorpId(String corpId, String appId) {
        com.facishare.open.qywx.accountinner.result.Result<String> result = qyWeixinManager.corpId2OpenCorpId(corpId);
        if (result.isSuccess() && StringUtils.isNotEmpty(result.getData())) {
            corpId = result.getData();
        }
        log.info("CorpManager.getValidCorpId,appId={},validCorpId={}",appId,corpId);
        return corpId;
    }

    /**
     * 获取缓存永久授权码
     * @param corpId
     * @param appId
     * @return
     */
    public String getPermannentCodeFromDB(String corpId, String appId) {
        if(StringUtils.isBlank(corpId)){
            //throw new RuntimeException("trace getPermannentCodeFromDB Required String parameter 'corpId' is not present");
            log.info("trace getPermannentCodeFromDB Required String parameter 'corpId' is not present");
            return "";
        }
        OuterOaAppInfoEntity oaAppInfoEntity = outerOaAppInfoManager.getEntity(ChannelEnum.qywx, corpId, appId);
        log.info("CorpManager.getPermannentCodeFromDB,qyweixinCorpBindBo={}",oaAppInfoEntity);
        if(null == oaAppInfoEntity){
            throw new RuntimeException("trace getPermannentCodeFromDB not find permannent_code corpId:"+corpId + " appId:"+appId);
        }
        QyweixinAppInfoParams qyweixinAppInfoParams = JSON.parseObject(oaAppInfoEntity.getAppInfo(), QyweixinAppInfoParams.class);
        return qyweixinAppInfoParams.getPermanentCode();
    }

    /**
     * 获取缓存永久授权码
     * @param corpId
     * @param appId
     * @return
     */
    public String getPermannentCodeFromDB2(String corpId, String appId) {
        if(StringUtils.isBlank(corpId)){
            //throw new RuntimeException("trace getPermannentCodeFromDB Required String parameter 'corpId' is not present");
            log.info("trace getPermannentCodeFromDB2 Required String parameter 'corpId' is not present");
            return "";
        }
        OuterOaAppInfoEntity oaAppInfoEntity = outerOaAppInfoManager.getEntity(ChannelEnum.qywx, corpId, appId);
        log.info("CorpManager.getPermannentCodeFromDB2,oaAppInfoEntity={}",oaAppInfoEntity);
        if(null == oaAppInfoEntity){
            return "";
        }
        QyweixinAppInfoParams qyweixinAppInfoParams = JSON.parseObject(oaAppInfoEntity.getAppInfo(), QyweixinAppInfoParams.class);
        return qyweixinAppInfoParams.getPermanentCode();
    }

//    public String getFsEaByOutEa(String corpId) {
//        com.facishare.open.qywx.accountbind.result.Result<String> result = qyweixinAccountBindService.outEaToFsEa(SourceTypeEnum.QYWX.getSourceType(),
//                corpId,null);
//        if(! result.isSuccess()){
//            log.error("trace getFsEaByOutEa error corpId:{} ", corpId);
//            return null;
//        }
//        return result.getData();
//    }

    public void updateCorpInfo(QyweixinGetAuthInfoRsp authInfoRsp, String corpId, String appId) {
        //检测到是手动绑定纷享企业的，不发送消息
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByOutEa(ChannelEnum.qywx, corpId, appId);
        if(enterpriseBindEntities.stream().anyMatch(enterpriseBind -> enterpriseBind.getBindType().equals(BindTypeEnum.manual))) {
            log.info("trace qyweixinAccountEnterpriseMapping is:{} 手动绑定不发送消息", QyweixinBindTypeEnum.OLD_CORP_BIND.getCode());
            return ;
        }
        //应用可见范围变更处理逻辑入口
        contactsService.onAvailableRangeChanged(appId,corpId);

//        QyweixinUpdateOrganizationEvent updateOrganizationEvent = new QyweixinUpdateOrganizationEvent();
//        updateOrganizationEvent.setAppId(appId);
//        updateOrganizationEvent.setCorpId(corpId);
//        updateOrganizationEvent.setSource(SourceTypeEnum.QYWX.getSourceType());
//        updateOrganizationEvent.setFsEa(getFsEaByOutEa(corpId));
//        updateOrganizationEvent.setEventType(QyweixinUpdateEventTypeEnum.UPDATE_APP_PRIVILEGE.getEventType());
//
//        updateOrganizationEvent.setAllowParty(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().get(0).getPrivilege().getAllow_party());
//        updateOrganizationEvent.setAllowUser(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().get(0).getPrivilege().getAllow_user());
//        updateOrganizationEvent.setAllowTag(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().get(0).getPrivilege().getAllow_tag());
//
//        Message msg = new Message();
//        msg.setTags(TAG_ORGANIZATION);
//        msg.setBody(updateOrganizationEvent.toProto());
//        SendResult sendResult = qywxEventNotifyMQSender.send(msg);
//        log.info("trace_appId:{} changeContacts from qywx send mq msg sendResult:{}, message:{}, body:{}", appId, sendResult, msg, JSONObject.toJSONString(updateOrganizationEvent));
    }

    public void changeContacts(String plainMsg, String changeType) {

        //String fsAccount="";
        //通讯录变更通知通讯录变更事件(员工、部门、标签)  发送变更消息
        ContactsXml contactsInfo = XStreamUtils.parseXml(plainMsg, ContactsXml.class);
        //String appId = contactsInfo.getSuiteId();

        //通讯录员工变动
//        if("create_user".contains(changeType) || "update_user".equals(changeType) || "delete_user".equals(changeType)) {
//            sendChangeContactsMsg(contactsInfo, changeType);
//        }

//        contactsService.onChangeContacts(contactsInfo);

//        //判断是否是更新userId，然后查找数据库是否存在这个out_account,是则更新,否则插入
//        if (null != contactsInfo.getNewUserID() && !contactsInfo.getNewUserID().isEmpty()){
//            com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEmployeeMapping>> accountEmployeeMappingList =
//                    qyweixinAccountBindService.queryFsAccountBindByOldOutAccount(SourceTypeEnum.QYWX.getSourceType(),
//                            Lists.newArrayList(contactsInfo.getUserID()), appId, contactsInfo.getAuthCorpId());
//            if(null != accountEmployeeMappingList) {
//                log.info("trace queryFsEaBindByOldOutAccount accountEmployeeMappingList:{}", accountEmployeeMappingList);
//                fsAccount = accountEmployeeMappingList.getData().get(0).getFsAccount();
//                // 所有应用一起更新
//                int result = qyweixinAccountBindService.updateByNewOutAccount(contactsInfo.getNewUserID(),
//                        contactsInfo.getUserID(), StringUtils.EMPTY, contactsInfo.getAuthCorpId());
//                log.info("trace updateByNewOutAccount (success：1，fail：0） result:{}", result);
//            }else {
//                changeType="create_user";
//            }
//        }
//
//        //检测到是手动绑定纷享企业的，不发送消息
////        QyweixinAccountEnterpriseMapping qyweixinAccountEnterpriseMapping = qyweixinAccountBindInnerService.queryEnterpriseBindByOutEa(contactsInfo.getAuthCorpId());
////        if( null != qyweixinAccountEnterpriseMapping && QyweixinBindTypeEnum.OLD_CORP_BIND.getCode().equals(qyweixinAccountEnterpriseMapping.getBindType())){
////            log.info("trace qyweixinAccountEnterpriseMapping is:{} 手动绑定不发送消息", QyweixinBindTypeEnum.OLD_CORP_BIND.getCode());
////            return ;
////        }
//        if(qyweixinAccountBindService.isManualBinding("", contactsInfo.getAuthCorpId()).getData()) {
//            log.info("CorpManager.changeContacts,corpId={},boolean={}.", contactsInfo.getAuthCorpId(), qyweixinAccountBindService.isManualBinding("", contactsInfo.getAuthCorpId()).getData());
//            return;
//        }
//
//        QyweixinUpdateOrganizationEvent updateOrganizationEvent = new QyweixinUpdateOrganizationEvent();
//        updateOrganizationEvent.setAppId(appId);
//        updateOrganizationEvent.setCorpId(contactsInfo.getAuthCorpId());
//        updateOrganizationEvent.setSource(SourceTypeEnum.QYWX.getSourceType());
//        updateOrganizationEvent.setFsEa(getFsEaByOutEa(contactsInfo.getAuthCorpId()));
//
//        Message msg = new Message();
//        // 延时1s投递
//        msg.setDelayTimeLevel(1);
//        msg.setTags(TAG_ORGANIZATION);
//        if("create_user".contains(changeType)){
//            updateOrganizationEvent.setEventType(QyweixinUpdateEventTypeEnum.UPDATE_EMPLOYEE.getEventType());
//            updateOrganizationEvent.setEventId(StringUtils.isEmpty(contactsInfo.getNewUserID()) ? contactsInfo.getUserID(): contactsInfo.getNewUserID());
//        }else if("update_user".equals(changeType)){  //修改了userid的把fsAccount传过去就不用再调用sync-provider的方法了
//            updateOrganizationEvent.setEventType(QyweixinUpdateEventTypeEnum.MODIFY_EMPLOYEE.getEventType());
//            updateOrganizationEvent.setEventId(StringUtils.isEmpty(contactsInfo.getNewUserID()) ? contactsInfo.getUserID(): contactsInfo.getNewUserID());
//            updateOrganizationEvent.setFsAccount(fsAccount);
//        } else if("delete_user".equals(changeType)){
//            updateOrganizationEvent.setEventType(QyweixinUpdateEventTypeEnum.DELETE_EMPLOYEE.getEventType());
//            updateOrganizationEvent.setEventId(contactsInfo.getUserID());
//        } else if("create_party,update_party".contains(changeType)){
//
//            updateOrganizationEvent.setEventType(QyweixinUpdateEventTypeEnum.UPDATE_DEPARTMENT.getEventType());
//            updateOrganizationEvent.setEventId(contactsInfo.getId());
//        }else if("delete_party".equals(changeType)){
//
//            updateOrganizationEvent.setEventType(QyweixinUpdateEventTypeEnum.DELETE_DEPARTMENT.getEventType());
//            updateOrganizationEvent.setEventId(contactsInfo.getId());
//        }else if("update_tag".equals(changeType)){
//
//            //标签变更 发送部门员工变更消息
//            String addUser = contactsInfo.getAddUserItems();
//            String addParty = contactsInfo.getAddPartyItems();
//            String delUser = contactsInfo.getDelUserItems();
//            String delParty = contactsInfo.getDelPartyItems();
//
//            updateOrganizationEvent.setEventId(contactsInfo.getTagId());
//            updateOrganizationEvent.setEventType(QyweixinUpdateEventTypeEnum.UPDATE_TAG.getEventType());
//            updateOrganizationEvent.setAddAllowParty(null == addParty ? Lists.newArrayList() : Arrays.stream(addParty.split(",")).collect(Collectors.toList()));
//            updateOrganizationEvent.setReduceAllowParty(null == delParty ? Lists.newArrayList() : Arrays.stream(delParty.split(",")).collect(Collectors.toList()));
//            updateOrganizationEvent.setAddAllowUser(null == addUser ? Lists.newArrayList() : Arrays.stream(addUser.split(",")).collect(Collectors.toList()));
//            updateOrganizationEvent.setReduceAllowUser(null == delUser ? Lists.newArrayList() : Arrays.stream(delUser.split(",")).collect(Collectors.toList()));
//        }
//
//        msg.setBody(updateOrganizationEvent.toProto());
//        SendResult sendResult = qywxEventNotifyMQSender.send(msg);
//        log.info("trace_appId:{} changeContacts from qywx send mq msg sendResult:{}, message:{}, body:{}",contactsInfo.getSuiteId(), sendResult, msg, JSONObject.toJSONString(updateOrganizationEvent));

    }

    public void sendChangeContactsMsg(String outEa, String appId, ContactsXml contactsInfo, String changeType) {
        //查询企业绑定关系
        List<OuterOaEnterpriseBindEntity> enterpriseBindList = outerOaEnterpriseBindManager.getEntitiesByOutEa(ChannelEnum.qywx, outEa, appId);

        if(CollectionUtils.isEmpty(enterpriseBindList)) {
            log.info("CorpManager.sendChangeContactsMsg,enterprise not bind.corpId={}", contactsInfo.getAuthCorpId());
            return;
        }

        ChangeContactEvent event = new ChangeContactEvent();
        event.setAppId(appId);
        event.setChangeType(changeType);
        event.setCorpId(outEa);
        //查询员工绑定关系
        String userId = StringUtils.isEmpty(contactsInfo.getNewUserID()) ? contactsInfo.getUserID(): contactsInfo.getNewUserID();
        event.setUserId(userId);
        for(OuterOaEnterpriseBindEntity enterpriseBind : enterpriseBindList) {
            event.setFsEa(enterpriseBind.getFsEa());
            //查找人员绑定关系
            OuterOaEmployeeBindEntity employeeBindEntity = outerOaEmployeeBindManager.getEmployeeBindEntity(ChannelEnum.qywx, outEa, enterpriseBind.getFsEa(), contactsInfo.getSuiteId(), userId);

            log.info("CorpManager.sendChangeContactsMsg,employeeBindEntity={}",employeeBindEntity);
            //人员没有绑定关系，也发送
            if(ObjectUtils.isNotEmpty(employeeBindEntity)) {
                event.setFsUserId("E." + enterpriseBind.getFsEa() + "." + employeeBindEntity.getFsEmpId());
            }

            Message msg = new Message();
            msg.setTags("create_user".contains(changeType) ? TAG_CREATE_USER : ("update_user".equals(changeType) ? TAG_UPDATE_USER : TAG_DELETE_USER));
            msg.setBody(event.toProto());
            if(ConfigCenter.TEM_CLOUD_EA.contains(enterpriseBind.getFsEa())) {
                CloudMessageProxyProto cloudMessageProxyProto = new CloudMessageProxyProto();
                cloudMessageProxyProto.setType(CloudProxyEnum.qywxEventNotifyMQSender.name());
                cloudMessageProxyProto.setCorpId(outEa);
                cloudMessageProxyProto.setFsEa(enterpriseBind.getFsEa());
                cloudMessageProxyProto.setMessage(msg);
                //跨云
                mqSender.sendCloudProxyMQ(eieaConverter.enterpriseAccountToId(enterpriseBind.getFsEa()), cloudMessageProxyProto);
            } else {
                SendResult sendResult = qywxEventNotifyMQSender.send(msg, outEa);
                log.info("CorpManager.sendChangeContactsMsg,ea={},sendResult={}", enterpriseBind.getFsEa(), sendResult);
            }
        }
    }

    public void changeContacts2(String plainMsg, String event,String appId) {
        log.info("CorpManager.changeContacts2,plainMsg={},event={},appId={}",plainMsg,
                event,appId);
        QYWeiXinMemberEventXml memberEventXml = XStreamUtils.parseXml(plainMsg, QYWeiXinMemberEventXml.class);
        log.info("CorpManager.changeContacts2,memberEventXml={}",memberEventXml);

        String outEa = memberEventXml.getToUserName();
        String outUserId = memberEventXml.getFromUserName();

        //检测到是手动绑定纷享企业的，不发送消息
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByOutEa(ChannelEnum.qywx, outEa, appId);
        log.info("CorpManager.changeContacts2,enterpriseBindEntities={}",enterpriseBindEntities);
        if (enterpriseBindEntities.stream().anyMatch(mapping -> mapping.getBindType().equals(BindTypeEnum.manual))) {
            log.info("CorpManager.changeContacts2,手动绑定不发送消息");
            return ;
        }
        //丢进临时库
        saveAppEvent(outEa, appId, event, plainMsg);


//        OuterOaEnterpriseBindEntity enterpriseBindEntity = enterpriseBindEntities.get(0);
//        String fsEa = enterpriseBindEntity.getFsEa();
//
//        OuterOaEmployeeBindEntity employeeBindEntity = outerOaEmployeeBindManager.getEmployeeBindEntity(ChannelEnum.qywx, outEa, fsEa, appId, outUserId);
//        log.info("CorpManager.changeContacts2,employeeBindEntity={}",employeeBindEntity);
        //CRM应用可见范围新增人员或者可见范围内的部门下新增员工
//        if("subscribe".contains(event)) {
//            //如果员工绑定关系不存在，新增员工并建立绑定关系，如果员工被停用，则恢复员工状态
//            contactsService.addUserList(enterpriseBindEntity, Lists.newArrayList(outUserId));
//        } else if("unsubscribe".equals(event)) { //从应用可见范围移除员工或者可见范围内的部门下移除员工或者员工离职
//            if(ObjectUtils.isNotEmpty(employeeBindEntity)) {
//                //如果员工绑定关系存在，停用员工并更新绑定关系
//                contactsService.stopUserList(enterpriseBindEntity, Lists.newArrayList(outUserId));
//            }
//        }
    }

//    public com.facishare.open.qywx.accountbind.result.Result<Boolean> saveEnterpriseAccountBind(String outEa,
//                                                                                                String depId,
//                                                                                                String fsEa,
//                                                                                                String corpName,
//                                                                                                String isvCorpId) {
//        QyweixinAccountEnterpriseMapping accountEnterpriseMapping = new QyweixinAccountEnterpriseMapping();
//        accountEnterpriseMapping.setFsEa(fsEa);
//        accountEnterpriseMapping.setOutEa(outEa);
//        accountEnterpriseMapping.setQywxDepartmentId(depId);
//        accountEnterpriseMapping.setSource(SourceTypeEnum.QYWX.getSourceType());
//        accountEnterpriseMapping.setStatus(0);
//        accountEnterpriseMapping.setBindType(QyweixinBindTypeEnum.OLD_CORP_BIND.getCode());
//        accountEnterpriseMapping.setOutName(corpName);
//        accountEnterpriseMapping.setIsvOutEa(isvCorpId);
//        accountEnterpriseMapping.setDomain(ConfigCenter.crm_domain);
//        Timestamp timestamp=new Timestamp(new Date().getTime());
//        accountEnterpriseMapping.setGmtCreate(timestamp);
//        accountEnterpriseMapping.setGmtModified(timestamp);
//
//
//        OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity = new OuterOaEnterpriseBindEntity();
//        outerOaEnterpriseBindEntity.setFsEa(fsEa);
//        outerOaEnterpriseBindEntity.setOutEa(outEa);
//        //qywx
//        outerOaEnterpriseBindEntity.setChannel(ChannelEnum.qywx);
//        //默认为crmAppId
//        outerOaEnterpriseBindEntity.setAppId(ConfigCenter.crmAppId);
//        outerOaEnterpriseBindEntity.setBindStatus(BindStatusEnum.normal);
//        outerOaEnterpriseBindEntity.setBindType(BindTypeEnum.manual);
//
//        return qyweixinAccountBindService.bindAccountEnterpriseMapping(outerOaEnterpriseBindEntity);
//    }

    /**
     * 异步通知同步历史待办消息 审批流，工作流消息
     * @param fsEa
     */
    public void notifyHistoryToDoMessage(String fsEa) {
        corpManagerThreadPool.execute(()->{
            try {
                int fsEi = eieaConverter.enterpriseAccountToId(fsEa);
                //同步历史待办消息
                Map<String, String> historyHeadersMap = Maps.newHashMap();
                historyHeadersMap.put("x-fs-ei", String.valueOf(fsEi));
                historyHeadersMap.put("x-fs-userinfo", "-10000");
                //限速
                if(!CrmRateLimiter.isAllowed(null)) {
                    return;
                }
                String historyToDoMessageResult = httpHelper.doGet(historyToDoMessageUrl + fsEi, historyHeadersMap);
                //成功返回：{"value":{"Result":true},"success":true,"message":"","errorCode":0}
                JSONObject historyToDoMessageJsonObject = JSONObject.parseObject(historyToDoMessageResult);
                if(0 == historyToDoMessageJsonObject.getIntValue("errorCode")){
                    if(historyToDoMessageJsonObject.getJSONObject("value").getBoolean("Result")){
                        log.info("trace notifyHistoryToDoMessage_success ei:{} ea:{}", fsEi, fsEa);
                    } else {
                        log.error("trace notifyHistoryToDoMessage_error ei:{} ea:{} result:{}", fsEi, fsEa, historyToDoMessageResult);
                    }
                } else{
                    log.error("trace notifyHistoryToDoMessage_error ei:{} ea:{} result:{}", fsEi, fsEa, historyToDoMessageResult);
                }


                Map<String, String> flowTaskMap = Maps.newHashMap();
                flowTaskMap.put("x-tenant-id", String.valueOf(fsEi));
                flowTaskMap.put("x-user-id", "-10000");
                flowTaskMap.put("Content-type", "application/json");
                //限速
                if(!CrmRateLimiter.isAllowed(null)) {
                    return;
                }
                String flowTaskResult = httpHelper.postJsonData2(flowTaskUrl, null, flowTaskMap);
                //成功返回：{"code":0,"message":"","data":"ok"}
                JSONObject flowTaskJsonObject = JSONObject.parseObject(flowTaskResult);
                if( 0 == flowTaskJsonObject.getIntValue("code")){
                    log.info("trace notifyHistoryToDoMessage_FlowTask_success ei:{} ea:{}", fsEi , fsEa);
                } else {
                    log.error("trace notifyHistoryToDoMessage_FlowTask_success ei:{} ea:{} result:{}", fsEi , fsEa, flowTaskResult);
                }
            } catch (RemoteException e) {
                log.info("trace notifyHistoryToDoMessage_error RemoteException fsEa:{} e:{}", fsEa, e.getMessage());
            } catch (Exception e){
                log.info("trace notifyHistoryToDoMessage_error Exception fsEa:{} e:{} ", fsEa, e.getMessage());
            }
        });
    }

    /**
     * 给绑定渠道的用户发送欢迎消息
     * 场景1 先安装CRM > 安装通讯录 > 给CRM可见范围的员工发送消息
     * 场景2 先安装通讯录 > 再在安装CRM >  给CRM可见范围的员工发送消息
     *
     * @param userIds
     * @param fsEa
     * @param corpId
     */
    private void sendMsg(List<String> userIds, String fsEa, String corpId, String appId) {

        if(StringUtils.isBlank(fsEa) || StringUtils.isBlank(corpId) || null ==userIds || userIds.isEmpty()){
            log.info("trace sendMsg empty parameter fsEa:{} corpId:{} userIds:{}", fsEa, corpId, userIds);
            return;
        }
        corpManagerThreadPool.execute(()->{
            QyweixinSendMsgEvent qyweixinSendMsgEvent = new QyweixinSendMsgEvent();
            qyweixinSendMsgEvent.setAppId(appId);
            qyweixinSendMsgEvent.setCorpId(corpId);
            qyweixinSendMsgEvent.setFsEa(fsEa);
            qyweixinSendMsgEvent.setToUserList(userIds);
            Message msg = new Message();
            msg.setTags(TAG_MESSAGE);
            msg.setBody(qyweixinSendMsgEvent.toProto());
            SendResult sendResult = qywxEventNotifyMQSender.send(msg, corpId);
            log.info("trace_appId:{} sendMsg from qywx send mq msg sendResult:{}, message:{}, body:{} fsEa:{}",
                    appId, sendResult, msg, JSONObject.toJSONString(qyweixinSendMsgEvent), fsEa);
        });
    }

    /**
     * 场景：在安装CRM应用时 》 给绑定通讯录应用的员工发欢迎消息
     * 该方法业务：检测安装了通讯录应用 》 全量给已绑定的用户发欢迎消息
     * @param corpId
     * @param fsEa
     */
//    public void sendMsgAfterInstallCRM(String corpId, String fsEa) {
//        try{
//            String mainAppId = qyweixinGatewayInnerService.getMainAppId(corpId).getData();
//            QyweixinCorpBindBo contactAppBind = new QyweixinCorpBindBo();
//            contactAppBind.setStatus(QyweixinBindStatusEnum.BIND.getCode());
//            contactAppBind.setCorpId(corpId);
//            contactAppBind.setAppId(contactAppId);
//
//            List<QyweixinCorpBindBo> findContactBindResult = qyweixinCorpBindDao.findByEntity(contactAppBind);
//            if(null != findContactBindResult && !findContactBindResult.isEmpty()){
//                List<QyweixinAccountEmployeeMapping> qyweixinAccountEmployeeMappings = qyweixinAccountBindInnerService.queryAccountBindByFsEa2(fsEa,mainAppId, corpId);
//                sendMsg(qyweixinAccountEmployeeMappings.stream().map(v-> v.getFsAccount().substring(v.getFsAccount().lastIndexOf(".")+1)).collect(Collectors.toList()), fsEa, corpId);
//            } else {
//                log.info("trace sendMsgAfterInstallCRM_error not install contactApp corpId:{} fsEa:{}", corpId, fsEa);
//            }
//        }catch (Exception e){
//            log.error("trace sendMsgAfterInstallCRM exception corpId:"+corpId + " fsEa:" +fsEa, e);
//        }
//
//    }

    /**
     * 场景：安装通讯录后 》 绑定账号 触发发送欢迎消息
     * 该方法业务：检测安装了CRM发送消息 》 给已绑定的用户发欢迎消息
     */
    public void sendMsgAfterAccountBind(OuterOaEnterpriseBindEntity enterpriseBindEntity, List<OuterOaEmployeeBindEntity> employeeBindEntities) {
        String corpId = enterpriseBindEntity.getOutEa();
        String fsEa = enterpriseBindEntity.getFsEa();
        String appId = enterpriseBindEntity.getAppId();
        try{
            sendMsg(employeeBindEntities.stream().map(OuterOaEmployeeBindEntity::getFsEmpId).filter(Objects::nonNull).collect(Collectors.toList()), fsEa, corpId, appId);
        }catch (Exception e){
            log.error("trace sendMsgAfterAccountBind exception corpId:"+corpId + " fsEa:" +fsEa, e);
        }
    }

//    public void saveQyweixinAdminMobile(QyweixinUserDetailInfoRsp userDetailInfoRsp, String appId) {
//        log.info("saveQyweixinAdminMobile start. userDetailInfoRsp:{}, appId:{}", userDetailInfoRsp, appId);
//        if (StringUtils.isEmpty(userDetailInfoRsp.getCorpid())) return;
//        corpManagerThreadPool.execute(()->{
//            QyweixinCorpInfoBo qyweixinCorpInfoBo = new QyweixinCorpInfoBo();
//            qyweixinCorpInfoBo.setCorpId(userDetailInfoRsp.getCorpid());
//            List<QyweixinCorpInfoBo> corpInfo = qyweixinCorpInfoDao.findByEntity(qyweixinCorpInfoBo);
//            if( null ==corpInfo || corpInfo.isEmpty()){
//                log.info("trace saveQyweixinAdminMobile get corpInfo empty corpId:{}", userDetailInfoRsp.getCorpid());
//                return;
//            }
//            log.info("trace saveQyweixinAdminMobile createApp admin user_id:{}  login_user_id:{}", corpInfo.get(0).getUserId(), userDetailInfoRsp.getUserid());
//            if(StringUtils.isBlank(corpInfo.get(0).getUserId())){
//                log.error("trace saveQyweixinAdminMobile create app's admin userId is empty corpId:{}", userDetailInfoRsp.getCorpid());
//                return;
//            }
//            if(userDetailInfoRsp.getUserid().equals(corpInfo.get(0).getUserId())){
//                PlatformAppAdminInfoBo platformAppAdminInfoBo = new PlatformAppAdminInfoBo();
//                platformAppAdminInfoBo.setSource(SourceTypeEnum.QYWX.getSourceType());
//                platformAppAdminInfoBo.setAppId(appId);
//                platformAppAdminInfoBo.setOutEa(userDetailInfoRsp.getCorpid());
//                platformAppAdminInfoBo.setMobile(userDetailInfoRsp.getMobile());
//                platformAppAdminInfoBo.setName(userDetailInfoRsp.getName());
//
//                int count = platformAppAdminInfoDao.savePlatformAdminInfo(platformAppAdminInfoBo);
//                log.info("trace saveQyweixinAdminMobile success updateCount:{}", count);
//            }
//        });
//    }

//    public boolean isManualBinding(String corpId) {
//        List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = qyweixinAccountBindInnerService.queryEnterpriseBindByOutEa(corpId);
//        if(CollectionUtils.isNotEmpty(enterpriseMappingList)) {
//            QyweixinAccountEnterpriseMapping enterpriseMapping = enterpriseMappingList.get(0);
//            if(null != enterpriseMapping && QyweixinBindTypeEnum.OLD_CORP_BIND.getCode().equals(enterpriseMapping.getBindType())){
//                log.info("isManualBinding=true, 手动绑定不发送消息", QyweixinBindTypeEnum.OLD_CORP_BIND.getCode());
//                return true;
//            }
//        }
//        return false;
//    }

//    /**
//     * 保存代开发应用授权
//     * @param qyweixinGetPermenantCodeRsp
//     * @param appId
//     */
//    public void saveRepInfoTask(QyweixinGetPermenantCodeRsp qyweixinGetPermenantCodeRsp, String appId) {
//        log.info("CorpManager.saveRepInfoTask,qyweixinGetPermenantCodeRsp={},appId={}", qyweixinGetPermenantCodeRsp, appId);
//        String corpId = qyweixinGetPermenantCodeRsp.getAuth_corp_info().getCorpid();
////        //明文corpId转换为加密的corpId
////        String isvCorpId = qyWeixinManager.getIsvCorpId(corpId);
//        String isvCorpId = corpId;
//        //保存绑定关系或更新
//        QyweixinCorpBindBo qyweixinCorpBindBo = qyweixinCorpBindDao.queryQyweixinCorpBind(corpId, appId);
//        if (null == qyweixinCorpBindBo) {
//            qyweixinCorpBindBo = new QyweixinCorpBindBo();
//            qyweixinCorpBindBo.setCorpId(corpId);
//            qyweixinCorpBindBo.setCorpName(qyweixinGetPermenantCodeRsp.getAuth_corp_info().getCorp_name());
//            qyweixinCorpBindBo.setAppId(appId);
//            qyweixinCorpBindBo.setPermanentCode(qyweixinGetPermenantCodeRsp.getPermanent_code());
//            qyweixinCorpBindBo.setAgentId(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().isEmpty() ? null : String.valueOf(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().get(0).getAgentid()));
//            qyweixinCorpBindBo.setStatus(QyweixinBindStatusEnum.BIND.getCode());
//            qyweixinCorpBindBo.setIsvCorpId(isvCorpId);
//            int count = qyweixinCorpBindDao.save(qyweixinCorpBindBo);
//            log.info("CorpManager.saveRepInfoTask,save,count={},qyweixinCorpBindBo={}", count,qyweixinCorpBindBo);
//            //第一次授权代开发时发送mq
//            String ea = qyweixinCorpBindDao.getFsEaByOutEa(corpId, ConfigCenter.crm_domain);
//            log.info("CorpManager.saveRepInfoTask,ea={}", ea);
//            if(StringUtils.isNotEmpty(ea)) {
//                messageGeneratingService.sendMessage(ea);
//            }
//        } else {
//            qyweixinCorpBindBo.setStatus(QyweixinBindStatusEnum.BIND.getCode());
//            qyweixinCorpBindBo.setCorpName(qyweixinGetPermenantCodeRsp.getAuth_corp_info().getCorp_name());
//            qyweixinCorpBindBo.setAgentId(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().isEmpty() ? null : String.valueOf(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().get(0).getAgentid()));
//            qyweixinCorpBindBo.setPermanentCode(qyweixinGetPermenantCodeRsp.getPermanent_code());
//            qyweixinCorpBindBo.setIsvCorpId(isvCorpId);
//            int count = qyweixinCorpBindDao.update(qyweixinCorpBindBo);
//            log.info("CorpManager.saveRepInfoTask,update,count={},qyweixinCorpBindBo={}",count,qyweixinCorpBindBo);
//        }
//        //判断会话存档是否需要保存代开发的corpSecret和agentId
//        this.updateCorpMessageGenerating(corpId, qyweixinGetPermenantCodeRsp);
//        qyWeixinManager.getToken(qyweixinGetPermenantCodeRsp.getPermanent_code(), corpId);
//    }
//
//    public void saveOrUpdateCorpBindData(List<QyweixinCorpBindBo> corpBindBoList) {
//        for(QyweixinCorpBindBo corpBindBo : corpBindBoList) {
//            QyweixinCorpBindBo qyweixinCorpBindBo = qyweixinCorpBindDao.queryQyweixinCorpBind(corpBindBo.getCorpId(), corpBindBo.getAppId());
//            if (null == qyweixinCorpBindBo) {
//                qyweixinCorpBindBo = new QyweixinCorpBindBo();
//                qyweixinCorpBindBo.setCorpId(corpBindBo.getCorpId());
//                qyweixinCorpBindBo.setCorpName(corpBindBo.getCorpName());
//                qyweixinCorpBindBo.setAppId(corpBindBo.getAppId());
//                qyweixinCorpBindBo.setPermanentCode(corpBindBo.getPermanentCode());
//                qyweixinCorpBindBo.setAgentId(corpBindBo.getAgentId());
//                qyweixinCorpBindBo.setStatus(corpBindBo.getStatus());
//                qyweixinCorpBindBo.setIsvCorpId(corpBindBo.getIsvCorpId());
//                int count = qyweixinCorpBindDao.save(qyweixinCorpBindBo);
//                log.info("CorpManager.saveRepInfoTask,save,count={},corpBindBo={}", count,corpBindBo);
//            } else {
//                corpBindBo.setId(qyweixinCorpBindBo.getId());
//                int count = qyweixinCorpBindDao.update(corpBindBo);
//                log.info("CorpManager.saveRepInfoTask,update,count={},corpBindBo={}",count,corpBindBo);
//            }
//        }
//    }
//
//    public void saveOrUpdateCorpInfoData(List<QyweixinCorpInfoBo> corpInfoBoList) {
//        for(QyweixinCorpInfoBo corpInfoBo : corpInfoBoList) {
//            //保存企业信息或更新
//            QyweixinCorpInfoBo qyweixinCorpInfoBo = new QyweixinCorpInfoBo();
//            qyweixinCorpInfoBo.setCorpId(corpInfoBo.getCorpId());
//            List<QyweixinCorpInfoBo> qyweixinCorpInfoList = qyweixinCorpInfoDao.findByEntity(qyweixinCorpInfoBo);
//            if(null == qyweixinCorpInfoList || qyweixinCorpInfoList.isEmpty()){
//                int save = qyweixinCorpInfoDao.save(corpInfoBo);
//                log.info("CorpManager.saveOrUpdateCorpInfoData,save,count={},corpInfoBo={}", save,corpInfoBo);
//            } else {
//                int update = qyweixinCorpInfoDao.update(corpInfoBo);
//                log.info("CorpManager.saveOrUpdateCorpInfoData,update,count={},corpInfoBo={}", update,corpInfoBo);
//            }
//        }
//    }

//    public void updateCorpMessageGenerating(String corpId, QyweixinGetPermenantCodeRsp qyweixinGetPermenantCodeRsp) {
//        com.facishare.open.qywx.accountbind.result.Result<String> result = qyweixinAccountBindService.outEaToFsEa("qywx", corpId,null);
//        String ea = result.getData();
//        com.facishare.open.qywx.save.result.Result<GenerateSettingVo> generateSettingVoResult = messageGeneratingService.querySetting(ea, null, corpId);
//        if(ObjectUtils.isNotEmpty(generateSettingVoResult.getData()) &&
//                StringUtils.isEmpty(generateSettingVoResult.getData().getCorpSecret()) &&
//                StringUtils.isEmpty(generateSettingVoResult.getData().getAgentId())) {
//            GenerateSettingVo generateSettingVo = new GenerateSettingVo();
//            generateSettingVo.setQywxCorpId(corpId);
//            generateSettingVo.setCorpSecret(qyweixinGetPermenantCodeRsp.getPermanent_code());
//            generateSettingVo.setAgentId(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().isEmpty() ? null : String.valueOf(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().get(0).getAgentid()));
//            log.info("CorpManager.updateCorpMessageGenerating,generateSettingVo={}", generateSettingVo);
//            messageGeneratingService.updateCorpRepSecret(generateSettingVo);
//        }
//    }
//
//    public void updateCorpMessageGenerating(String corpId, QyweixinCorpBindBo qyweixinCorpBindBo) {
//        com.facishare.open.qywx.accountbind.result.Result<String> result = qyweixinAccountBindService.outEaToFsEa("qywx", corpId,null);
//        String ea = result.getData();
//        com.facishare.open.qywx.save.result.Result<GenerateSettingVo> generateSettingVoResult = messageGeneratingService.querySetting(ea, null, corpId);
//        if(ObjectUtils.isNotEmpty(generateSettingVoResult.getData()) && qyweixinCorpBindBo.getPermanentCode().equals(generateSettingVoResult.getData().getCorpSecret())) {
//            GenerateSettingVo generateSettingVo = new GenerateSettingVo();
//            generateSettingVo.setQywxCorpId(corpId);
//            generateSettingVo.setCorpSecret(null);
//            generateSettingVo.setAgentId(null);
//            log.info("CorpManager.updateCorpMessageGenerating,generateSettingVo={}", generateSettingVo);
//            messageGeneratingService.updateCorpRepSecret(generateSettingVo);
//        }
//    }

//    public void autoBindAccountEnterprise(String fsEa, String corpId, List<String> userIds) {
//        log.info("CorpManager.autoBindAccountEnterprise,fsEa={},corpId={},userIds={}.", fsEa, corpId, userIds);
//        List<QyweixinAccountEmployeeMapping> employeeMappingList = new LinkedList<>();
//        for(String userId : userIds) {
//            QyweixinAccountEmployeeMapping employeeMapping = new QyweixinAccountEmployeeMapping();
//            //通过代开发获取名称
//            com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> qyweixinUserInfoResult = qyWeixinManager.getUserInfo(repAppId, corpId, userId);
//            log.info("CorpManager.autoBindAccountEnterprise,qyweixinUserInfo={}.", qyweixinUserInfoResult);
//            if(!qyweixinUserInfoResult.isSuccess() || ObjectUtils.isEmpty(qyweixinUserInfoResult.getData())) {
//                continue;
//            }
//            QyweixinUserDetailInfoRsp qyweixinUserInfo = qyweixinUserInfoResult.getData();
//            //通过名称获取纷享CRM的员工的详情
//            GetEmployeesDtoByNameResult fsEmployeeInfo = fsManager.getEmployeesByName(fsEa, qyweixinUserInfo.getName());
//            log.info("CorpManager.autoBindAccountEnterprise,fsEmployeeInfo={}.", fsEmployeeInfo);
//            if(ObjectUtils.isEmpty(fsEmployeeInfo) || ObjectUtils.isEmpty(fsEmployeeInfo.getEmployeeDto())) {
//                continue;
//            }
//            //匹配
//            employeeMapping.setFsAccount("E." + fsEa + "." + fsEmployeeInfo.getEmployeeDto().getEmployeeId());
//            employeeMapping.setOutAccount(userId);
//            employeeMapping.setIsvAccount(userId);
//            employeeMapping.setOutEa(corpId);
//            employeeMapping.setSource(SourceTypeEnum.QYWX.getSourceType());
//            employeeMappingList.add(employeeMapping);
//        }
//        log.info("CorpManager.autoBindAccountEnterprise,employeeMappingList={}.", employeeMappingList);
//        //入库
//        if(CollectionUtils.isNotEmpty(employeeMappingList)) {
//            qyweixinAccountBindService.bindAccountEmployeeMapping(employeeMappingList);
//        }
//    }

    public void autoBindAccountEnterprise2(OuterOaEnterpriseBindEntity enterpriseBindEntity, List<QyweixinUserDetailInfoRsp> qyweixinUserInfos) {
        log.info("CorpManager.autoBindAccountEnterprise2,enterpriseBindEntity={},qyweixinUserInfos={}.", enterpriseBindEntity, qyweixinUserInfos);
        String dcId = enterpriseBindEntity.getId();
        String fsEa = enterpriseBindEntity.getFsEa();
        String corpId = enterpriseBindEntity.getOutEa();
        String appId = enterpriseBindEntity.getAppId();
        //过滤已绑定的员工
        List<OuterOaEmployeeBindEntity> outerOaEmployeeBindEntities = outerOaEmployeeBindManager.batchGetEmployeeBindEntity(ChannelEnum.qywx, corpId, fsEa, appId, null, null);
        log.info("CorpManager.autoBindAccountEnterprise2,outerOaEmployeeBindEntities={}.", outerOaEmployeeBindEntities);
        Set<String> outAccountsWithBind = outerOaEmployeeBindEntities.stream()
                .map(OuterOaEmployeeBindEntity::getOutEmpId)
                .collect(Collectors.toSet());
        qyweixinUserInfos = qyweixinUserInfos.stream()
                .filter(v -> !outAccountsWithBind.contains(v.getUserid()))
                .collect(Collectors.toList());
        log.info("CorpManager.autoBindAccountEnterprise2,qyweixinUserInfos={}.", qyweixinUserInfos);
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(qyweixinUserInfos)) {
            return;
        }
        List<OuterOaEmployeeBindEntity> outerOaEmployeeBindList = new LinkedList<>();
        for(QyweixinUserDetailInfoRsp qyweixinUserInfo : qyweixinUserInfos) {
            OuterOaEmployeeBindEntity outerOaEmployeeBind = new OuterOaEmployeeBindEntity();
            //通过名称获取纷享CRM的员工的详情
            int fsId = 0;
            FindEmployeeDtoByFullNameResult fsEmployeeInfoByFullName = fsManager.getEmployeesByFullName(fsEa, qyweixinUserInfo.getName());
            if(ObjectUtils.isNotEmpty(fsEmployeeInfoByFullName) && CollectionUtils.isNotEmpty(fsEmployeeInfoByFullName.getEmployeeDtos())) {
                fsId = fsEmployeeInfoByFullName.getEmployeeDtos().get(0).getEmployeeId();
            } else {
                GetEmployeesDtoByNameResult fsEmployeeInfoByName = fsManager.getEmployeesByName(fsEa, qyweixinUserInfo.getName());
                if(ObjectUtils.isNotEmpty(fsEmployeeInfoByName) && ObjectUtils.isNotEmpty(fsEmployeeInfoByName.getEmployeeDto())) {
                    fsId = fsEmployeeInfoByName.getEmployeeDto().getEmployeeId();
                }
            }
            log.info("CorpManager.autoBindAccountEnterprise2,ea={},qyweixinUserInfo={},fsId={}.", fsEa, qyweixinUserInfo, fsId);
            if(fsId == 0) {
                continue;
            }
            //企业微信可以有重名的用户，虽然自动绑定的前提是不允许重名，但是有可能添加了重名的用户，这里还是得做一次判断
            Set<String> fsAccountsWithBind = outerOaEmployeeBindEntities.stream()
                    .map(OuterOaEmployeeBindEntity::getFsEmpId)
                    .collect(Collectors.toSet());
            log.info("CorpManager.autoBindAccountEnterprise2,fsAccountsWithBind={}.", fsAccountsWithBind);
            if(fsAccountsWithBind.contains(String.valueOf(fsId))) {
                continue;
            }
            Set<String> fsAccountsWithNotBind = outerOaEmployeeBindList.stream()
                    .map(OuterOaEmployeeBindEntity::getFsEmpId)
                    .collect(Collectors.toSet());
            log.info("CorpManager.autoBindAccountEnterprise2,fsAccountsWithNotBind={}.", fsAccountsWithNotBind);
            if(fsAccountsWithNotBind.contains(String.valueOf(fsId))) {
                continue;
            }
            //匹配
            outerOaEmployeeBind.setChannel(ChannelEnum.qywx);
            outerOaEmployeeBind.setFsEa(fsEa);
            outerOaEmployeeBind.setOutEa(corpId);
            outerOaEmployeeBind.setDcId(dcId);
            outerOaEmployeeBind.setFsEmpId(String.valueOf(fsId));
            outerOaEmployeeBind.setOutEmpId(qyweixinUserInfo.getUserid());
            outerOaEmployeeBind.setBindStatus(BindStatusEnum.normal);
            outerOaEmployeeBind.setAppId(appId);
            outerOaEmployeeBind.setCreateTime(System.currentTimeMillis());
            outerOaEmployeeBind.setUpdateTime(System.currentTimeMillis());
            outerOaEmployeeBindList.add(outerOaEmployeeBind);
        }
        log.info("CorpManager.autoBindAccountEnterprise2,outerOaEmployeeBindList={}.", outerOaEmployeeBindList);
        //入库
        if(CollectionUtils.isNotEmpty(outerOaEmployeeBindList)) {
            qyweixinAccountBindService.bindAccountEmployeeMapping(outerOaEmployeeBindList);
        }
    }

    public void autoBindAccountEnterprise3(OuterOaEnterpriseBindEntity enterpriseBindEntity, List<QyweixinUserDetailInfoRsp> qyweixinUserInfos, EmployeeDto employeeDto) {
        log.info("CorpManager.autoBindAccountEnterprise3,enterpriseBindEntity={},qyweixinUserInfos={},employeeDto={}.", enterpriseBindEntity, qyweixinUserInfos, employeeDto);
        String dcId = enterpriseBindEntity.getId();
        String fsEa = enterpriseBindEntity.getFsEa();
        String corpId = enterpriseBindEntity.getOutEa();
        String appId = enterpriseBindEntity.getAppId();
        List<OuterOaEmployeeBindEntity> employeeBindEntities = new LinkedList<>();

        //先查询名称，再查询昵称
        List<QyweixinUserDetailInfoRsp> userInfos = null;
        if(StringUtils.isNotEmpty(employeeDto.getFullName())) {
            userInfos = qyweixinUserInfos.stream().filter(v -> StringUtils.isNotEmpty(v.getName()) && v.getName().equals(employeeDto.getFullName())).collect(Collectors.toList());
        }
        if(CollectionUtils.isEmpty(userInfos) && StringUtils.isNotEmpty(employeeDto.getName())) {
            userInfos = qyweixinUserInfos.stream().filter(v -> StringUtils.isNotEmpty(v.getName()) && v.getName().equals(employeeDto.getName())).collect(Collectors.toList());
        }
        log.info("CorpManager.autoBindAccountEnterprise3,fsEa={},userInfos={}.", fsEa, userInfos);
        if(CollectionUtils.isEmpty(userInfos)) {
            return;
        }
        OuterOaEmployeeBindEntity employeeBindEntity = new OuterOaEmployeeBindEntity();
        //匹配
        employeeBindEntity.setChannel(ChannelEnum.qywx);
        employeeBindEntity.setFsEa(fsEa);
        employeeBindEntity.setDcId(dcId);
        employeeBindEntity.setFsEmpId(employeeDto.getEmployeeId() + "");
        employeeBindEntity.setOutEmpId(userInfos.get(0).getUserid());
        employeeBindEntity.setOutEa(corpId);
        employeeBindEntity.setAppId(appId);
        employeeBindEntity.setBindStatus(BindStatusEnum.normal);
        employeeBindEntity.setCreateTime(System.currentTimeMillis());
        employeeBindEntity.setUpdateTime(System.currentTimeMillis());
        employeeBindEntities.add(employeeBindEntity);
        log.info("CorpManager.autoBindAccountEnterprise3,employeeBindEntities={}.", employeeBindEntities);
        //入库
        if(CollectionUtils.isNotEmpty(employeeBindEntities)) {
            qyweixinAccountBindService.bindAccountEmployeeMapping(employeeBindEntities);
        }
    }

    public void autoBindEmpAccount(OuterOaEnterpriseBindEntity enterpriseBindEntity, List<QyweixinUserDetailInfoRsp> qyweixinUserInfos) {
        String fsEa = enterpriseBindEntity.getFsEa();
        String corpId = enterpriseBindEntity.getOutEa();
        String appId = enterpriseBindEntity.getAppId();
        String dcId = enterpriseBindEntity.getId();
        log.info("CorpManager.autoBindEmpAccount,fsEa={},corpId={},qyweixinUserInfos={}.", fsEa, corpId, qyweixinUserInfos);
        //过滤已绑定的员工
        List<OuterOaEmployeeBindEntity> outerOaEmployeeBindEntities = outerOaEmployeeBindManager.batchGetEmployeeBindEntity(ChannelEnum.qywx, corpId, fsEa, appId, null, null);
        log.info("CorpManager.autoBindEmpAccount,outerOaEmployeeBindEntities={}.", outerOaEmployeeBindEntities);
        Set<String> outAccountsWithBind = outerOaEmployeeBindEntities.stream()
                .map(OuterOaEmployeeBindEntity::getOutEmpId)
                .collect(Collectors.toSet());
        qyweixinUserInfos = qyweixinUserInfos.stream()
                .filter(v -> !outAccountsWithBind.contains(v.getUserid()))
                .collect(Collectors.toList());
        log.info("CorpManager.autoBindEmpAccount,qyweixinUserInfos={}.", qyweixinUserInfos);
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(qyweixinUserInfos)) {
            return;
        }
        Map<String, Map<String, String>> autoBindEmpEnterpriseMap = new Gson().fromJson(ConfigCenter.EXTATTR_AUTO_BIND_EMP_ENTERPRISE, new TypeToken<Map<String, Map<String, String>>>() {
        }.getType());
        List<OuterOaEmployeeBindEntity> outerOaEmployeeBindList = new LinkedList<>();
        int ei = eieaConverter.enterpriseAccountToId(fsEa);
        for(QyweixinUserDetailInfoRsp qyweixinUserInfo : qyweixinUserInfos) {
            OuterOaEmployeeBindEntity outerOaEmployeeBind = new OuterOaEmployeeBindEntity();
            outerOaEmployeeBind.setId(IdGenerator.get());
            String fsId = null;
            String attrsText = null;
            //先看看是否有自定义字段
            if(ObjectUtils.isNotEmpty(qyweixinUserInfo.getExtattr()) && CollectionUtils.isNotEmpty(qyweixinUserInfo.getExtattr().getAttrs())) {
                for(QyweixinUserDetailInfoRsp.AttrsInfo attrsInfo : qyweixinUserInfo.getExtattr().getAttrs()) {
                    if(attrsInfo.getType() == 0 && attrsInfo.getName().equals(autoBindEmpEnterpriseMap.get(fsEa).get("QYWX_EXTATTR"))) {
                        attrsText = attrsInfo.getText().getValue();
                        break;
                    }
                }
            }
            if(StringUtils.isEmpty(attrsText)) {
                continue;
            }
            //根据自定义字段查询crm对象
            String fsPersonnelObject = fsManager.queryFsObject(ei, autoBindEmpEnterpriseMap.get(fsEa).get("FS_API_NAME"), attrsText, "PersonnelObj");
            if(StringUtils.isNotEmpty(fsPersonnelObject)) {
                JSONArray read = (JSONArray) JSONPath.read(fsPersonnelObject, "$.data.dataList");
                if(read.size()!=0){
                    fsId = JSONPath.read(fsPersonnelObject, "$.data.dataList[0].user_id").toString();
                }
            }
            log.info("CorpManager.autoBindEmpAccount,ea={},qyweixinUserInfo={},fsId={}.", fsEa, qyweixinUserInfo, fsId);
            if(StringUtils.isEmpty(fsId)) {
                continue;
            }
            //企业微信可以有重名的用户，虽然自动绑定的前提是不允许重名，但是有可能添加了重名的用户，这里还是得做一次判断
            Set<String> fsAccountsWithBind = outerOaEmployeeBindEntities.stream()
                    .map(OuterOaEmployeeBindEntity::getFsEmpId)
                    .collect(Collectors.toSet());
            log.info("CorpManager.autoBindEmpAccount,fsAccountsWithBind={}.", fsAccountsWithBind);
            if(fsAccountsWithBind.contains(fsId)) {
                continue;
            }
            Set<String> fsAccountsWithNotBind = outerOaEmployeeBindList.stream()
                    .map(OuterOaEmployeeBindEntity::getFsEmpId)
                    .collect(Collectors.toSet());
            log.info("CorpManager.autoBindEmpAccount,fsAccountsWithNotBind={}.", fsAccountsWithNotBind);
            if(fsAccountsWithNotBind.contains(fsId)) {
                continue;
            }
            //匹配
            outerOaEmployeeBind.setChannel(ChannelEnum.qywx);
            outerOaEmployeeBind.setFsEa(fsEa);
            outerOaEmployeeBind.setOutEa(corpId);
            outerOaEmployeeBind.setDcId(dcId);
            outerOaEmployeeBind.setFsEmpId(fsId);
            outerOaEmployeeBind.setOutEmpId(qyweixinUserInfo.getUserid());
            outerOaEmployeeBind.setBindStatus(BindStatusEnum.normal);
            outerOaEmployeeBind.setAppId(appId);
            outerOaEmployeeBind.setCreateTime(System.currentTimeMillis());
            outerOaEmployeeBind.setUpdateTime(System.currentTimeMillis());
            outerOaEmployeeBindList.add(outerOaEmployeeBind);
        }
        log.info("CorpManager.autoBindEmpAccount,outerOaEmployeeBindList={}.", outerOaEmployeeBindList);
        //入库
        if(CollectionUtils.isNotEmpty(outerOaEmployeeBindList)) {
            qyweixinAccountBindService.bindAccountEmployeeMapping(outerOaEmployeeBindList);
        }
    }

    public void enterpriseBind(String corpId, String fsEa, String appId, BindStatusEnum bindStatus) {
        // 绑定新建企业
        OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity = new OuterOaEnterpriseBindEntity();
        outerOaEnterpriseBindEntity.setFsEa(fsEa);
        outerOaEnterpriseBindEntity.setOutEa(corpId);
        //qywx
        outerOaEnterpriseBindEntity.setChannel(ChannelEnum.qywx);
        //appId
        outerOaEnterpriseBindEntity.setAppId(appId);
        outerOaEnterpriseBindEntity.setBindStatus(bindStatus);
        outerOaEnterpriseBindEntity.setBindType(BindTypeEnum.auto);
        outerOaEnterpriseBindEntity.setCreateTime(System.currentTimeMillis());
        outerOaEnterpriseBindEntity.setUpdateTime(System.currentTimeMillis());
        com.facishare.open.qywx.accountbind.result.Result<Boolean> result = qyweixinAccountBindService.bindAccountEnterpriseMapping(outerOaEnterpriseBindEntity);
        LogUtils.info("CorpManager.enterpriseBind,bindAccountEnterpriseMapping,result={}", result);
    }

    public void updateEnterpriseBindStatus(String fsEa,BindStatusEnum status) {
        com.facishare.open.qywx.accountbind.result.Result<Integer> result = qyweixinAccountBindService.updateEnterpriseBindStatus(fsEa, status);
        LogUtils.info("CorpManager.updateEnterpriseBindStatus,result={}", result);
    }

    public void employeeBind(String corpId,String fsEa,String appId,String outUserId,BindStatusEnum status) {
        OuterOaEnterpriseBindEntity enterpriseBindEntity = outerOaEnterpriseBindManager.getEntity(ChannelEnum.qywx, fsEa, corpId, appId);
        String dcId = enterpriseBindEntity.getId();
        OuterOaEmployeeBindEntity arg = new OuterOaEmployeeBindEntity();
        arg.setChannel(ChannelEnum.qywx);
        arg.setFsEa(fsEa);
        arg.setOutEa(corpId);
        arg.setDcId(dcId);
        arg.setFsEmpId(String.valueOf(ConfigCenter.FIRST_EMPLOYEE_ID));
        arg.setOutEmpId(outUserId);
        arg.setAppId(appId);
        arg.setBindStatus(status);
        arg.setCreateTime(System.currentTimeMillis());
        arg.setUpdateTime(System.currentTimeMillis());

        com.facishare.open.qywx.accountbind.result.Result<Boolean> result = qyweixinAccountBindService.bindAccountEmployeeMapping(Lists.newArrayList(arg));
        LogUtils.info("CorpManager.employeeBind,bindAccountEmployeeMapping,result={}", result);
    }

    public void updateEmployeeBindStatus(String fsEa, String fsEmpId, BindStatusEnum status) {
        List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.getEmployeeBindEntitiesByFsEmpId(ChannelEnum.qywx, fsEa, fsEmpId);
        if (CollectionUtils.isEmpty(employeeBindEntities)) {
            return;
        }
        OuterOaEmployeeBindEntity employeeBindEntity = employeeBindEntities.get(0);
        employeeBindEntity.setBindStatus(status);
        com.facishare.open.qywx.accountbind.result.Result<Integer> result = qyweixinAccountBindService.updateEmployeeBindStatus(employeeBindEntity);
        LogUtils.info("CorpManager.updateEmployeeBindStatus,result={}", result);
    }

    public void updateEmployeeBindStatus(OuterOaEmployeeBindEntity employeeBindEntity) {
        com.facishare.open.qywx.accountbind.result.Result<Integer> result = qyweixinAccountBindService.updateEmployeeBindStatus(employeeBindEntity);
        LogUtils.info("CorpManager.updateEmployeeBindStatus,result={}", result);
    }

//    /**
//     * 代开发应用是否安装
//     * @param outEa
//     * @return
//     */
//    public boolean isRepAppInstalled(String outEa) {
//        QyweixinCorpBindBo corpBindBo = qyweixinCorpBindDao.queryQyweixinCorpBind(outEa, repAppId);
//        if(corpBindBo!=null && corpBindBo.getStatus()==0) return true;
//        return false;
//    }
    public IntelligentAppInfoResult queryIntelligentBind(String fsea, String sessionAppId) {
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsea).bindStatus(BindStatusEnum.normal).build());
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return null;
        }

        OuterOaEnterpriseBindEntity enterpriseBindEntity = null;
        for (OuterOaEnterpriseBindEntity oaEnterpriseBindEntity : enterpriseBindEntities) {
            OuterOaConfigInfoEntity configInfoEntity = outerOaConfigInfoManager.getEntityByDataCenterId(OuterOaConfigInfoTypeEnum.CONVERSATION_ARCHIVE_CONFIG, oaEnterpriseBindEntity.getId());
            if (ObjectUtils.isEmpty(configInfoEntity)){
                continue;
            }

            ConversationArchiveInfo conversationArchiveInfo = JSON.parseObject(configInfoEntity.getConfigInfo(), ConversationArchiveInfo.class);
            if (conversationArchiveInfo.getIsOpen()) {
                enterpriseBindEntity = oaEnterpriseBindEntity;
                break;
            }
        }

        if (ObjectUtils.isEmpty(enterpriseBindEntity)){
            return null;
        }

        String finalAppId = enterpriseBindEntity.getAppId();
        //查看是否有repAppId
        if (finalAppId.equals(ConfigCenter.crmAppId)) {
            OuterOaAppInfoEntity repAppInfoEntity = outerOaAppInfoManager.getEntity(ChannelEnum.qywx, enterpriseBindEntity.getOutEa(), ConfigCenter.repAppId);
            if (ObjectUtils.isNotEmpty(repAppInfoEntity) && repAppInfoEntity.getStatus() == OuterOaAppInfoStatusEnum.normal) {
                log.info("QyweixinGatewayInnerServiceImpl.getGroupChatDetail2,repAppInfoEntity={}", repAppInfoEntity);
                finalAppId = ConfigCenter.repAppId;
            }
        }

        OuterOaAppInfoEntity appInfoEntity = outerOaAppInfoManager.getEntity(ChannelEnum.qywx, enterpriseBindEntity.getOutEa(), finalAppId);
        if (ObjectUtils.isEmpty(appInfoEntity)) {
            return null;
        }
        QyweixinAppInfoParams qyweixinAppInfoParams = JSON.parseObject(appInfoEntity.getAppInfo(), QyweixinAppInfoParams.class);

        IntelligentAppInfoResult intelligentAppInfoResult=new IntelligentAppInfoResult();
        intelligentAppInfoResult.setAgentId(String.valueOf(qyweixinAppInfoParams.getAuthAppInfo().getAgentId()));
        intelligentAppInfoResult.setQywxCorpId(enterpriseBindEntity.getOutEa());
        intelligentAppInfoResult.setFsEa(fsea);
        intelligentAppInfoResult.setAppId(finalAppId);
        return intelligentAppInfoResult;

//        if(StringUtils.isEmpty(sessionAppId)){
//            sessionAppId = ConfigCenter.repAppId;
//        }
//        String outEaByFsEa = qyweixinCorpBindDao.getOutEaByFsEa(fsea, ConfigCenter.crm_domain);
//        QyweixinCorpBindBo corpBindBo = qyweixinCorpBindDao.queryQyweixinCorpBind(outEaByFsEa, sessionAppId);
//        if(corpBindBo==null && corpBindBo.getStatus()!=0){
//            return null;
//        }
//        IntelligentAppInfoResult intelligentAppInfoResult=new IntelligentAppInfoResult();
//        intelligentAppInfoResult.setAgentId(corpBindBo.getAgentId());
//        intelligentAppInfoResult.setQywxCorpId(outEaByFsEa);
//        intelligentAppInfoResult.setFsEa(fsea);
//        intelligentAppInfoResult.setAppId(corpBindBo.getAppId());
//        return intelligentAppInfoResult;
    }
//    public Integer cancelCorpAuth(String corpId, String appId) {
//        //停用
//        OuterOaAppInfoEntity outerOaAppInfo = outerOaAppInfoManager.getEntity(ChannelEnum.qywx, corpId, appId);
//        log.info("CorpManager.cancelCorpAuth,outerOaAppInfo={}",outerOaAppInfo);
//        if (ObjectUtils.isEmpty(outerOaAppInfo)) {
//            return 0;
//        }
//
//        outerOaAppInfo.setStatus(OuterOaAppInfoStatusEnum.stop);
//        Integer count = outerOaAppInfoManager.updateById(outerOaAppInfo);
//        log.info("CorpManager.cancelCorpAuth,count={}",count);
//        return count;
//    }

    private void saveAppEvent(String outEa, String appId, String infoType, String plainMsg) {
        log.info("CorpManager.saveAppEvent,outEa={}, appId={}, infoType={}, plainMsg={}", outEa, appId, infoType, plainMsg);


        OaConnectorSyncEventDataDoc dataDoc = new OaConnectorSyncEventDataDoc();
        dataDoc.setId(ObjectId.get());
        dataDoc.setChannel(ChannelEnum.qywx);
        dataDoc.setAppId(appId);
        dataDoc.setOutEa(outEa);
        dataDoc.setEventType(infoType);
        dataDoc.setEvent(plainMsg);
        dataDoc.setStatus(0);
        dataDoc.setCreateTime(System.currentTimeMillis());
        dataDoc.setUpdateTime(System.currentTimeMillis());
        BulkWriteResult bulkWriteResult = oaConnectorSyncEventDataMongoDao.batchReplace(Lists.newArrayList(dataDoc));
        log.info("CorpManager.saveAppEvent,bulkWriteResult={}", bulkWriteResult);
    }
}
