package com.facishare.open.qywx.web.arg;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * HTTP响应封装类
 * 用于完整地传递HTTP响应信息，包括状态码、响应头和响应体
 */
@Data
public class HttpResponse implements Serializable {
    
    /**
     * HTTP状态码
     */
    private int statusCode;
    
    /**
     * 状态消息
     */
    private String statusMessage;
    
    /**
     * 响应头
     */
    private Map<String, String> headers;
    
    /**
     * 响应体内容
     */
    private String body;
    
    /**
     * Content-Type
     */
    private String contentType;
    
    /**
     * 字符编码
     */
    private String charset;
}
