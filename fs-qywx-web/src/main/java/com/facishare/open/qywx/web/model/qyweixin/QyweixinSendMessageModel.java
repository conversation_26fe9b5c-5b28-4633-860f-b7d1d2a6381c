package com.facishare.open.qywx.web.model.qyweixin;

import lombok.Data;

import java.io.Serializable;

@Data
public class QyweixinSendMessageModel implements Serializable {

    // 指定接收消息的成员，成员ID列表（多个接收者用‘|’分隔，最多支持1000个）
    private String touser;

    // 指定接收消息的部门，部门ID列表，多个接收者用‘|’分隔，最多支持100个
    private String toparty;

    // 指定接收消息的标签，标签ID列表，多个接收者用‘|’分隔，最多支持100个
    private String totag;

    // 消息类型
    private String msgtype;

    // 企业应用的id，整型
    private int agentid;

    // 消息内容
    private TextMsgContent text;

    //卡片消息
    private TextCardMsgContent textcard;

    // 表示是否是保密消息，0表示可对外分享，1表示不能分享且内容显示水印，默认为0
    private int safe;

    // 表示是否开启id转译，0表示否，1表示是，默认0
    private int enable_id_trans;

    // 表示是否开启重复消息检查，0表示否，1表示是，默认0
    private int enable_duplicate_check;

    // 表示是否重复消息检查的时间间隔，默认1800s，最大不超过4小时
    private int duplicate_check_interval;

    @Data
    public static class TextMsgContent implements Serializable {

        private static final long serialVersionUID = -5693155950325542781L;
        private String content;
    }

    @Data
    public static class TextCardMsgContent implements Serializable {

        private static final long serialVersionUID = 4355074295312862109L;
        //标题，不超过128个字节，超过会自动截断
        private String title;
        //描述，不超过512个字节，超过会自动截断  如：<div class=\"gray\">2016年9月26日</div> <div class=\"normal\">恭喜你抽中iPhone 7一台，领奖码：xxxx</div><div class=\"highlight\">请于2016年10月10日前联系行政同事领取</div>
        private String description;
        //点击后跳转的链接。
        private String url;
        //按钮文字。 默认为“详情”， 不超过4个文字，超过自动截断。
        private String btntxt;
    }
}
