package com.facishare.open.qywx.accountsync.utils.xml;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Data;

@XStreamAlias("xml")
@Data
public class externalMigrationXml {
    @XStreamAlias("AuthCorpId")
    private String AuthCorpId;

    @XStreamAlias("InfoType")
    private String InfoType;

    @XStreamAlias("ServiceCorpId")
    private String ServiceCorpId;

    @XStreamAlias("TimeStamp")
    private String TimeStamp;

    @Override
    public String toString() {
        return "externalMigrationXml{" +
                "AuthCorpId='" + AuthCorpId + '\'' +
                ", InfoType='" + InfoType + '\'' +
                ", ServiceCorpId='" + ServiceCorpId + '\'' +
                ", TimeStamp='" + TimeStamp + '\'' +
                '}';
    }
}
