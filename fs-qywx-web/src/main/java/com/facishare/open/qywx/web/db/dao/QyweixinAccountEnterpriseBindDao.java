package com.facishare.open.qywx.web.db.dao;

import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.github.mybatis.mapper.ICrudMapper;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/7/18.
 */
@Repository
public interface QyweixinAccountEnterpriseBindDao extends ICrudMapper<QyweixinAccountEnterpriseMapping> {

    @Insert("<script>" + "insert ignore into enterprise_account_bind(source, fs_ea, out_ea, dep_id, isv_out_ea, status, bind_type, domain, gmt_modified, gmt_create) " + "values " + "(#{accountEnterpriseMapping.source}, #{accountEnterpriseMapping.fsEa}, #{accountEnterpriseMapping.outEa}, #{accountEnterpriseMapping.depId},#{accountEnterpriseMapping.isvOutEa}, #{accountEnterpriseMapping.status}, #{accountEnterpriseMapping.bindType}, #{accountEnterpriseMapping.domain}, now(), now())" + "</script>")
    int bindAccountEnterpriseMapping(@Param("accountEnterpriseMapping")QyweixinAccountEnterpriseMapping accountEnterpriseMapping);

    @Select("<script>" +
            "select * from enterprise_account_bind where source=#{source} " +
            "and (out_ea=#{outEa} or isv_out_ea=#{outEa}) " +
            "<if test='depId != null and depId !=\"\"'> and dep_id=#{depId} </if> " +
            "<if test=\"status != -1\"> and status=#{status} </if> " +
            "<if test='domain != null and domain !=\"\"'> and domain=#{domain} </if> " +
            "</script>")
    List<QyweixinAccountEnterpriseMapping> queryEaMappingFromOutEa(@Param("source") String source,
                                 @Param("outEa") String outEa,
                                 @Param("depId") String depId,
                                 @Param("status") Integer status,
                                 @Param("domain") String domain);

    @Select("<script>" + "select * from enterprise_account_bind where source=#{source} and fs_ea=#{fsEa} <if test=\"outEa != null and outEa != ''\">and out_ea = #{outEa} </if> <if test='domain != null and domain !=\"\"'> and domain=#{domain} </if> order by gmt_create ASC limit 1" + "</script>")
    QyweixinAccountEnterpriseMapping queryEaMappingFromFsEa(@Param("source") String source,
                                                            @Param("fsEa") String fsEa,
                                                            @Param("outEa") String outEa,
                                                            @Param("domain") String domain);

    @Select("<script>" + "select * from enterprise_account_bind where source=#{source} and fs_ea=#{fsEa} <if test=\"status != -1\"> and status=#{status} </if>" + "</script>")
    List<QyweixinAccountEnterpriseMapping> queryEnterpriseMappingListFromFsEa(@Param("source") String source,
                                                                              @Param("fsEa") String fsEa,
                                                                              @Param("status") Integer status);

    //支持一个CRM对多个企微
    @Select("<script>" + "select * from enterprise_account_bind where fs_ea=#{fsEa} <if test='domain != null and domain !=\"\"'> and domain=#{domain} </if> " + "</script>")
    List<QyweixinAccountEnterpriseMapping> queryEaMappingListFromFsEa(@Param("source") String source,
                                                                      @Param("fsEa") String fsEa,
                                                                      @Param("domain") String domain);

    @Delete("<script>"+ "delete from enterprise_account_bind where out_ea=#{outEa}" +"</script>")
    int deleteByOutEa(String outEa);

    @Delete("<script>"+ "delete from enterprise_account_bind where fs_ea=#{fsEa} <if test=\"outEa != null and outEa != ''\">and out_ea = #{outEa} </if>" +"</script>")
    int deleteByFsEa(@Param("fsEa") String fsEa,
                     @Param("outEa") String outEa);

    @Select("<script>"+ "SELECT DISTINCT(aTable.out_ea) out_ea , aTable.fs_ea FROM enterprise_account_bind aTable " +
            " LEFT JOIN qyweixin_corp_bind bTable on aTable.out_ea = bTable.corp_id " +
            " where bTable.status=0 <if test='domain != null and domain !=\"\"'> and aTable.domain=#{domain} </if> LIMIT #{pageNum},#{pageSize};"+"</script>")
    ArrayList<HashMap<String, String>> queryFsEaBindBatch(@Param("pageNum") int pageNum,
                                                          @Param("pageSize") int pageSize,
                                                          @Param("domain") String domain);

    @Select("<script>"+"select corp_name corpName from qyweixin_corp_info where corp_id=#{outEa} "+"</script>")
    Map<String,String> queryCorpInfoByCorpId(@Param("outEa") String outEa);

    @Update("<script>" + "update enterprise_account_bind set " + " isv_out_ea = #{accountEnterpriseMapping.isvOutEa} where" + " out_ea=#{accountEnterpriseMapping.outEa}  and " + " fs_ea=#{accountEnterpriseMapping.fsEa}" +"</script>")
    Integer updateEnterpriseMapping(@Param("accountEnterpriseMapping")QyweixinAccountEnterpriseMapping accountEnterpriseMapping);

    @Update("<script>" + "update enterprise_account_bind set " + " account_sync_config = #{accountSyncConfig} " +
            " <if test=\"accountSyncConfigTime != null and accountSyncConfigTime != ''\"> , account_sync_config_time = #{accountSyncConfigTime}</if> " +
            " <if test=\"leadsSyncConfigTime != null and leadsSyncConfigTime != ''\"> , leads_sync_config_time = #{leadsSyncConfigTime}</if> " +
            " <if test=\"contactSyncConfigTime != null and contactSyncConfigTime != ''\"> , contact_sync_config_time = #{contactSyncConfigTime}</if> " +
            " where" + " id=#{id} " +"</script>")
    Integer updateAccountSyncConfig(@Param("id") int id,@Param("accountSyncConfig")String accountSyncConfig
            ,@Param("accountSyncConfigTime") String accountSyncConfigTime,@Param("leadsSyncConfigTime") String leadsSyncConfigTime
            ,@Param("contactSyncConfigTime") String contactSyncConfigTime);

    @Update("<script>" + "update enterprise_account_bind set " + "out_ea = #{accountEnterpriseMapping.outEa}, " + " isv_out_ea = #{accountEnterpriseMapping.isvOutEa} where" + " fs_ea=#{accountEnterpriseMapping.fsEa}" +"</script>")
    Integer updateQyweixinAccountEnterpriseMapping(@Param("accountEnterpriseMapping")QyweixinAccountEnterpriseMapping accountEnterpriseMapping);

    @Select("<script>" +
            "SELECT * FROM enterprise_account_bind where bind_type=#{bindType} AND status=0 <if test='domain != null and domain !=\"\"'> and domain=#{domain} </if>" +
            "</script>")
    List<QyweixinAccountEnterpriseMapping> queryEnterpriseMappingByBindType(@Param("bindType") Integer bindType,
                                                                            @Param("domain") String domain);

    @Update("<script>" + "update enterprise_account_bind set status = #{status} where fs_ea=#{fsEa} <if test=\"outEa != null and outEa != ''\">and out_ea = #{outEa} </if>" +"</script>")
    Integer updateEnterpriseBindStatus(@Param("fsEa") String fsEa,
                                       @Param("status") int status,
                                       @Param("outEa") String outEa);

    @Delete("<script>" + "delete from enterprise_account_bind where " +
            "out_ea = #{corpId} " +
            "and fs_ea = #{fsEa} " +
            "</script>")
    int deleteEnterpriseBind(@Param("corpId") String corpId, @Param("fsEa") String fsEa);

    @Update("<script>" + "update enterprise_account_bind set extend=#{extend} where fs_ea=#{fsEa} <if test=\"outEa != null and outEa != ''\">and out_ea = #{outEa} </if>" +"</script>")
    Integer updateExtend(@Param("fsEa") String fsEa,
                         @Param("extend") String extend,
                         @Param("outEa") String outEa);

    @Update("<script>" + "update enterprise_account_bind set out_ea = #{outEa},dep_id=#{outDepId} where id=#{id} " + "</script>")
    Integer updateEnterpriseOutInfo(@Param("id") int id,
                          @Param("outEa") String outEa,
                          @Param("outDepId") String outDepId);

    @Update("<script>" + "update enterprise_account_bind set domain = #{domain} where fs_ea=#{fsEa} " + "</script>")
    Integer updateEnterpriseDomain(@Param("fsEa") String fsEa,
                                    @Param("domain") String domain);

    @Select("<script>"+"select * from enterprise_account_bind where source='qywx'  <if test=\"outEa != null and outEa != ''\">and out_ea = #{outEa} </if> <if test=\"fsEa != null and fsEa != ''\">and fs_ea = #{fsEa} </if> "+"</script>")
    List<QyweixinAccountEnterpriseMapping> queryEnterpriseMappings(@Param("fsEa") String fsEa, @Param("outEa") String outEa);

    /**
     * 通过多个outEa批量查询企业绑定关系
     *
     * @param outEas 外部企业账号列表
     * @return 企业绑定关系列表
     */
    @Select("<script>" +
            "select * from enterprise_account_bind where out_ea in " +
            "<foreach collection='outEas' item='outEa' open='(' separator=',' close=')'>" +
            "#{outEa}" +
            "</foreach>" +
            "</script>")
    List<QyweixinAccountEnterpriseMapping> queryEaMappingFromOutEaList(@Param("outEas") List<String> outEas);

    /**
     * 通过时间范围查询企业绑定关系
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 企业绑定关系列表
     */
    @Select("select * from enterprise_account_bind where gmt_create between #{startTime} and #{endTime}")
    List<QyweixinAccountEnterpriseMapping> queryEaMappingByTimeRange(@Param("startTime") Timestamp startTime,
                                                                    @Param("endTime") Timestamp endTime);


    @Update("<script>" + "update enterprise_account_bind set out_ea = #{outEa},isv_out_ea=#{outEa},fs_ea=#{fsEa} where id=#{id} " + "</script>")
    Integer updateEnterpriseOldById(@Param("id") int id,
                                    @Param("outEa") String outEa,
                                    @Param("fsEa") String fsEa);
}
