package com.facishare.open.qywx.web.db.dao;

import com.facishare.open.qywx.accountbind.model.QyweixinAccountDepartmentMapping;
import com.github.mybatis.mapper.ICrudMapper;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by fengyh on 2018/3/5.
 */

@Repository
public interface QyweixinAccountDepartmentBindDao extends ICrudMapper<QyweixinAccountDepartmentMapping>  {

    @Insert("<script>" + "insert ignore into department_account_bind(source, fs_ea, out_ea, app_id,fs_department_id, out_department_id, status, gmt_modified, gmt_create) " + "values " +
            "<foreach collection='accountDepartmentMappingList' item='mapping' separator=','>" +
            "(#{mapping.source}, #{mapping.fsEa}, #{mapping.outEa}, #{mapping.appId}, #{mapping.fsDepartmentId}, #{mapping.outDepartmentId}, #{mapping.status}, now(), now())" +
            "</foreach></script>")
    int bindAccountEnterpriseMapping(@Param("accountDepartmentMappingList") List<QyweixinAccountDepartmentMapping> accountDepartmentMappingList);

    @Select("<script>" + "select * from department_account_bind where " + "source=#{source} and fs_ea=#{fsEa} and " + " fs_department_id in " +
            "<foreach collection='fsDepartmentIdList' item='fsDepartmentId' open='(' close=')'  separator=','>" +
            "#{fsDepartmentId} </foreach> and app_id=#{appId} and status = 0" + "</script>")
    List<QyweixinAccountDepartmentMapping> queryDepartmentBindByFsDepartment(@Param("source")String source,
                                                                             @Param("fsEa")String fsEa,
                                                                             @Param("appId") String appId,
                                                                             @Param("fsDepartmentIdList")List<Integer> fsDepartmentIdList);

    @Select("<script>" + "select * from department_account_bind where " + "source=#{source} and fs_ea=#{fsEa} and " + " fs_department_id in " +
            "<foreach collection='fsDepartmentIdList' item='fsDepartmentId' open='(' close=')'  separator=','>" +
            "#{fsDepartmentId} </foreach>" + "</script>")
    List<QyweixinAccountDepartmentMapping> queryByFsDepartmentIds(@Param("source") String source,
                                                                  @Param("fsEa") String fsEa,
                                                                  @Param("fsDepartmentIdList") List<Integer> fsDepartmentIdList);

    @Update("<script>" + "update department_account_bind set status=#{status} where source=#{source} and " +
            "fs_ea=#{fsEa} and app_id =#{appId} and out_department_id in <foreach " +
            "collection='outDepartmentIds' item='outDepartmentId' open='(' close=')'" +
            "  separator=','>" + "#{outDepartmentId}" + "</foreach>" + "</script>")
    int changeDepartmentStatus(@Param("source")String source,
                               @Param("fsEa")String fsEa,
                               @Param("appId") String appId,
                               @Param("outDepartmentIds") List<String> outDepartmentIds,
                               @Param("status") int status);

    @Select("<script>" + "select * from department_account_bind where " + "source=#{source} and out_ea=#{outEa} " +
            " <if test=\"outDepartmentIdList != null and outDepartmentIdList.size() > 0\"> and out_department_id in " + "<foreach " +
            "collection='outDepartmentIdList' item='outDepartmentId' open='(' close=')'  separator=','>" + "#{outDepartmentId} </foreach></if>" +
            "<if test=\"appId != null and appId != ''\"> and app_id = #{appId} </if> " +
            "<if test=\"status != -1\"> and status=#{status} </if></script>")
    List<QyweixinAccountDepartmentMapping> queryDepartmentBindByOutDepartment(@Param("source")String source,
                                                                              @Param("outEa")String outEa,
                                                                              @Param("appId") String appId,
                                                                              @Param("status") int status,
                                                                              @Param("outDepartmentIdList")List<String> outDepartmentIdList);

    @Delete("<script>" + "delete from department_account_bind where out_ea=#{outEa} and app_id=#{appId} </script>")
    int deleteByOutEa(@Param("outEa") String outEa,@Param("appId")String appId);

    @Update("<script>" + "update department_account_bind set out_ea=#{openOutEa} where out_ea=#{outEa} and source=#{source}</script>")
    int BatchUpdateDepartmentBind(@Param("outEa") String outEa, @Param("openOutEa") String openOutEa, @Param("source")String source);

    @Update("<script>" + "update department_account_bind set status=#{status} where " +
            "fs_ea=#{fsEa} and app_id =#{appId} and fs_department_id in <foreach " +
            "collection='fsDepIdList' item='fsDepId' open='(' close=')'" +
            "  separator=','>" + "#{fsDepId}" + "</foreach> <if test=\"outEa != null and outEa != ''\">and out_ea = #{outEa} </if>" + "</script>")
    int batchUpdateFsDepBindStatus(@Param("fsEa") String fsEa,
                                   @Param("fsDepIdList") List<String> fsDepIdList,
                                   @Param("status") int status,
                                   @Param("appId") String appId,
                                   @Param("outEa") String outEa);

    @Delete("<script>" + "delete from department_account_bind where app_id=#{appId} " +
            " and out_ea = #{corpId} " +
            " and fs_ea = #{fsEa} " +
            "</script>")
    int deleteDepartmentBind(@Param("corpId") String corpId, @Param("fsEa") String fsEa, @Param("appId")String appId);

    @Select("<script>" + "select * from department_account_bind where " + "source=#{source} and fs_ea=#{fsEa} and " + " fs_department_id in " +
            "<foreach collection='fsDepartmentIdList' item='fsDepartmentId' open='(' close=')'  separator=','> " +
            "#{fsDepartmentId} </foreach>" +
            "<if test=\"appId != null and appId != ''\"> and app_id = #{appId} </if> " +
            "<if test=\"status != -1\"> and status=#{status} </if> <if test=\"outEa != null and outEa != ''\">and out_ea = #{outEa} </if>" +
            "</script>")
    List<QyweixinAccountDepartmentMapping> queryByFsDepartmentIdsV2(@Param("source") String source,
                                                                    @Param("fsEa") String fsEa,
                                                                    @Param("appId") String appId,
                                                                    @Param("status") Integer status,
                                                                    @Param("fsDepartmentIdList") List<Integer> fsDepartmentIdList,
                                                                    @Param("outEa") String outEa);

    @Update("<script>" + "update department_account_bind set out_department_id = #{accountMapping.outDepartmentId}, status = #{accountMapping.status} where" + " fs_ea" +
            "=#{accountMapping.fsEa}  and out_ea=#{accountMapping.outEa} and fs_department_id=#{accountMapping.fsDepartmentId} <if test=\"accountMapping.appId != null and accountMapping.appId != ''\">and app_id = #{accountMapping.appId} </if>"+ "</script>")
    Integer updateDepartmentBindMapping(@Param("accountMapping") QyweixinAccountDepartmentMapping accountMapping);

    @Select("<script>" + "select * from department_account_bind where source='qywx' " +
            "and fs_ea = #{fsEa} and fs_department_id = #{fsDepartmentId}" +
            "and out_ea = #{outEa} and out_department_id = #{outDepartmentId}" +
            "<if test=\"status != null and status != -1\"> and status=#{status} </if>  limit 1" + "</script>")
    QyweixinAccountDepartmentMapping getQywxDepartmentMapping(@Param("fsEa") String fsEa,
                                                              @Param("fsDepartmentId") String fsDepartmentId,
                                                              @Param("outEa") String outEa,
                                                              @Param("outDepartmentId") String outDepartmentId,
                                                              @Param("status") Integer status);

    @Select("<script>" +
            "select * from department_account_bind where fs_ea=#{fsEa} and out_ea=#{outEa} and status='0' limit #{offset}, #{limit}" +
            "</script>")
    List<QyweixinAccountDepartmentMapping> selectByPage(@Param("fsEa") String fsEa, @Param("outEa") String outEa, @Param("offset") int offset, @Param("limit") int limit);

}
