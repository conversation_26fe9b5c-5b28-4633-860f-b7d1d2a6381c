package com.facishare.open.qywx.web.model.qyweixin;

import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinAuthCorpInfoRsp;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinAuthInfoRsp;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinEditionInfoRsp;
import com.facishare.open.qywx.accountsync.core.enums.QyweixinErrorCodeEnum;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinAuthUserInfoRsp;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by fengyh on 2018/4/23.
 */
@Data
public class QyweixinGetPermenantCodeRsp implements Serializable {
    private int errcode;
    private String errmsg;
    private String access_token;
    private int expires_in;
    private String permanent_code;
    QyweixinAuthCorpInfoRsp auth_corp_info;
    QyweixinAuthInfoRsp auth_info;
    QyweixinAuthUserInfoRsp auth_user_info;

    QyweixinEditionInfoRsp edition_info;

    public boolean isSuccess(){
        return QyweixinErrorCodeEnum.SUCCESS.getErrCode().equals(errcode);
    }
}
