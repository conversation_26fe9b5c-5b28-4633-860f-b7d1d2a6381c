package com.facishare.open.qywx.messagesend.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;


@Data
public class InvalidUserResult implements Serializable {
    private List<MsgIdAnUser> sucessList;
    private List<MsgIdAnUser> failedList;

    @Data
    @AllArgsConstructor
    public static class MsgIdAnUser implements Serializable{
        private String msgId;
        //发送失败的用户列表
        //不区分大小写，返回的列表都统一转为小写
        private List<String> invalidUser;
        private String errmsg;
    }

}
