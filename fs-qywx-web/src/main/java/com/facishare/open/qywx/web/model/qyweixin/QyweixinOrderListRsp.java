package com.facishare.open.qywx.web.model.qyweixin;

import com.facishare.open.qywx.accountsync.core.enums.QyweixinErrorCodeEnum;
import com.facishare.open.qywx.accountsync.model.qyweixin.OrderInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * Created on 2019/2/15
 */
@Data
public class QyweixinOrderListRsp implements Serializable {
    private static final long serialVersionUID = 6642115165366836336L;

    private Integer errcode;
    private String errmsg;
    List<OrderInfo> order_list;

    public boolean isSuccess(){
        return QyweixinErrorCodeEnum.SUCCESS.getErrCode().equals(errcode);
    }
}
