package com.facishare.open.qywx.web.aop;

import cn.hutool.core.io.IoUtil;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;

public class CacheRequestBodyContent  extends HttpServletRequestWrapper {
    private byte[] body = null;
    /**
     * Constructs a request object wrapping the given request.
     *
     * @param request The request to wrap
     * @throws IllegalArgumentException if the request is null
     */
    public CacheRequestBodyContent(HttpServletRequest request, ServletResponse response) {
        super(request);
        try {
            request.setCharacterEncoding("utf-8");
            response.setCharacterEncoding("utf-8");
            body = IoUtil.readBytes(request.getInputStream(), false);
        } catch (Exception e) {
            LogUtils.error("请求数据读取失败，请重试");
        }
    }

    
    @Override
    public BufferedReader getReader() throws IOException {
        return new BufferedReader(new InputStreamReader(getInputStream()));
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        final ByteArrayInputStream cache = new ByteArrayInputStream(body);
        return new ServletInputStream() {
            @Override
            public boolean isFinished() {
                return false;
            }

            @Override
            public boolean isReady() {
                return false;
            }

            @Override
            public void setReadListener(ReadListener readListener) {

            }

            @Override
            public int read() throws IOException {
                return cache.read();
            }

            @Override
            public int available() throws IOException {
                return body.length;
            }

        };
    }
}