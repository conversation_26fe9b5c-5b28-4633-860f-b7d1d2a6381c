package com.facishare.open.qywx.old.accountsync.service

import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinEnterpriseLicenseModel
import com.facishare.open.qywx.accountsync.service.QyweixinAccountSyncService
import com.google.common.collect.Lists
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
class QyweixinAccountSyncServiceTest extends Specification {

    @Autowired
    private QyweixinAccountSyncService qyweixinAccountSyncService;

    def "getAppAccessToken"() {
        given:
        def fsEa = fsEaCases;
        def appId = appIdCases;
        expect:
        def result = qyweixinAccountSyncService.getAppAccessToken(fsEa, appId);
        println result;
        where:
        fsEaCases                           | appIdCases
        //有绑定关系没有企业应用
        "test"                              | "test"
        "test"                              | "wx88a141937dd6f838"
        "test"                              | "dkdf3684b6720635f7"
        //没有绑定关系没有企业应用
        "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA1" | "test"
        "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA1" | "wx88a141937dd6f838"
        "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA1" | "dkdf3684b6720635f7"
        //有绑定关系，有纷享应用和代开发应用
        "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA"  | "test"
        "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA"  | "wx88a141937dd6f838"
        "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA"  | "dkdf3684b6720635f7"
        //有绑定关系，只有纷享应用
        "wpwx1mDAAAVTsh6kPr_JZ8HAXwNvT0Bw"  | "test"
        "wpwx1mDAAAVTsh6kPr_JZ8HAXwNvT0Bw"  | "wx88a141937dd6f838"
        "wpwx1mDAAAVTsh6kPr_JZ8HAXwNvT0Bw"  | "dkdf3684b6720635f7"
    }

    def "getOutEaResultByFsEa"() {
        given:
        def fsEa = fsEaCases;
        expect:
        def result = qyweixinAccountSyncService.getOutEaResultByFsEa(fsEa);
        println result;
        where:
        fsEaCases | outEaCases
        "test" |   "test"
        "848831" |        "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA1"
        "84883" |         "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA"
    }

    def "getFsEaResultByOutEa"() {
        given:
        def outEa = outEaCases;
        expect:
        def result = qyweixinAccountSyncService.getFsEaResultByOutEa(outEa);
        println result;
        where:
        fsEaCases | outEaCases
        "test" |   "test"
        "848831" |        "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA1"
        "84883" |         "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA"
    }

//    def "switchExternalContactEmployeeId2"() {
//        given:
//        expect:
//        def result = qyweixinAccountSyncService.switchExternalContactEmployeeId2("84883", Lists.newArrayList(case1, case2),null);
//        println result;
//        where:
//        case1 | case2
//        "wmwx1mDAAAb5CbGFLOot4oxD_x2GnRzQ" |   "test"
//    }

//    def "switchEmployeeId2"() {
//        given:
//        expect:
//        def result = qyweixinAccountSyncService.switchEmployeeId2("84883", Lists.newArrayList(case1, case2), null);
//        println result;
//        where:
//        case1 | case2
//        "***********" |   "***********"
//    }

    def "checkEnterpriseProductVersion"() {
        given:
        QyweixinEnterpriseLicenseModel enterpriseLicenseModel = new QyweixinEnterpriseLicenseModel();
        enterpriseLicenseModel.setFsEa(fsEa)
        enterpriseLicenseModel.setCheckType(checkType)
        expect:
        def result = qyweixinAccountSyncService.checkEnterpriseProductVersion(enterpriseLicenseModel)
        println result
        where:
        fsEa | checkType
        "test" |   "test"
        "84883" |        "wechat_scrm"
        "83384" |         "wechat_scrm"
    }

//    def "updateUserAndDepartmentInfo"() {
//        expect:
//        def result = qyweixinAccountSyncService.updateUserAndDepartmentInfo("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA")
//        println result
//    }
}
