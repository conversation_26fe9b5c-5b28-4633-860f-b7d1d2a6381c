package com.facishare.open.qywx.old.save.service

import com.facishare.open.qywx.save.arg.MessageStorageArg
import com.facishare.open.qywx.save.service.MessageGeneratingService
import com.facishare.open.qywx.save.vo.GenerateSettingVo
import com.google.common.collect.Lists
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
class MessageGeneratingServiceTest extends Specification {

    @Autowired
    private MessageGeneratingService messageGeneratingService

    def "saveSetting"() {
        given:
        GenerateSettingVo generateSettingVo = new GenerateSettingVo()
        generateSettingVo.setEa("111110")
        generateSettingVo.setAgentId("100000")
        generateSettingVo.setCorpSecret("testCorpSecret")
        generateSettingVo.setFsTenantId(111110)
        generateSettingVo.setIpList(Lists.newArrayList("***********"))
        generateSettingVo.setPrivateKey("testPrivateKey")
        generateSettingVo.setPublicKey("testPublicKey")
        generateSettingVo.setSecret("testSecret")
        generateSettingVo.setVersion(1)
        generateSettingVo.setQywxCorpId("testQywxCorpId")
        MessageStorageArg arg = new MessageStorageArg();
        arg.setSalesRetentionType(1)
        arg.setConversionObjType(0)
        generateSettingVo.setStorageLocation(arg)
        expect:
        def result = messageGeneratingService.saveSetting(generateSettingVo)
        println result
    }

//    def "getMessageStorageLocation"() {
//        given:
//        def fsEa = fsEaCase
//        def version = versionCase
//        def serviceKey = serviceKeyCase
//        expect:
//        def result = messageGeneratingService.getMessageStorageLocation(fsEa, version, serviceKey)
//        println result
//        where:
//        fsEaCase  |  versionCase  |  serviceKeyCase
//        "84883"   |  4          |  "testqywx"
//        "84883"   |  null          |  "testqywx"
//        "111110"   |  1          |  "testqywx"
//        "111110"   |  null          |  "testqywx"
//    }

//    def "querySettingByAuto"() {
//        given:
//        def fsEa = fsEaCase
//        def version = versionCase
//        expect:
//        def result = messageGeneratingService.querySettingByAuto(fsEa, version)
//        println result
//        where:
//        fsEaCase  |  versionCase
//        "84883"   |  4
//        "84883"   |  null
//        "111110"   |  1
//        "111110"   |  null
//    }
//
//    def "querySetting"() {
//        given:
//        def fsEa = fsEaCase
//        def version = versionCase
//        expect:
//        def result = messageGeneratingService.querySetting(fsEa, version)
//        println result
//        where:
//        fsEaCase  |  versionCase
//        "84883"   |  4
//        "84883"   |  null
//        "111110"   |  1
//        "111110"   |  null
//    }
}
