package com.facishare.open.qywx.web.eventHandler;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class RepEventHandlerTest extends AbstractJUnit4SpringContextTests {
    @Resource
    private RepEventHandler repEventHandler;

    @Test
    public void testHandleCreateAuth() throws Exception {
        /**
         *         //TODO
         *         corpAuthInfoResult.setCode("0");
         *         String json = "{\"errcode\":0,\"errmsg\":null,\"access_token\":\"***\",\"expires_in\":0,\"permanent_code\":\"0G-O4_HVLKv17T5_0VH6akLXSazpdXU1nTIXJVDyScY\",\"auth_corp_info\":{\"corpid\":\"wpwx1mDAAAaaLlKWQ8uGm23tj_KM8gIA\",\"corp_name\":\"测试1201\",\"corp_type\":\"unverified\",\"corp_square_logo_url\":\"https://wework.qpic.cn/wwpic3az/979535_4zXHU2egQzim7T__1733123579/0\",\"corp_user_max\":1000,\"corp_agent_max\":0,\"corp_full_name\":null,\"verified_end_time\":null,\"subject_type\":1,\"corp_wxqrcode\":\"https://wework.qpic.cn/wwpic3az/885386_Y39YWXyKTjKnq5C_1742470082/0\",\"corp_scale\":\"1-50人\",\"corp_industry\":\"IT服务\",\"corp_sub_industry\":\"互联网和相关服务\",\"location\":\"\"},\"auth_info\":null,\"auth_user_info\":{\"userid\":\"wowx1mDAAAswtw60xRomOCQexeLXdU3w\",\"name\":\"陈宗鑫\",\"avatar\":\"https://wework.qpic.cn/wwpic3az/868660_GziNyJSiT6ao3aj_1733236384/0\"},\"edition_info\":null}";
         *         QyweixinGetPermenantCodeRsp corpAuthInfo1 = new Gson().fromJson(json, new TypeToken<QyweixinGetPermenantCodeRsp>() {
         *         }.getType());
         *         corpAuthInfoResult.setData(corpAuthInfo1);
         */
        String xml = "<xml><SuiteId><![CDATA[dkdf3684b6720635f7]]></SuiteId><AuthCode><![CDATA[PBYW4zA8Nvu_uUvDZ3ZZoiON2oZvwWokrq9mEmlRGqvfwKxk8rHE-NiDhZHp537jijzQ0rM39tXyCADFNIsJE_ZmM_LJ7oKssl3WqqUIgTA]]></AuthCode><InfoType><![CDATA[create_auth]]></InfoType><TimeStamp>1742470081</TimeStamp></xml>";
        String appId = "dkdf3684b6720635f7";
        repEventHandler.handle(xml, appId);
    }

    @Test
    public void testHandleCancelAuth() throws Exception {
        String xml = "<xml><SuiteId><![CDATA[dkdf3684b6720635f7]]></SuiteId><AuthCorpId><![CDATA[wpwx1mDAAAaaLlKWQ8uGm23tj_KM8gIA]]></AuthCorpId><InfoType><![CDATA[cancel_auth]]></InfoType><TimeStamp>1742470081</TimeStamp></xml>";
        String appId = "dkdf3684b6720635f7";
        repEventHandler.handle(xml, appId);
    }

    @Test
    public void testHandleChangeAuth() throws Exception {
        String xml = "<xml><SuiteId><![CDATA[dkdf3684b6720635f7]]></SuiteId><InfoType><![CDATA[change_auth]]></InfoType><TimeStamp>1403610513</TimeStamp><AuthCorpId><![CDATA[wpwx1mDAAALG1GObCJSt5_5ruswcU9gA]]></AuthCorpId><State><![CDATA[abc]]></State><ExtraInfo></ExtraInfo></xml>";
        String appId = "dkdf3684b6720635f7";
        repEventHandler.handle(xml, appId);
    }

    @Test
    public void testHandleChangeContact() throws Exception {
        //人员变更通知事件
        String xml = "<xml><ToUserName><![CDATA[wpwx1mDAAAaaLlKWQ8uGm23tj_KM8gIA]]></ToUserName><FromUserName><![CDATA[sys]]></FromUserName><CreateTime>1742620334</CreateTime><MsgType><![CDATA[event]]></MsgType><Event><![CDATA[change_contact]]></Event><ChangeType><![CDATA[update_user]]></ChangeType><UserID><![CDATA[wowx1mDAAAswtw60xRomOCQexeLXdU3w]]></UserID><Position><![CDATA[宝宝肚肚打雷啦]]></Position></xml>, infoType=change_contact, baseMsgXml=QyweixinMsgBaseXml(SuiteId=null, InfoType=null, TimeStamp=null, AuthCorpId=null, ToUserName=wpwx1mDAAAaaLlKWQ8uGm23tj_KM8gIA, ChangeType=update_user), tagChangeEventXml=TagChangeEventXml(ToUserName=wpwx1mDAAAaaLlKWQ8uGm23tj_KM8gIA, FromUserName=sys, CreateTime=1742620334, MsgType=event, Event=change_contact, ChangeType=update_user, TagId=null, AddUserItems=null, DelUserItems=null, AddPartyItems=null, DelPartyItems=null)}";
        String xml1 = "<xml><ToUserName><![CDATA[wpwx1mDAAAaaLlKWQ8uGm23tj_KM8gIA]]></ToUserName><FromUserName><![CDATA[sys]]></FromUserName><CreateTime>1742623954</CreateTime><MsgType><![CDATA[event]]></MsgType><Event><![CDATA[change_contact]]></Event><ChangeType><![CDATA[create_party]]></ChangeType><Id>3</Id><Name><![CDATA[121102]]></Name><ParentId>1</ParentId><Order>99999000</Order></xml>, infoType=change_contact, baseMsgXml=QyweixinMsgBaseXml(SuiteId=null, InfoType=null, TimeStamp=null, AuthCorpId=null, ToUserName=wpwx1mDAAAaaLlKWQ8uGm23tj_KM8gIA, ChangeType=create_party), tagChangeEventXml=TagChangeEventXml(ToUserName=wpwx1mDAAAaaLlKWQ8uGm23tj_KM8gIA, FromUserName=sys, CreateTime=1742623954, MsgType=event, Event=change_contact, ChangeType=create_party, TagId=null, AddUserItems=null, DelUserItems=null, AddPartyItems=null, DelPartyItems=null)}";
        String appId = "dkdf3684b6720635f7";
        repEventHandler.handle(xml1, appId);
    }

    @Test
    public void testProgramNotify() throws Exception {
        String xml = "<xml><ToUserName><![CDATA[wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA]]></ToUserName><FromUserName><![CDATA[sys]]></FromUserName><MsgType><![CDATA[event]]></MsgType><Event><![CDATA[program_notify]]></Event><CreateTime>1744883288</CreateTime><NotifyId><![CDATA[f95e87ac54a31a0bdd5bfe48f4b31e20]]></NotifyId></xml>";
        String appId = "dkdf3684b6720635f7";
        repEventHandler.handle(xml, appId);
    }
}
