package com.facishare.open.qywx.old.save.test;

import com.facishare.open.qywx.BaseTest;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.accountsync.service.QyweixinAccountSyncService;
import com.facishare.open.qywx.web.arg.ActviceCodeArgExecuteTypeArg;
import com.facishare.open.qywx.web.constant.CreateObjectEnum;
import com.facishare.open.qywx.web.manager.CrmObjectSupportManager;
import com.facishare.open.qywx.web.manager.QywxPermissionManager;
import com.facishare.open.qywx.web.service.QyweixinAccountBindServiceTest;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;

public class CrmObjectSupportManagerTest extends BaseTest {
    @Autowired
    private CrmObjectSupportManager crmObjectSupportManager;
    @Autowired
    private QyweixinAccountSyncService qyweixinAccountSyncService;

    @Test
    public void createDefineObject() {
        boolean defineObject = crmObjectSupportManager.createDefineObject(74164, CreateObjectEnum.WechatConversionObj.name());
        System.out.println(defineObject);
    }
    @Test
    public void testData(){
        ActviceCodeArgExecuteTypeArg.QywxTransferUserItem qywxTransferUserItem=new ActviceCodeArgExecuteTypeArg.QywxTransferUserItem();
        qywxTransferUserItem.setHandoverUserid("wowx1mDAAAwQtEOBYCEBNki7AT0Jj4xA");
        qywxTransferUserItem.setTakeoverUserid("wowx1mDAAAsnzPFdfvBqmxBoU1K621AA");

        ArrayList<ActviceCodeArgExecuteTypeArg.QywxTransferUserItem> qywxTransferUserItems = Lists.newArrayList(qywxTransferUserItem);
        Result<Void> voidResult = qyweixinAccountSyncService.transferUserIds("84883", qywxTransferUserItems);
        System.out.println(voidResult);
    }
}
