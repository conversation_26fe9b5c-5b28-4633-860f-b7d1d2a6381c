package com.facishare.open.qywx.web.service;

import com.alibaba.fastjson.JSON;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.order.contacts.proxy.api.result.Result;
import com.facishare.open.order.contacts.proxy.api.service.FsContactsServiceProxy;
import com.facishare.open.qywx.BaseTest;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountsync.service.ContactsService;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
public class ContactsServiceTest extends BaseTest {
    @Resource
    private ContactsService contactsService;
    @Resource
    private FsContactsServiceProxy fsContactsServiceProxy;

    @Test
    public void test() throws Exception {
        Result<List<ObjectData>> listResult = fsContactsServiceProxy.batchGetAllFsDep(89780, "999999");
        log.info("listResult={}", JSON.toJSONString(listResult));
    }

    @Test
    public void test2() throws Exception {
        contactsService.syncOuterContacts("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA", "dkdf3684b6720635f7", "change_auth", "<xml><SuiteId><![CDATA[dkdf3684b6720635f7]]></SuiteId><InfoType><![CDATA[change_auth]]></InfoType><TimeStamp>1746530545</TimeStamp><AuthCorpId><![CDATA[wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA]]></AuthCorpId></xml>");
    }

    @Test
    public void test3() throws Exception {
        contactsService.syncOuterContacts("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA", "dkdf3684b6720635f7", "change_contact", "<xml><ToUserName><![CDATA[wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA]]></ToUserName><FromUserName><![CDATA[sys]]></FromUserName><CreateTime>1746532694</CreateTime><MsgType><![CDATA[event]]></MsgType><Event><![CDATA[change_contact]]></Event><ChangeType><![CDATA[update_user]]></ChangeType><UserID><![CDATA[wowx1mDAAArLI4aPJXTqJx_UJCABHILQ]]></UserID><Alias><![CDATA[center11]]></Alias></xml>");
    }

    @Test
    public void test4() throws Exception {
        contactsService.syncOuterContacts("wpwx1mDAAAf2dgc5rOe0AJxr_Sd9KBIA", "wx88a141937dd6f838", "subscribe", "<xml><ToUserName><![CDATA[wpwx1mDAAAf2dgc5rOe0AJxr_Sd9KBIA]]></ToUserName><FromUserName><![CDATA[wowx1mDAAAC1SPX3vn0t4uZ-61NXAs5Q]]></FromUserName><CreateTime>1753791506</CreateTime><MsgType><![CDATA[event]]></MsgType><AgentID>1000004</AgentID><Event><![CDATA[subscribe]]></Event></xml>");
    }

    @Test
    public void test5() throws Exception {
        contactsService.syncOuterContacts("wpwx1mDAAAf2dgc5rOe0AJxr_Sd9KBIA", "wx88a141937dd6f838", "unsubscribe", "<xml><ToUserName><![CDATA[wpwx1mDAAAf2dgc5rOe0AJxr_Sd9KBIA]]></ToUserName><FromUserName><![CDATA[wowx1mDAAAC1SPX3vn0t4uZ-61NXAs5Q]]></FromUserName><CreateTime>1753791506</CreateTime><MsgType><![CDATA[event]]></MsgType><AgentID>1000004</AgentID><Event><![CDATA[unsubscribe]]></Event></xml>");
    }
}
