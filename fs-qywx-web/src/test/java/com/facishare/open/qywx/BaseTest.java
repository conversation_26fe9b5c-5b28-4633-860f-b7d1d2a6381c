package com.facishare.open.qywx;

import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import org.junit.BeforeClass;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.UUID;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring-test/spring-common-test.xml"})
public class BaseTest {
    @BeforeClass
    public static void SetUp(){
        TraceUtils.initTrace(UUID.randomUUID().toString());
        System.setProperty("config.mode", "localFirst");
        System.setProperty("process.profile", "fstest");
        System.setProperty("process.name","fs-qywx-web");
    }
}