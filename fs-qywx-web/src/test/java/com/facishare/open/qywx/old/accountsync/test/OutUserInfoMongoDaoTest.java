package com.facishare.open.qywx.old.accountsync.test;

import com.facishare.open.qywx.web.mongo.dao.OutUserInfoMongoDao;
import com.facishare.open.qywx.web.mongo.document.OutUserInfoDoc;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import com.mongodb.client.result.DeleteResult;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class OutUserInfoMongoDaoTest extends AbstractJUnit4SpringContextTests {
    @Autowired
    private OutUserInfoMongoDao outUserInfoMongoDao;

    @ReloadableProperty("crmAppId")
    private String crmAppId;
    @ReloadableProperty("repAppId")
    private String repAppId;

    @Test
    public void testMongoDao() {
        OutUserInfoDoc outUserInfoDoc = new OutUserInfoDoc();
        //departmentInfoDoc.setId(ObjectId.get());
        outUserInfoDoc.setOutEa("wpwx1mDAAA_oUB9IvGELTOW-O5sA9o9g1");
        outUserInfoDoc.setOutUserId("100");
        outUserInfoDoc.setOutUserInfo("这个是jsonyyyy");
        outUserInfoDoc.setCreateTime(System.currentTimeMillis());
        outUserInfoDoc.setUpdateTime(System.currentTimeMillis());
        outUserInfoMongoDao.batchReplace(Lists.newArrayList(outUserInfoDoc));
        //查询
//        OutDepartmentInfoDoc outDepartmentInfoDoc = outDepartmentInfoMongoDao.getById(departmentInfoDoc.getId().toString());
//        System.out.println(outDepartmentInfoDoc);
        //departmentInfoDoc.setId(ObjectId.get());
        outUserInfoDoc.setOutUserInfo("这个是json11111");
        outUserInfoDoc.setCreateTime(System.currentTimeMillis());
        outUserInfoDoc.setUpdateTime(System.currentTimeMillis());
        outUserInfoMongoDao.batchReplace(Lists.newArrayList(outUserInfoDoc));
        //查询
//        OutDepartmentInfoDoc outDepartmentInfoDoc1 = outDepartmentInfoMongoDao.getById(departmentInfoDoc.getId().toString());
//        System.out.println(outDepartmentInfoDoc1);

        List<OutUserInfoDoc> outDepartmentInfoDocs = outUserInfoMongoDao.queryUserInfos("wpwx1mDAAA_oUB9IvGELTOW-O5sA9o9g1");
        System.out.println(outDepartmentInfoDocs);
    }

    @Test
    public void deleteUserInfoByUserId() {
        //outUserInfoMongoDao.deleteIndex("index_outEa_outDepartmentId");
        DeleteResult result = outUserInfoMongoDao.deleteUserInfoByUserId("wpwx1mDAAAVaQ_WJTdJalXD5vN1xgdjg", "wowx1mDAAANLToEuC6-YGa8yj1s6iUtA");
        System.out.println(result);
    }

    @Test
    public void deleteUserInfo() {
        //outUserInfoMongoDao.deleteIndex("index_outEa_outDepartmentId");
        List<OutUserInfoDoc> docs = outUserInfoMongoDao.queryUserInfos("wpwx1mDAAAlHhsjfqdPRNWd0IzyMjWYw");
        DeleteResult result = outUserInfoMongoDao.deleteUserInfoByOutEa("wpwx1mDAAAlHhsjfqdPRNWd0IzyMjWYw");
        docs = outUserInfoMongoDao.queryUserInfos("wpwx1mDAAAlHhsjfqdPRNWd0IzyMjWYw");
        System.out.println(result);
    }

    @Test
    public void queryUserInfos() {
        List<OutUserInfoDoc> outDepartmentInfoDocs = outUserInfoMongoDao.queryUserInfos("wpwx1mDAAAVaQ_WJTdJalXD5vN1xgdjg");
        System.out.println(outDepartmentInfoDocs);
    }

    @Test
    public void deleteNotInCollectionDocs() {
        OutUserInfoDoc outUserInfoDoc = new OutUserInfoDoc();
        //departmentInfoDoc.setId(ObjectId.get());
        outUserInfoDoc.setOutEa("wpwx1mDAAA_oUB9IvGELTOW-O5sA9o9g1");
        outUserInfoDoc.setOutUserId("100");
        outUserInfoDoc.setOutUserInfo("这个是jsonyyyy");
        outUserInfoDoc.setCreateTime(System.currentTimeMillis());
        outUserInfoDoc.setUpdateTime(System.currentTimeMillis());

        OutUserInfoDoc outUserInfoDoc1 = new OutUserInfoDoc();
        //departmentInfoDoc1.setId(ObjectId.get());
        outUserInfoDoc1.setOutEa("wpwx1mDAAA_oUB9IvGELTOW-O5sA9o9g1");
        outUserInfoDoc1.setOutUserId("300");
        outUserInfoDoc1.setOutUserInfo("这个是jsonyyyy");
        outUserInfoDoc1.setCreateTime(System.currentTimeMillis());
        outUserInfoDoc1.setUpdateTime(System.currentTimeMillis());
        DeleteResult deleteResult = outUserInfoMongoDao.deleteNotInCollectionDocs("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA", Lists.newArrayList(outUserInfoDoc, outUserInfoDoc1));
        System.out.println(deleteResult);
    }
}
