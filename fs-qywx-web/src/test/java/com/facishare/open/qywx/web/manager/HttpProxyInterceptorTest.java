package com.facishare.open.qywx.web.manager;

import com.facishare.open.qywx.accountinner.result.Result;
import com.facishare.open.qywx.web.aop.HttpProxyInterceptor;
import com.fasterxml.jackson.databind.JsonNode;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.web.util.ContentCachingRequestWrapper;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;

import static com.facishare.restful.server.FRestJacksonMapperBean.objectMapper;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class HttpProxyInterceptorTest extends AbstractJUnit4SpringContextTests {
    @Autowired
    private HttpProxyInterceptor httpProxyInterceptor;


    @Test
    public void setCallback() throws Exception {
        MockHttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        // 2. 设置请求方法
        request.setMethod("POST");

        // 3. 设置请求体数据
        String requestBody = "{\"ea\": \"test\"}";
        request.setContent(requestBody.getBytes(StandardCharsets.UTF_8));

        try {
            ContentCachingRequestWrapper wrappedRequest = new ContentCachingRequestWrapper(request);
            byte[] cachedBody = wrappedRequest.getContentAsByteArray();
            if (cachedBody != null && cachedBody.length > 0) {
                JsonNode bodyNode = objectMapper.readTree(cachedBody);
                String extractEaFromJson = extractEaFromJson(bodyNode);
                if (StringUtils.isEmpty(extractEaFromJson)) {
                    System.out.println();
                }
            }
        } catch (IOException e) {
            System.out.println();
        }

        Boolean result = httpProxyInterceptor.preHandle(request, response, null);
        System.out.println(result);
    }

    private String extractEaFromJson(JsonNode jsonNode) {
        if (jsonNode.isObject()) {
            if (jsonNode.has("ea")) {
                return jsonNode.get("ea").asText();
            } else if (jsonNode.has("fsEa")) {
                return jsonNode.get("fsEa").asText();
            } else if (jsonNode.has("fsEnterpriseAccount")) {
                return jsonNode.get("fsEnterpriseAccount").asText();
            }

            if (jsonNode.has("outEa")) {
                return jsonNode.get("outEa").asText();
            } else if (jsonNode.has("corpId")) {
                return jsonNode.get("corpId").asText();
            }

            for (JsonNode childNode : jsonNode) {
                String result = extractEaFromJson(childNode);
                if (result != null) {
                    return result;
                }
            }
        } else if (jsonNode.isArray()) {
            for (JsonNode childNode : jsonNode) {
                String result = extractEaFromJson(childNode);
                if (result != null) {
                    return result;
                }
            }
        }
        return null;
    }
}
