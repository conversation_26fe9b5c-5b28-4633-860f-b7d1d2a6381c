package com.facishare.open.qywx.old.accountsync.utils;

import com.fxiaoke.common.PasswordUtil;
import com.google.common.net.UrlEscapers;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class MongoPwdUtils {
    private static final Pattern MONGO_URI = Pattern.compile("mongodb://((.+):(.*)@)");
    private static String decodePassword(final String servers) {
        String uri = servers;
        Matcher m = MONGO_URI.matcher(servers);
        if (m.find()) {
            try {
                String pwd = UrlEscapers.urlFormParameterEscaper().escape(PasswordUtil.decode(m.group(3)));
                uri = servers.substring(0, m.end(2) + 1) + pwd + servers.substring(m.end(1) - 1);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return uri;
    }

    public static void main(String[] args) {
        String pwd = PasswordUtil.decode("69D423EB481233FA908500397AAF4F6CF1B713BB4A1DAD32431D32D15CC2A42F");
        System.out.println(pwd);
    }
}
