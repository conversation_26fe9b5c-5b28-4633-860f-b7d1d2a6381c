package com.facishare.open.huawei.kit.web.handler;

import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.huawei.kit.web.consts.HuaweiProduceConstant;
import com.facishare.open.huawei.kit.web.result.QueryInstanceResult;
import com.facishare.open.huawei.kit.web.service.HuaweiOrderService;
import com.facishare.open.huawei.kit.web.templateData.KitVerifyTemplateData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 查询实例事件处理逻辑
 * <AUTHOR>
 * @date 20241107
 */
@Slf4j
@Component
public class QueryInstanceEventHandler extends HuaweiEventHandler {

    @Resource
    private HuaweiOrderService huaweiOrderService;


    @Override
    public String getSupportEventType() {
        return HuaweiProduceConstant.QUERY_INSTANCE;
    }

    @Override
    public void handle(KitVerifyTemplateData kitVerifyTemplateData) {
        //查询实例
    }

    @Override
    public TemplateResult result(KitVerifyTemplateData kitVerifyTemplateData) {
        //查询实例
        //只返回基础信息
        String queryInstanceInstanceId = String.valueOf(kitVerifyTemplateData.getIsvProduceReq().get("instanceId"));
        QueryInstanceResult queryInstanceResult = huaweiOrderService.queryInstance(queryInstanceInstanceId);
        return TemplateResult.newSuccess(queryInstanceResult);
    }
}
