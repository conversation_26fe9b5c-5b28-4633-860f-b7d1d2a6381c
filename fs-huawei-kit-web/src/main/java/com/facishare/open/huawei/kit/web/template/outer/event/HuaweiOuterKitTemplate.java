package com.facishare.open.huawei.kit.web.template.outer.event;

import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.outer.OuterEventHandlerTemplate;
import com.facishare.open.huawei.kit.web.config.ConfigCenter;
import com.facishare.open.huawei.kit.web.templateData.KitVerifyTemplateData;
import com.facishare.open.huawei.kit.web.result.Result;
import com.facishare.open.huawei.kit.web.result.ResultCodeEnum;
import com.facishare.open.huawei.kit.web.handler.HuaweiEventHandlerManager;
import com.facishare.open.huawei.kit.web.utils.VerifyMessageUtil;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 华为云外部kit事件处理器模板实现类
 * <AUTHOR>
 * @date 2024-09-19
 */

@Slf4j
@Component
public class HuaweiOuterKitTemplate extends OuterEventHandlerTemplate {
    @Resource
    private HuaweiEventHandlerManager huaweiEventHandlerManager;

    @Override
    public void onEventDecode(MethodContext context) {
        String traceId = UUID.randomUUID().toString();
        TraceUtils.initTraceId(traceId);
        log.info("HuaweiOuterKitTemplate.onEventDecode,context={}",context);

        KitVerifyTemplateData kitVerifyTemplateData = context.getData();

        try {
//            Map<String, Object> req = getParamMap(kitVerifyTemplateData.getIsvProduceReq());
            // 根据绑定的联营saas同步接口的密钥进行验签
            Result result = VerifyMessageUtil.verifyTenantSignature(kitVerifyTemplateData.getIsvProduceReq(), kitVerifyTemplateData.getRequest(), ConfigCenter.KIT_ACCESS_KEY);

            if (result.isSuccess()) {
                //准备下一步需要的context
                context.setData(kitVerifyTemplateData);
                context.setResult(TemplateResult.newSuccess());
            } else {
                context.getResult().setData(result);
                context.setResult(TemplateResult.newError(result.resultMsg));
            }
        } catch (Exception e) {
            LogUtils.info("HuaweiOuterKitTemplate.push,exception={}",e.getMessage());
            context.getResult().setData(Result.newError(ResultCodeEnum.OTHER_INNER_ERROR));
            context.setResult(TemplateResult.newError(ResultCodeEnum.OTHER_INNER_ERROR.getResultMsg()));
        }
    }

    @Override
    public void onEventFilter(MethodContext context) {
        log.info("HuaweiOuterKitTemplate.onEventFilter,context={}",context);
        context.setResult(TemplateResult.newSuccess());
    }

    @Override
    public void onEventHandle(MethodContext context) {
        log.info("HuaweiOuterKitTemplate.onEventHandle,context={}",context);
        KitVerifyTemplateData kitVerifyTemplateData = context.getData();

        TemplateResult templateResult = huaweiEventHandlerManager.handle(kitVerifyTemplateData.getType(), kitVerifyTemplateData);

        context.setResult(templateResult);

//        String type = kitVerifyTemplateData.getType();
//        // 根据行为不同，分发事件
//        switch (type) {
//            case "tenantSync":
//                //租户同步，走的是订单同步，只有这个事件有企业id和企业名称
//                com.facishare.open.feishu.syncapi.result.Result<List<EnterpriseBindEntity>> enterpriseBindList = enterpriseBindService.getEnterpriseBindList(kitVerifyTemplateData.getIsvProduceReq().get("tenantId").toString());
//                if(CollectionUtils.isNotEmpty(enterpriseBindList.getData())) {
//                    context.setResult(TemplateResult.newSuccess(Result2.newSuccess()));
//                    return;
//                }
//                ThreadPoolHelper.huaweiEventTypeThreadPool.submit(() -> {
//                    TemplateResult tenantSyncTemplateResult = huaweiOpenEnterpriseHandlerTemplate.execute(kitVerifyTemplateData);
//                    log.info("HuaweiOuterKitTemplate.onEventHandle,tenantSyncTemplateResult={}",tenantSyncTemplateResult);
//                });
//                context.setResult(TemplateResult.newSuccess(Result2.newSuccess()));
//                return;
//            case "applicationSync":
//                String clientSecret = "";
//                try {
//                    clientSecret = RSAEncryptionUtil.decrypt(kitVerifyTemplateData.getIsvProduceReq().get("clientSecret").toString(), ConfigCenter.PRIVATE_KEY_CLIENT);
//                } catch (Exception e) {
//                    throw new RuntimeException(e);
//                }
//                //应用同步，保存应用id
//                AppInfoEntity entity = AppInfoEntity
//                        .builder()
//                        .tenantKey(kitVerifyTemplateData.getIsvProduceReq().get("tenantId").toString())
//                        .appId(kitVerifyTemplateData.getIsvProduceReq().get("appId").toString())
//                        .applicants("manager")
//                        .clientId(kitVerifyTemplateData.getIsvProduceReq().get("clientId").toString())
//                        .clientSecret(clientSecret)
//                        .build();
//                appService.updateAppInfo(entity);
//                context.setResult(TemplateResult.newSuccess(Result2.newSuccess()));
//                return;
//            case "authSync":
//                //同步人员
//                ObjectMapper authSyncObjectMapper = new ObjectMapper();
//                AuthSyncInfo authSyncInfo = authSyncObjectMapper.convertValue(kitVerifyTemplateData.getIsvProduceReq(), AuthSyncInfo.class);
//
//                com.facishare.open.feishu.syncapi.result.Result<List<EnterpriseBindEntity>> authSyncEnterpriseBindList = enterpriseBindService.getEnterpriseBindList(kitVerifyTemplateData.getIsvProduceReq().get("tenantId").toString());
//                if(CollectionUtils.isEmpty(authSyncEnterpriseBindList.getData())) {
//                    //先存起来
//                    setTempRedis(authSyncInfo.getTenantId(), "authSync", authSyncInfo);
//                    context.setResult(TemplateResult.newSuccess(Result2.newSuccess()));
//                    return;
//                }
//
//                ThreadPoolHelper.huaweiEventTypeThreadPool.submit(() -> {
//                    huaweiContactsService.syncEmployee(authSyncInfo);
//                });
//
//                context.setResult(TemplateResult.newSuccess(Result.newSuccess()));
//                return;
//            case "singleOrgSync":
//                ObjectMapper singleOrgSyncObjectMapper = new ObjectMapper();
//                SingleOrgSyncInfo singleOrgSyncInfo = singleOrgSyncObjectMapper.convertValue(kitVerifyTemplateData.getIsvProduceReq(), SingleOrgSyncInfo.class);
//
//                com.facishare.open.feishu.syncapi.result.Result<List<EnterpriseBindEntity>> singleOrgSyncEnterpriseBindList = enterpriseBindService.getEnterpriseBindList(kitVerifyTemplateData.getIsvProduceReq().get("tenantId").toString());
//                if(CollectionUtils.isEmpty(singleOrgSyncEnterpriseBindList.getData())) {
//                    //先存起来
//                    setTempRedis(singleOrgSyncInfo.getTenantId(), "singleOrgSync", singleOrgSyncInfo);
//                    context.setResult(TemplateResult.newSuccess(Result2.newSuccess()));
//                    return;
//                }
//
//                ThreadPoolHelper.huaweiEventTypeThreadPool.submit(() -> {
//                    huaweiContactsService.syncDepartment(singleOrgSyncInfo);
//                });
//
//                context.setResult(TemplateResult.newSuccess(Result.newSuccess()));
//                return;
//            case "allOrgSync":
//                ObjectMapper allOrgSyncSyncObjectMapper = new ObjectMapper();
//                AllOrgSyncInfo allOrgSyncInfo = allOrgSyncSyncObjectMapper.convertValue(kitVerifyTemplateData.getIsvProduceReq(), AllOrgSyncInfo.class);
//
//                com.facishare.open.feishu.syncapi.result.Result<List<EnterpriseBindEntity>> allOrgSyncEnterpriseBindList = enterpriseBindService.getEnterpriseBindList(kitVerifyTemplateData.getIsvProduceReq().get("tenantId").toString());
//                if(CollectionUtils.isEmpty(allOrgSyncEnterpriseBindList.getData())) {
//                    //先存起来
//                    setTempRedis(allOrgSyncInfo.getTenantId(), "allOrgSync", allOrgSyncInfo);
//                    context.setResult(TemplateResult.newSuccess(Result2.newSuccess()));
//                    return;
//                }
//
//                ThreadPoolHelper.huaweiEventTypeThreadPool.submit(() -> {
//                    huaweiContactsService.syncAllDepartment(allOrgSyncInfo);
//                });
//
//                context.setResult(TemplateResult.newSuccess(Result.newSuccess()));
//                return;
//            default:
//                log.info("unsupported kitVerifyTemplateData: {}", kitVerifyTemplateData);
//                context.setResult(TemplateResult.newSuccess(Result.newSuccess()));
//        }
    }

    private Map<String, String> getParamMap(Map<String, Object> map) {
        Map<String, String> req = new HashMap<>();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String key = entry.getKey();
            String value = String.valueOf(entry.getValue());
            req.put(key, value);
        }
        return req;
    }

//    private void setTempRedis(String outTenantId, String type, Object object) {
//        log.info("HuaweiOuterKitTemplate.setTempRedis,outTenantId={},type={},object={}",outTenantId, type, object);
//        Gson gson = new Gson();
//        String tem = redisDataSource.getRedisClient().get("huawei-kit2-sync-" + outTenantId);
//        Map<String, Object> temSyncMap;
//        if(StringUtils.isEmpty(tem)) {
//            temSyncMap = new HashMap<>();
//        } else {
//            temSyncMap = gson.fromJson(tem, new TypeToken<Map<String, Object>>() {
//            });
//        }
//        temSyncMap.put(type, object);
//        redisDataSource.getRedisClient().set("huawei-kit2-sync-" + outTenantId, gson.toJson(temSyncMap));
//        redisDataSource.getRedisClient().expire("huawei-kit2-sync-" + outTenantId, 60 * 10L);
//    }
}
