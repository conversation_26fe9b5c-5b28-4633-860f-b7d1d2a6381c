package com.facishare.open.huawei.kit.web.controller;

import com.facishare.converter.EIEAConverter;
import com.facishare.open.huawei.kit.web.config.ConfigCenter;
import com.facishare.open.huawei.kit.web.result.Result;
import com.facishare.open.huawei.kit.web.BaseTest;
import com.facishare.open.huawei.kit.web.controller.outer.HuaweiExternalController;
import com.facishare.open.huawei.kit.web.utils.VerifyMessageUtil;
import org.junit.Test;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.annotation.Resource;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class FeishuExternalControllerTest extends BaseTest {
    @Resource
    private HuaweiExternalController huaweiExternalController;
    @Resource
    private EIEAConverter eieaConverter;

    @Test
    public void push() throws ServletException, IOException {

        int i = eieaConverter.enterpriseAccountToId("czx3540");
        System.out.println(i);
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        huaweiExternalController.loginAuth("c31eff7367d5405eaf2d5c7d13ee2514", "123456", null, response, request);
    }

    @Test
    public void verifyRequestParams() throws ServletException, IOException {
        Map<String, Object> requestParams = new HashMap<>();
        requestParams.put("activity", "newInstance");
        requestParams.put("businessId", "5bfb0b59-af1b-4705-80b0-3f40fdb111c3");
        requestParams.put("orderId", "MOCKPERIODYEARNEW");
        requestParams.put("orderLineId", "MOCKPERIODYEARNEW-000001");
        requestParams.put("testFlag", "1");
        Map<String, String[]> paramsMap = new HashMap<>();
        paramsMap.put("signature", new String[]{"C35D08CE9EBA9AD14FA65C6EE92C0B38D5B5105C7107F7DEE47235C9E236A00A"});
        paramsMap.put("timestamp", new String[]{"1730980816792"});
        paramsMap.put("nonce", new String[]{"BbZ6SurgatvMe4VfqcuX48jgnw37c1e1"});
        String accessKey = ConfigCenter.SIGN_ACCESS_KEY;
        Result result = VerifyMessageUtil.verifyRequestParams(requestParams, paramsMap, accessKey);
        System.out.println(result);
    }
}
