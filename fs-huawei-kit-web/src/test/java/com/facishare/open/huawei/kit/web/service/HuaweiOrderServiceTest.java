package com.facishare.open.huawei.kit.web.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.huawei.kit.web.BaseTest;
import com.facishare.open.huawei.kit.web.arg.CreateCustomerAndUpdateMappingArg;
import com.facishare.open.huawei.kit.web.arg.CreateOrderArg;
import com.facishare.open.huawei.kit.web.handler.NewInstanceEventHandler;
import com.facishare.open.huawei.kit.web.handler.RefreshInstanceEventHandler;
import com.facishare.open.huawei.kit.web.info.HuaweiOrderInfo;
import com.facishare.open.huawei.kit.web.model.HuaweiOrderDataModel;
import com.facishare.open.huawei.kit.web.result.OrderInfoResult;
import com.facishare.open.huawei.kit.web.result.result.Result;
import com.facishare.open.huawei.kit.web.template.outer.event.order.HuaweiOpenEnterpriseHandlerTemplate;
import com.facishare.open.huawei.kit.web.templateData.KitVerifyTemplateData;
import com.facishare.open.huawei.kit.web.utils.HuaweiApiUtil;
import com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.google.gson.reflect.TypeToken;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

public class HuaweiOrderServiceTest extends BaseTest {
    @Resource
    private HuaweiOrderService huaweiOrderService;
    @Autowired
    private HuaweiLoginService huaweiLoginService;
    @Resource
    private RedisDataSource redisDataSource;
    @Resource
    private HuaweiOpenEnterpriseHandlerTemplate huaweiOpenEnterpriseHandlerTemplate;
    @Resource
    private NewInstanceEventHandler newInstanceEventHandler;
    @Resource
    private RefreshInstanceEventHandler refreshInstanceEventHandler;

    @Test
    public void getSersaveOrderviceAuth() {
        HuaweiOrderDataModel huaweiOrderDataModel = new HuaweiOrderDataModel();
        huaweiOrderDataModel.setTenantId("382fd33e2ee64c26bc0ed2ab62bbd42c");
        huaweiOrderDataModel.setEnterpriseName("大风车");
        huaweiOrderDataModel.setInstanceId("47e255f4-f0e7-45ef-b46b-2e00fba3a4aa");
        huaweiOrderDataModel.setOrderId("CS2411151523ARO0M");

        Result<HuaweiOrderInfo> a = huaweiOrderService.saveOrder(huaweiOrderDataModel);

        System.out.println(a);
    }

    @Test
    public void createCustomerAndUpdateMapping() {
        CreateCustomerAndUpdateMappingArg arg = new CreateCustomerAndUpdateMappingArg();
        arg.setFsEa("huaweitest122");
        arg.setEnterpriseName("huaweitenantname122");
        arg.setOutEa("68cbc86abc2018ab880d92f36422fa0e222");
        arg.setOutEid("68cbc86abc2018ab880d92f36422fa0e222");
        arg.setInstallerMobilePhone("1000000000");
        arg.setInstallerUserId("123456");
        arg.setInstallerName("namename");

        Result<Void> a = huaweiOrderService.createCustomerAndUpdateMapping(arg);
        System.out.println(a);
    }

    @Test
    public void createOrder() {
        HuaweiOrderDataModel huaweiOrderDataModel = new HuaweiOrderDataModel();
        huaweiOrderDataModel.setTenantId("68cbc86abc2018ab880d92f36422fa0e");
        huaweiOrderDataModel.setEnterpriseName("huaweitenantname");
        huaweiOrderDataModel.setInstanceId("huaiweitest123456");
        huaweiOrderDataModel.setOrderId("MOCKPERIODYEARNEW");

        CreateCustomerAndUpdateMappingArg arg = new CreateCustomerAndUpdateMappingArg();
        arg.setFsEa("huaweitest");
        arg.setEnterpriseName("huaweitenantname");
        arg.setOutEa("68cbc86abc2018ab880d92f36422fa0e1");
        arg.setOutEid("68cbc86abc2018ab880d92f36422fa0e1");
        arg.setInstallerMobilePhone("1000000000");
        arg.setInstallerUserId("123456");
        arg.setInstallerName("namename");

        CreateOrderArg createOrderArg = new CreateOrderArg();
        createOrderArg.setFsEa("huaiweitest0001");
        createOrderArg.setOutEa("huaiweiinsstancetest0001");
        createOrderArg.setOrderId("MOCKPERIODYEARNEW");

        Result<Void> a = huaweiOrderService.createOrder(createOrderArg);
        System.out.println(a);
    }

    @Test
    public void getUserByCode() {
//        KitVerifyTemplateData huaweiOuterKitTemplate = new KitVerifyTemplateData();
//        TemplateResult result = huaweiOpenEnterpriseHandlerTemplate.execute(huaweiOuterKitTemplate);
//        System.out.println(result);

        Object a = huaweiLoginService.getUserByCode("QhSD4_su9YWLyfFCcP81X-Qt53yOkddjlHlZO3Uw7ow", "07454853c9800f6c0f3dc001d861de20_34f07482");
        System.out.println(a);
    }

    @Test
    public void saveOrderAndAddCrmOrder() {
        HuaweiOrderDataModel model = new HuaweiOrderDataModel();
        model.setOrderId("MOCKMONTYRENEW");
        model.setTenantId("huaiweiinsstancetest0007");
        model.setInstanceId("huaiweitest0007");

        Result<Void> a = huaweiOrderService.saveOrderAndAddCrmOrder(model);
        System.out.println(a);
    }

    @Test
    public void newInstanceEventHandler() {
        KitVerifyTemplateData kitVerifyTemplateData = new KitVerifyTemplateData();

        Map<String, Object> isvProduceReq = new HashMap<>();
        isvProduceReq.put("businessId", "huaiweitestzy005");
//        // orderId：主版本
//        isvProduceReq.put("orderId", "CS2507171918X6FI4");
        // orderId：其他版本
        isvProduceReq.put("orderId", "CS2507171908Y4KGC");
        kitVerifyTemplateData.setIsvProduceReq(isvProduceReq);
        
        newInstanceEventHandler.handle(kitVerifyTemplateData);
    }

    @Test
    public void refreshInstanceEventHandler() {
        KitVerifyTemplateData kitVerifyTemplateData = new KitVerifyTemplateData();

        Map<String, Object> isvProduceReq = new HashMap<>();
        isvProduceReq.put("instanceId", "huaiweitestzy005");
//        // orderId：主版本
//        isvProduceReq.put("orderId", "CS2507171918X6FI4");
        // orderId：其他版本
        isvProduceReq.put("orderId", "CS2507171908Y4KGC");
        kitVerifyTemplateData.setIsvProduceReq(isvProduceReq);

        refreshInstanceEventHandler.handle(kitVerifyTemplateData);
    }
}
