package com.facishare.open.outer.oa.connector.i18n;

import com.fxiaoke.i18n.client.I18nClient;
import com.github.trace.TraceContext;
import lombok.experimental.UtilityClass;

/**
 * 不需要spring管理的util
 * <AUTHOR> (^_−)☆
 */
@UtilityClass
public class I18nUtil {
    public String buildKey(String... suffixKeys) {
        StringBuilder i18nKey = new StringBuilder("erpdss_outer_oa_connector");
        for (String suffixKey : suffixKeys) {
            i18nKey.append(".").append(suffixKey);
        }
        return i18nKey.toString();
    }

    /**
     * 仅前端请求可用
     */
    public String getOrDefault(String i18nKey, String defaultValue) {
        String lang = getLocaleFromTrace();
        return I18nClient.getInstance().getOrDefault(i18nKey, 0, lang, defaultValue);
    }

    /**
     * 仅前端请求可用
     */
    public String getOrDefault(I18NStringEnum i18NStringEnum) {
        return getOrDefault(i18NStringEnum.getI18nKey(), i18NStringEnum.getI18nValue());
    }

    public static String getLocaleFromTrace() {
        String locale = TraceContext.get().getLocale();
        if (locale == null || locale.isEmpty()) {
            locale = "zh-CN";
        }
        return locale;
    }
}
