package com.facishare.open.ding.web.base;

import com.facishare.asm.api.auth.AuthXC;
import java.io.Serializable;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>类的详细说明</p>
 * @dateTime 2018/7/17 15:14 
 * <AUTHOR> <EMAIL>
 * @version 1.0 
 */
@Data
public class UserVo implements Serializable {
    private String fCode;
    private String enterpriseAccount;
    private Integer enterpriseId;
    private Integer employeeId;
    private String account;
    private String name;
    private String fullName;
    private String mobile;
    private String clientSource;
    private Long tokenNo;
    private String deviceId;
    private String sessionId;
    private String enterpriseName;
    private String enterpriseShortName;

    public UserVo(){
    }

    public UserVo(AuthXC authXC, String dataCenterId) {
        this.enterpriseAccount = authXC.getEnterpriseAccount();
        this.enterpriseId = authXC.getEnterpriseId();
        this.employeeId = authXC.getEmployeeId();
        this.account = authXC.getAccount();
        this.name = authXC.getName();
        this.fullName = authXC.getFullName();
        this.mobile = authXC.getMobile();
        this.clientSource = authXC.getClientSource();
        this.tokenNo = authXC.getTokenNo();
        this.deviceId = authXC.getDeviceId();
        this.sessionId = authXC.getSessionId();
    }

    public UserVo(AuthXC authXC, String enterpriseName, String enterpriseShortName, String dataCenterId) {
        this.enterpriseAccount = authXC.getEnterpriseAccount();
        this.enterpriseId = authXC.getEnterpriseId();
        this.employeeId = authXC.getEmployeeId();
        this.account = authXC.getAccount();
        this.name = authXC.getName();
        this.fullName = authXC.getFullName();
        this.mobile = authXC.getMobile();
        this.clientSource = authXC.getClientSource();
        this.tokenNo = authXC.getTokenNo();
        this.deviceId = authXC.getDeviceId();
        this.sessionId = authXC.getSessionId();
        this.enterpriseName = enterpriseName;
        this.enterpriseShortName = enterpriseName;
    }

    public boolean isInvalid(){
        if(StringUtils.isBlank(enterpriseAccount)){
            return true;
        }
        if(employeeId==null||employeeId<=0){
            return true;
        }
        return false;
    }
}
