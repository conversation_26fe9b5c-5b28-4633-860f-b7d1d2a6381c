package com.facishare.open.ding.provider.arg;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/5/7 16:00  应用开通绑定的企业
 * @Version 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "ding_corp_mapping")
public class DingCorpMappingEntity implements Serializable {
    @Id
    private Long id;
    private Integer ei;
    private String ea;
    private String enterpriseName;
    private String dingCorpId;
    private String dingMainCorpId;
    private Integer bindType;
    private Integer status;
    private Long appCode;
    private Integer repeatIndex;
    private Integer isInit;
    private Integer connector;//连接器的连接状态，0 代表未安装连接器，1 代表已安装连接器
    private String category;//默认的产品分类code
    private String extend;//扩展字段
    private Date createTime;
    private Date updateTime;
}
