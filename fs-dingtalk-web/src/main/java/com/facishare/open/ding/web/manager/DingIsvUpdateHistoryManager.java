package com.facishare.open.ding.web.manager;

import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.ding.api.model.AppParams;
import com.facishare.open.ding.api.result.AppAuthResult;
import com.facishare.open.ding.api.vo.DingCorpMappingVo;
import com.facishare.open.ding.cloud.constants.ConfigCenter;
import com.facishare.open.ding.provider.dao.*;
import com.facishare.open.ding.transfer.handler.DingTalkIsvCorpMappingHandler;
import com.facishare.open.ding.transfer.handler.DingTalkIsvHandler;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.*;
import com.facishare.open.oa.base.dbproxy.pg.mapper.dingtalkisv.DingRefuseDataPgMapper;
import com.facishare.open.outer.oa.connector.common.api.admin.DingConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.constants.GlobalValue;
import com.facishare.open.outer.oa.connector.common.api.enums.*;
import com.facishare.open.outer.oa.connector.i18n.I18NStringEnum;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Component
public class DingIsvUpdateHistoryManager {
    @Autowired
    private AppAuthDao appAuthDao;
    @Autowired
    private DingCorpMappingDao dingCorpMappingDao;
    @Autowired
    private DingMappingEmployeeDao dingMappingEmployeeDao;
    @Autowired
    private DingRefuseDataDao dingRefuseDataDao;
    @Autowired
    private DingTaskDao dingTaskDao;
    @Autowired
    private OrderInfoDao orderInfoDao;
    @Autowired
    private OuterOaAppInfoManager outerOaAppInfoManager;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;
    @Autowired
    private OuterOaEmployeeDataManager outerOaEmployeeDataManager;
    @Autowired
    private DingRefuseDataPgMapper dingRefuseDataPgMapper;
    @Autowired
    private OuterOaMessageBindManager outerOaMessageBindManager;
    @Autowired
    private OuterOaOrderInfoManager outerOaOrderInfoManager;
    @Autowired
    private DingTalkIsvCorpMappingHandler dingTalkIsvHandler;
    @Autowired
    private EIEAConverter eieaConverter;


    public void transferData(String corpId){
        List<String> allTenantIds = dingTalkIsvHandler.getAllTenantIds();
        for (String allTenantId : allTenantIds) {
            Integer ei=Integer.valueOf(allTenantId);
            String enterpriseIdToAccount = eieaConverter.enterpriseIdToAccount(Integer.valueOf(allTenantId));
            List<DingCorpMappingVo> dingCorpMappingVos = dingCorpMappingDao.queryByEa(enterpriseIdToAccount);
            //转发企业绑定
            for (DingCorpMappingVo dingCorpMappingVo : dingCorpMappingVos) {
                //保存企业绑定关系
                //从应用里面拿到suite_id
                List<AppAuthResult> appAuthResults = appAuthDao.conditionAppAuth(dingCorpMappingVo.getDingCorpId(), dingCorpMappingVo.getAppCode(), null);
                String suiteId=ConfigCenter.CRM_SUITE_ID;
                if(CollectionUtils.isNotEmpty(appAuthResults)){
                    suiteId=appAuthResults.get(0).getSuiteId().toString();
                    //需要插入应用id
                }


                DingConnectorVo dingConnectorVo=new DingConnectorVo();
                dingConnectorVo.setDingCorpId(dingCorpMappingVo.getDingCorpId());
                dingConnectorVo.setExtend(dingCorpMappingVo.getExtend());
                dingConnectorVo.setDingMainCorpId(dingCorpMappingVo.getDingMainCorpId());
                dingConnectorVo.setAppKey(dingCorpMappingVo.getAppCode().toString());
                if(dingCorpMappingVo.getAppCode().equals(ConfigCenter.APP_CRM_ID)){
                    //默认只有crm应用才通知待办提醒
                    dingConnectorVo.setAlertConfig(true);
                    dingConnectorVo.setAlertTypes(Lists.newArrayList(AlertTypeEnum.CRM_TODO, AlertTypeEnum.CRM_NOTIFICATION));
                }
                AppParams appParams=ConfigCenter.APP_PARAMS_MAP.get(suiteId);
                dingConnectorVo.setAppType(OuterOaAppInfoTypeEnum.isv);
                dingConnectorVo.setEnterpriseName(dingCorpMappingVo.getEnterpriseName());
                dingConnectorVo.setDataCenterName(I18NStringEnum.valueOf(appParams.getI18nKey()).getI18nKey());

                dingConnectorVo.setRepeatIndex(dingCorpMappingVo.getRepeatIndex());
                OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity=new OuterOaEnterpriseBindEntity();
                outerOaEnterpriseBindEntity.setId(dingCorpMappingVo.getId().toString());
                outerOaEnterpriseBindEntity.setChannel(ChannelEnum.dingding);
                outerOaEnterpriseBindEntity.setFsEa(enterpriseIdToAccount);
                outerOaEnterpriseBindEntity.setOutEa(dingCorpMappingVo.getDingCorpId());
                outerOaEnterpriseBindEntity.setAppId(dingCorpMappingVo.getAppCode().toString());
                outerOaEnterpriseBindEntity.setConnectInfo(JSON.toJSONString(dingCorpMappingVo));
                outerOaEnterpriseBindEntity.setBindType(BindTypeEnum.auto);
                outerOaEnterpriseBindEntity.setBindStatus(BindStatusEnum.normal);
                outerOaEnterpriseBindEntity.setCreateTime(System.currentTimeMillis());
                outerOaEnterpriseBindEntity.setUpdateTime(System.currentTimeMillis());


            }

        }
    }



}
