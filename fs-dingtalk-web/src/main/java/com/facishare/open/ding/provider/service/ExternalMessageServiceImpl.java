package com.facishare.open.ding.provider.service;

import com.facishare.open.ding.api.result.*;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.provider.arg.BaseExternalMessageArg;
import com.facishare.open.ding.provider.arg.TextCardMessageArg;
import com.facishare.open.ding.provider.manager.DingEnterpriseManager;
import com.facishare.open.ding.provider.manager.DingMappingEmployeeManager;
import com.facishare.open.ding.provider.manager.MessageSendManager;
import com.facishare.restful.common.StopWatch;
import com.fxiaoke.message.extrnal.platform.api.ExternalMessageService;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextCardMessageArg;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextMessageArg;
import com.fxiaoke.message.extrnal.platform.model.result.SendTextCardMessageResult;
import com.fxiaoke.message.extrnal.platform.model.result.SendTextMessageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR> crm提醒
 * @version 1.0
 * @date 2019-09-16 12:05
 */
@Slf4j
@Service("externalMessageServiceImpl")
public class ExternalMessageServiceImpl implements ExternalMessageService {
    @Autowired
    private DingEnterpriseManager dingEnterpriseManager;

    @Autowired
    private DingMappingEmployeeManager employeeManager;

    private static final String MID_URL = "https://www.ceshi112.com/dingtalk/business/authorize?direct_uri=";

    //客户端
    private static final String DING_SINGLE_URL = "https://oapi.dingtalk.com/connect/oauth2/sns_authorize?response_type=code&scope=snsapi_auth&state=STATE";

    //网页版
    private static final String DING_SINGLE_URL_WEB = "https://oapi.dingtalk.com/connect/oauth2/sns_authorize?response_type=code&scope=snsapi_login&state=STATE";

    private static final String MESSAGE_TYPE = "action_card";

    private static final  Integer closeStatus=0;

    @Autowired
    private MessageSendManager messageSendManager;

    @Override
    public SendTextMessageResult sendTextMessage(SendTextMessageArg arg) {
        StopWatch textMessage=StopWatch.create("trace sendTextMessage ea:"+arg.getEa());
        log.info("ExternalMessage service sendTextMessage arg:{}",arg);
        SendTextMessageResult result= new SendTextMessageResult();
        result.setCode(200);

        if (Objects.isNull(arg)){
            log.warn("sendMessage param is null");
            result.setMessage("sendMessage param is null");
            return result;
        }
        Integer ei = arg.getEi();
        Result<DingEnterpriseResult> enterpriseResult = dingEnterpriseManager.queryEnterpriseByEi(ei);

        textMessage.lap("queryEnterprise");
        if (Objects.isNull(enterpriseResult) || Objects.isNull(enterpriseResult.getData())){
            log.warn("the fx enterprise is not binded, ei={}.", ei);
            result.setMessage("the fx enterprise is not binded");
            return result;
        }
        if(enterpriseResult.getData().getAlertStatus().equals(closeStatus)){
            //已经关闭就不再发送crm提醒消息
            log.info("crm alert status was closed arg:{}",arg);
            result.setMessage("crm alert was closed");
            return result;
        }
        BaseExternalMessageArg textMessageArg=new BaseExternalMessageArg();
        BeanUtils.copyProperties(arg,textMessageArg);
        BaseResult baseResult = messageSendManager.commonSendMenssage(textMessageArg, enterpriseResult.getData());
        textMessage.lap("commonSendMenssage");
        textMessage.log();
        log.info("external message commonSendMessage arg:{},enterprise:{},baseResult:{}",textMessageArg,enterpriseResult.getData(),baseResult);
        result.setMessage(baseResult.getMessage());
        return result;

    }

    @Override
    public SendTextCardMessageResult sendTextCardMessage(SendTextCardMessageArg arg) {
        StopWatch sendTextCardMessage=StopWatch.create("trace sendTextCardMessage ea:"+arg.getEa());
        log.info("ExternalMessage service sendTextCardMessage arg:{}",arg);
        SendTextCardMessageResult result= new SendTextCardMessageResult();
        result.setCode(200);

        if (Objects.isNull(arg)){
            log.warn("sendMessage param is null");
            result.setMessage("sendMessage param is null");
            return result;
        }
        Integer ei = arg.getEi();
        Result<DingEnterpriseResult> enterpriseResult = dingEnterpriseManager.queryEnterpriseByEi(ei);
        sendTextCardMessage.lap("queryEnterpriseByEi");
        if (Objects.isNull(enterpriseResult) || Objects.isNull(enterpriseResult.getData())){
            log.warn("the fx enterprise is not binded, ei={}.", ei);
            result.setMessage("the fx enterprise is not binded");
            return result;
        }
        if(enterpriseResult.getData().getAlertStatus().equals(closeStatus)){
            //已经关闭就不再发送crm提醒消息
            log.info("crm alert status was closed arg:{}",arg);
            result.setMessage("crm alert was closed");
            return result;
        }
        TextCardMessageArg textCardMessageArg=new TextCardMessageArg();
        BeanUtils.copyProperties(arg,textCardMessageArg);

        BaseResult baseResult = messageSendManager.commonSendMenssage(textCardMessageArg, enterpriseResult.getData());
        sendTextCardMessage.lap("commonSendMenssage");
        log.info("external message commonSendMessage arg:{},enterprise:{},baseResult:{}",textCardMessageArg,enterpriseResult.getData(),baseResult);
        result.setMessage(baseResult.getMessage());
        return result;

    }


}
