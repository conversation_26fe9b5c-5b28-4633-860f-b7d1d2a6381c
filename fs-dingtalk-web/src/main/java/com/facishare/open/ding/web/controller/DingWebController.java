package com.facishare.open.ding.web.controller;

import com.facishare.open.ding.api.service.DingCorpMappingService;
import com.facishare.open.ding.api.service.cloud.CloudEmpService;
import com.facishare.open.ding.api.service.cloud.CloudOrderService;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.web.base.BaseController;
import com.facishare.open.ding.web.base.UserVo;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.ding.web.template.inner.login.DingtalkLoginTemplate;
import com.facishare.open.ding.web.template.model.FsUserModel;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.info.EnterpriseTrialInfo;
import com.facishare.open.outer.oa.connector.common.api.info.FsEmployeeDetailInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * Created by system on 2018/4/4.
 */
@Slf4j
@RestController
@RequestMapping("/web")
public class DingWebController extends BaseController{

    @Autowired
    private CloudOrderService cloudOrderService;
    @Autowired
    private CloudEmpService cloudEmpService;
    @Autowired
    private DingCorpMappingService dingCorpMappingService;
    @Autowired
    private DingtalkLoginTemplate dingtalkLoginTemplate;

    /**
     * 留资功能使用的接口，得到企业的最新订单情况和绑定类型
     * @return
     */
    @RequestMapping(value = "/order/getEnterpriseTrialInfo", method = RequestMethod.POST)
    @ResponseBody
    public Result<EnterpriseTrialInfo> getEnterpriseTrialInfo() {
        UserVo userVo = getUserVo();
        if(ObjectUtils.isEmpty(userVo)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        return cloudOrderService.getEnterpriseTrialInfo(userVo.getEnterpriseAccount());
    }

    /**
     * 留资功能使用的接口，得到员工在纷享crm的一些信息
     * @return
     */
    @RequestMapping(value = "/employee/getFsCurEmployeeDetailInfo", method = RequestMethod.POST)
    @ResponseBody
    public Result<FsEmployeeDetailInfo> getFsCurEmployeeDetailInfo() {
        UserVo userVo = getUserVo();
        if(ObjectUtils.isEmpty(userVo)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        return cloudEmpService.getFsCurEmployeeDetailInfo(userVo.getEnterpriseId(), userVo.getEmployeeId());
    }

    /**
     * 更新企业绑定表拓展字段
     * 1、留资功能：函数调用此接口更新拓展字段的是否已留资的字段
     * @return
     */
    @RequestMapping(value = "/enterprise/updateEnterpriseExtend", method = RequestMethod.POST)
    @ResponseBody
    public Result<Void> updateEnterpriseExtend(@RequestParam String fsEa,
                                               @RequestParam String extendField,
                                               @RequestParam Object extendValue) {
        UserVo userVo = getUserVo();
        if(ObjectUtils.isEmpty(userVo)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        return dingCorpMappingService.updateEnterpriseExtend(fsEa, extendField, extendValue);
    }

    /**
     * 用ticket获取纷享用户信息，给俊文使用
     * @param ticket
     * @return
     */
    @Deprecated
    @RequestMapping(value="/getFsUserInfo",method = RequestMethod.GET)
    @ResponseBody
    public Result<FsUserModel> getFsUserInfo(@RequestParam String ticket) {
        LogUtils.info("DingWebController.getFsUserInfo,ticket={}",ticket);
        MethodContext context = MethodContext.newInstance(ticket);
        dingtalkLoginTemplate.getFsUserInfoByTicket(context);
        Result<FsUserModel> fsUser = context.getResultData();
        LogUtils.info("DingWebController.getFsUserInfo,fsUser={}",fsUser);
        return fsUser;
    }
}
