package com.facishare.open.ding.cloud.mq;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaConfigInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeDataEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaConfigInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeDataManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.*;
import com.facishare.open.outer.oa.connector.common.api.info.SettingAccountRulesModel;
import com.facishare.open.outer.oa.connector.common.api.result.SystemFieldMappingResult;
import com.facishare.organization.api.event.OrganizationChangedListener;
import com.facishare.organization.api.event.organizationChangeEvent.DepartmentChangeEvent;
import com.facishare.organization.api.event.organizationChangeEvent.EmployeeChangeEvent;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;


/**
 * 监听组织架构变化，缓存数据
 * <AUTHOR>
 * @Version 1.0
 */
@Slf4j
@Component("fsOrganizationChangedListener")
public class CrmEmployeeChangedListener extends OrganizationChangedListener {

    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private OuterOaConfigInfoManager outerOaConfigInfoManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private FsEmployeeServiceProxy fsEmployeeServiceProxy;
    @Autowired
    private OuterOaEmployeeDataManager outerOaEmployeeDataManager;
    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;


    public CrmEmployeeChangedListener() {
        super("fs-open-dingtalk-isv-mq");
    }

    @Override
    protected void onEmployeeChanged(EmployeeChangeEvent event) {
        String traceId = TraceUtils.getTraceId();
        if (StringUtils.isEmpty(traceId)) {
            TraceUtils.initTraceId(UUID.randomUUID() + "_" + event.getEnterpriseAccount());
        }
        log.info("FeishuFsOrganizationChangedListenerTemplate.onEmployeeChanged,context={}", event);
        String fsEa = event.getEnterpriseAccount();
        List<OuterOaEnterpriseBindEntity> entities = outerOaEnterpriseBindManager.getEntitiesNormalByChanelEnums(Lists.newArrayList(ChannelEnum.dingding), fsEa, null);
        if (entities == null || entities.isEmpty()) {
            return;
        }
        for (OuterOaEnterpriseBindEntity entity : entities) {
            OuterOaConfigInfoEntity entityByDataCenterId = outerOaConfigInfoManager.getEntityByDataCenterId(OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES, entity.getId());
            SettingAccountRulesModel settingAccountRulesModel = JSON.parseObject(entityByDataCenterId.getConfigInfo(), SettingAccountRulesModel.class);
            if (settingAccountRulesModel.getSyncTypeEnum() == EnterpriseConfigAccountSyncTypeEnum.accountBind && settingAccountRulesModel.getBindTypeEnum() == BindTypeEnum.auto) {
                LogUtils.info("enterprise need auto bind:{}", entity.getFsEa());
                //需要读取匹配的字段：
                OuterOaConfigInfoEntity uniqueEmployeeData = outerOaConfigInfoManager.getEntityByDataCenterId(OuterOaConfigInfoTypeEnum.EMPLOYEE_UNIQUE_IDENTITY, entity.getId());
                SystemFieldMappingResult systemFieldResult = JSONObject.parseObject(uniqueEmployeeData.getConfigInfo(), SystemFieldMappingResult.class);
                SystemFieldMappingResult.ItemFieldMapping itemFieldMapping = systemFieldResult.getItemFieldMappings().stream().filter(item -> item.getMatchUnique()).findFirst().orElse(null);
                if (ObjectUtil.isNotEmpty(itemFieldMapping)) {
                    //
                    Integer enterpriseId = eieaConverter.enterpriseAccountToId(fsEa);
                    Integer fsEmployeeId = event.getNewEmployeeDto().getEmployeeId();
                    com.facishare.open.order.contacts.proxy.api.result.Result<ObjectData> detail = fsEmployeeServiceProxy.detail(enterpriseId.toString(), event.getNewEmployeeDto().getEmployeeId() + "");
                    if (detail.isSuccess() && ObjectUtils.isNotEmpty(detail.getData())) {
                        Object crmObjectValue = detail.getData().get(itemFieldMapping.getCrmFieldApiName());
                        if (ObjectUtils.isEmpty(crmObjectValue)) {
                            log.info("filter crmvalue is empty");
                            continue;
                        }
                        //这个员工已经绑定了，那就不继续
                        OuterOaEmployeeBindEntity employeeBindEntity = outerOaEmployeeBindManager.getEntitiesByDcId(entity.getId(), String.valueOf(fsEmployeeId), null);
                        if (ObjectUtils.isNotEmpty(employeeBindEntity)) {
                            log.info("employee data had bind");
                            continue;
                        }
                        List<OuterOaEmployeeDataEntity> outerOaEmployeeDataEntities = outerOaEmployeeDataManager.selectByField(entity, itemFieldMapping.getOuterOAFieldApiName(), String.valueOf(crmObjectValue));
                        if (CollectionUtils.isEmpty(outerOaEmployeeDataEntities)) {
                            continue;
                        }

                        //还有一种情况是外部人员绑定了其他CRM人员，这个需要continue
                        employeeBindEntity = outerOaEmployeeBindManager.getEntitiesByDcId(entity.getId(), null, outerOaEmployeeDataEntities.get(0).getOutUserId());
                        if (ObjectUtils.isNotEmpty(employeeBindEntity)) {
                            log.info("employee data had bind");
                            continue;
                        }
                        if (ObjectUtils.isNotEmpty(outerOaEmployeeDataEntities)) {
                            //匹配插入绑定
                            OuterOaEmployeeBindEntity outerOaEmployeeBindEntity = new OuterOaEmployeeBindEntity();
                            outerOaEmployeeBindEntity.setChannel(entity.getChannel());
                            outerOaEmployeeBindEntity.setBindStatus(BindStatusEnum.normal);
                            outerOaEmployeeBindEntity.setId(IdGenerator.get());
                            outerOaEmployeeBindEntity.setDcId(entity.getId());
                            outerOaEmployeeBindEntity.setAppId(entity.getAppId());
                            outerOaEmployeeBindEntity.setFsEmpId(String.valueOf(event.getNewEmployeeDto().getEmployeeId()));
                            outerOaEmployeeBindEntity.setOutEmpId(outerOaEmployeeDataEntities.get(0).getOutUserId());
                            outerOaEmployeeBindEntity.setUpdateTime(System.currentTimeMillis());
                            outerOaEmployeeBindEntity.setCreateTime(System.currentTimeMillis());
                            outerOaEmployeeBindEntity.setFsEa(fsEa);
                            outerOaEmployeeBindEntity.setOutEa(entity.getOutEa());
                            LogUtils.info("fsemp tripper auto bind:{}-{}-{}", fsEa, fsEmployeeId, crmObjectValue);
                            outerOaEmployeeBindManager.batchUpsert(Lists.newArrayList(outerOaEmployeeBindEntity));
                        }

                    }
                }
            }
        }
    }

}
