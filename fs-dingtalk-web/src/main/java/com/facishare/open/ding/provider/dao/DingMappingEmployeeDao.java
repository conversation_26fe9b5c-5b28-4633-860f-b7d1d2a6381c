package com.facishare.open.ding.provider.dao;

import com.facishare.open.ding.api.result.BindFxUserResult;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.common.model.User;
import com.facishare.open.ding.provider.entity.DingMappingEmployee;
import com.github.mybatis.mapper.ICrudMapper;
import org.apache.ibatis.annotations.*;

import java.util.List;

public interface DingMappingEmployeeDao extends ICrudMapper<DingMappingEmployee> {
    /**
     * 根据企业ID查询员工映射
     * @param ei 企业ID
     * @return 员工映射列表
     */
    List<DingMappingEmployee> findByEi(Integer ei);

    @Select("<script>SELECT * FROM ding_mapping_employee WHERE ei = #{ei} " +
            "<choose>" +
            "<when test='bindStatus==0'>AND bind_status = 0 </when>" +
            "<when test='bindStatus==1'>AND bind_status = 1</when>" +
            "<when test='bindStatus==2'>AND bind_status = 2</when>" +
            "<when test='bindStatus==3'>AND bind_status > 0</when>" +
            "<when test='bindStatus==5'>AND bind_status > 0 AND ((employee_status IS NOT NULL AND employee_status!=1) OR ding_employee_status!=1)</when>" +
            "<otherwise>" +
            "</otherwise>" +
            "</choose>" +
            "<if test=\"dingName!=null and dingName!=''\"> " +
            "AND ding_employee_name = #{dingName} " +
            "</if>" + "<if test=\"dingDeptId!=null and dingDeptId!=''\"> " +
            "AND ding_dept_id = #{dingDeptId} " +
            "</if>" +
            "<if test=\"dingPhone!=null and dingPhone!=''\"> " +
            "AND ding_employee_phone = #{dingPhone} " +
            "</if>" +
            "group by ding_employee_id " +
            "ORDER BY update_time,ding_employee_id DESC " +
            "<if test='offset!=null'> " +
            "LIMIT #{offset},#{pageSize}" +
            "</if>" +
            "</script>")
    List<DingMappingEmployee> findByEI(@Param("ei") Integer ei, @Param("bindStatus") Integer bindStatus,
                                       @Param("offset") Integer offset, @Param("pageSize") Integer pageSize,
                                       @Param("dingName") String dingName, @Param("dingPhone") String dingPhone, @Param("dingDeptId") Long dingDeptId);


    //条件查询返回人员
    @Select("<script>SELECT * FROM ding_mapping_employee WHERE ei = #{ei} " +
            "<choose>" +
            "<when test='bindStatus==0'>AND bind_status = 0 </when>" +
            "<when test='bindStatus==1'>AND bind_status = 1</when>" +
            "<when test='bindStatus==2'>AND bind_status = 2</when>" +
            "<when test='bindStatus==3'>AND bind_status > 0</when>" +
            "<when test='bindStatus==5'>AND bind_status > 0 AND ((employee_status IS NOT NULL AND employee_status!=1) OR ding_employee_status!=1)</when>" +
            "<otherwise>" +
            "</otherwise>" +
            "</choose>" +
            "<if test=\"dingNameOrPhone!=null and dingNameOrPhone!=''\"> " +
            "AND ding_employee_name like  concat('%',#{dingNameOrPhone},'%') or ding_employee_phone like concat('%',#{dingNameOrPhone},'%')" +
            "</if>" +
            "<if test=\"deptList!=null and deptList!=''\"> " +
            "AND ding_dept_id in " +
            "  <foreach collection=\"deptList\" item=\"item\" index=\"index\"\n" +
            "        open=\"(\" separator=\",\" close=\")\">\n" +
            "    #{item}\n" +
            "    </foreach> " +
            "</if>" +
            "group by ding_employee_id " +
            "ORDER BY update_time  " +
            "<if test='offset!=null'> " +
            "LIMIT #{offset},#{pageSize}" +
            "</if>" +
            "</script>")
    List<DingMappingEmployee> conditionQueryEmployee(@Param("ei") Integer ei, @Param("bindStatus") Integer bindStatus,
                                                     @Param("offset") Integer offset, @Param("pageSize") Integer pageSize,
                                                     @Param("dingNameOrPhone") String dingNameOrPhone, @Param("deptList") List<Long> deptList);


    @Select("<script>SELECT COUNT(distinct(ding_employee_id)) FROM ding_mapping_employee WHERE ei = #{ei} " +
            "<choose>" +
            "<when test='bindStatus==0'>AND bind_status = 0 </when>" +
            "<when test='bindStatus==1'>AND bind_status = 1 AND ding_employee_status=1</when>" +
            "<when test='bindStatus==2'>AND bind_status = 2 AND employee_status=1 AND ding_employee_status=1</when>" +
            "<when test='bindStatus==3'>AND bind_status > 0</when>" +
            "<when test='bindStatus==5'>AND bind_status > 0 AND ((employee_status IS NOT NULL AND employee_status!=1) OR ding_employee_status!=1)</when>" +
            "<otherwise>" +
            "</otherwise>" +
            "</choose> " +
            "<if test=\"dingName!=null and dingName!=''\"> " +
            "AND ding_employee_name = #{dingName} " +
            "</if>" +
            "<if test=\"dingPhone!=null and dingPhone!=''\"> " +
            "AND ding_employee_phone = #{dingPhone} " +
            "</if>" +
            "</script>")
    Integer findCountByEI(@Param("ei") Integer ei, @Param("bindStatus") Integer bindStatus,
                          @Param("dingName") String dingName, @Param("dingPhone") String dingPhone);


    //conditionCount
    @Select("<script>SELECT COUNT(distinct(ding_employee_id)) FROM ding_mapping_employee WHERE ei = #{ei} " +
            "<choose>" +
            "<when test='bindStatus==0'>AND bind_status = 0 </when>" +
            "<when test='bindStatus==1'>AND bind_status = 1 AND ding_employee_status=1</when>" +
            "<when test='bindStatus==2'>AND bind_status = 2 AND employee_status=1 AND ding_employee_status=1</when>" +
            "<when test='bindStatus==3'>AND bind_status > 0</when>" +
            "<when test='bindStatus==5'>AND bind_status > 0 AND ((employee_status IS NOT NULL AND employee_status!=1) OR ding_employee_status!=1)</when>" +
            "<otherwise>" +
            "</otherwise>" +
            "</choose> " +
            "<if test=\"dingNameOrPhone!=null and dingNameOrPhone!=''\"> " +
            "AND ding_employee_name like  concat('%',#{dingNameOrPhone},'%') or ding_employee_phone like concat('%',#{dingNameOrPhone},'%')" +
            "</if>" +
            "<if test=\"deptList!=null and deptList!=''\"> " +
            "AND ding_dept_id in " +
            "  <foreach collection=\"deptList\" item=\"item\" index=\"index\"\n" +
            "        open=\"(\" separator=\",\" close=\")\">\n" +
            "    #{item}\n" +
            "    </foreach> " +
            "</if>" +
            "</script>")
    Integer conditionCount(@Param("ei") Integer ei, @Param("bindStatus") Integer bindStatus,
                           @Param("dingNameOrPhone") String dingNameOrPhone,@Param("deptList") List<Long> deptList);


    @Select("<script>SELECT COUNT(id) FROM ding_mapping_employee WHERE ei = #{ei} " +
            "</script>")
    Integer findAllCountByEI(@Param("ei") Integer ei);

    @Insert("<script>" +
            "INSERT  INTO ding_mapping_employee" +
            "(ei,ding_employee_status,ding_employee_name,ding_employee_id,ding_employee_phone," +
            "ding_union_id,ding_dept_id,ding_dept_name,bind_status,create_by,update_by) values " +
            "<foreach collection=\"list\" item=\"item\"  separator=\",\" >" +
            "(#{ei},1," +
            "#{item.name},#{item.userid},#{item.mobile}," +
            "#{item.unionid},#{deptId},#{deptName},0,#{userId},#{userId})" +
            "</foreach>" +
            "ON DUPLICATE KEY UPDATE update_time=now()"+
            "</script>")
    Integer initMappingEmployee(@Param("list") List<User> list,
                                @Param("ei") Integer ei,
                                @Param("userId") Integer userId,
                                @Param("deptId") Long deptId,
                                @Param("deptName") String deptName);

    @Delete("<script> DELETE FROM ding_mapping_employee " +
            "WHERE ei = #{ei} AND ding_employee_id = #{dingEmployeeId}</script>")
    Integer deleteBind(@Param("ei") Integer ei, @Param("dingEmployeeId") String dingEmployeeId);

    @Delete("<script> DELETE FROM ding_mapping_employee " +
            "WHERE ei = #{ei} AND employee_id = #{fsEmployeeId}</script>")
    Integer deleteBindByFsId(@Param("ei") Integer ei, @Param("fsEmployeeId") Integer fsEmployeeId);

    @Delete("<script> DELETE FROM ding_mapping_employee " +
            "WHERE ei = #{ei} AND ding_employee_id = #{dingEmployeeId} and ding_dept_id=#{dingDeptId}</script>")
    Integer deleteByDeptId(@Param("ei") Integer ei, @Param("dingEmployeeId") String dingEmployeeId,@Param("dingDeptId") Long dingDeptId);


    @Delete("<script> UPDATE ding_mapping_employee " +
            "SET bind_status=0,employee_phone=null,employee_id =null, employee_name = null,employee_status=null " +
            "WHERE ei = #{ei} AND ding_employee_id = #{dingEmployeeId}</script>")
    Integer relieveBind(@Param("ei") Integer ei, @Param("dingEmployeeId") String dingEmployeeId);

    @Update("UPDATE ding_mapping_employee SET employee_phone = #{kcMappingEmployee.employeePhone}," +
            "employee_id = #{kcMappingEmployee.employeeId}," +
            "employee_name = #{kcMappingEmployee.employeeName}," +
            "employee_status = #{kcMappingEmployee.employeeStatus}," +
            "bind_status = #{kcMappingEmployee.bindStatus}," +
            "update_by = #{kcMappingEmployee.updateBy} " +
            "WHERE id = #{kcMappingEmployee.id}")
    Integer updateByBindId(@Param("kcMappingEmployee") DingMappingEmployeeResult kcMappingEmployee);

    @Update("<script>" +
            "UPDATE ding_mapping_employee " +
            "SET employee_id = #{emp.employeeId},employee_status = #{emp.employeeStatus}," +
            "employee_name = #{emp.employeeName},employee_phone = #{emp.employeePhone}," +
            "bind_status = #{emp.bindStatus}," +
            "crm_dept_id = #{emp.crmDeptId}," +
            "update_time = NOW()," +
            "update_by = #{emp.updateBy} " +
            "WHERE ei = #{emp.ei} AND ding_employee_id = #{emp.dingEmployeeId}" +
            "<if test=\"emp.dingDeptId!=null\"> " +
            "AND ding_dept_id = #{emp.dingDeptId} " +
            "</if>" +
            "</script>")
    Integer updateMappingEmployee(@Param("emp") DingMappingEmployeeResult emp);

    @Select("SELECT COUNT(employee_id) FROM ding_mapping_employee WHERE ei=#{ei} AND employee_id = #{employeeId}")
    Integer findByEmployeeId(@Param("ei") Integer ei, @Param("employeeId") Integer employeeId);

    @Select("SELECT COUNT(employee_phone) FROM ding_mapping_employee WHERE ei = #{ei} AND employee_phone = #{phone}")
    Integer findByFxPhone(@Param("ei") Integer ei, @Param("phone") String phone);

    @Update("<script>" +
            "UPDATE ding_mapping_employee " +
            "SET ding_employee_status = #{emp.dingEmployeeStatus},employee_status=#{emp.employeeStatus}," +
            "ding_employee_name = #{emp.dingEmployeeName},ding_employee_phone = #{emp.dingEmployeePhone}," +
            "employee_name = #{emp.employeeName},employee_phone = #{emp.employeePhone}," +
            "ding_dept_id = #{emp.dingDeptId},ding_dept_name = #{emp.dingDeptName}," +
            "crm_dept_id = #{emp.crmDeptId}," +
            "update_time = NOW()," +
            "update_by = #{emp.updateBy} " +
            "WHERE ei = #{emp.ei} AND ding_employee_id = #{emp.dingEmployeeId} AND ding_dept_id= #{emp.dingDeptId}" +
            "</script>")
    Integer updateDingEmpModel(@Param("emp") DingMappingEmployeeResult emp);


    @Update("<script>" +
            "UPDATE ding_mapping_employee " +
            "SET ding_employee_status = #{emp.dingEmployeeStatus}," +
            "ding_employee_name = #{emp.dingEmployeeName},ding_employee_phone = #{emp.dingEmployeePhone}," +
            "update_time = NOW()," +
            "update_by = #{emp.updateBy} " +
            "WHERE ei = #{emp.ei} AND ding_employee_id = #{emp.dingEmployeeId}" +
            "</script>")
    Integer updateDingEmpByDingId(@Param("emp") DingMappingEmployeeResult emp);

    @Select("SELECT * FROM ding_mapping_employee WHERE ei = #{emp.ei} AND ding_employee_id = #{emp.dingEmployeeId} limit 1")
    DingMappingEmployeeResult findIsBindByDingId(@Param("emp") DingMappingEmployeeResult emp);

    @Select("SELECT * FROM ding_mapping_employee WHERE ei = #{ei} AND ding_employee_id = #{dingEmployeeId} limit 1")
    DingMappingEmployeeResult findDingEmployeeId(@Param("ei") Integer ei,@Param("dingEmployeeId") String dingEmployeeId );

    @Select("SELECT * FROM ding_mapping_employee WHERE ei = #{ei} AND ding_employee_id = #{dingEmployeeId}")
   List<DingMappingEmployeeResult> findDingEmpIdList(@Param("ei") Integer ei,@Param("dingEmployeeId") String dingEmployeeId );

    //全量数据插入
    @Insert("<script>" +
            "INSERT IGNORE INTO ding_mapping_employee" +
            "(ei,employee_id,employee_status,employee_name,employee_phone,ding_employee_status,ding_employee_name,ding_employee_id,ding_employee_phone," +
            "ding_union_id,ding_dept_id,crm_dept_id,ding_dept_name,bind_status,create_by,update_by) values " +
            "<foreach collection=\"list\" item=\"item\"  separator=\",\" >" +
            "(#{ei},#{item.employeeId},1,#{item.employeeName},#{item.employeePhone},1," +
            "#{item.dingEmployeeName},#{item.dingEmployeeId},#{item.dingEmployeePhone}," +
            "#{item.dingUnionId},#{item.dingDeptId},#{item.crmDeptId},#{item.dingDeptName},#{item.bindStatus},1000,1000)" +
            "</foreach>" +
            "</script>")
    Integer insertAllModelData(@Param("ei")Integer ei,@Param("list") List<DingMappingEmployeeResult> list);




    @Insert("<script>" +
            "INSERT IGNORE INTO ding_mapping_employee" +
            "(ei,ding_employee_status,ding_employee_name,ding_union_id,ding_employee_id,ding_employee_phone," +
            "bind_status,create_by,update_by) values " +
            "(#{emp.ei},#{emp.dingEmployeeStatus},#{emp.dingEmployeeName},#{emp.dingUnionId}," +
            "#{emp.dingEmployeeId},#{emp.dingEmployeePhone},0,#{emp.createBy},#{emp.updateBy})" +
            "</script>")
    Integer insertDingEmp(@Param("emp") DingMappingEmployeeResult emp);

    @Insert("<script>" +
            "INSERT IGNORE INTO ding_mapping_employee" +
            "(ei,employee_id,employee_status,employee_name,employee_phone,ding_employee_status,ding_employee_name,ding_employee_id,ding_employee_phone," +
            "ding_union_id,ding_dept_id,crm_dept_id,ding_dept_name,bind_status,create_by,update_by) values " +
            "(#{item.ei},#{item.employeeId},1,#{item.employeeName},#{item.employeePhone},1," +
            "#{item.dingEmployeeName},#{item.dingEmployeeId},#{item.dingEmployeePhone}," +
            "#{item.dingUnionId},#{item.dingDeptId},#{item.crmDeptId},#{item.dingDeptName},#{item.bindStatus},1000,1000)" +
            "</script>")
    Integer insertModelEmp(@Param("item") DingMappingEmployeeResult emp);




    @Select("SELECT * FROM ding_mapping_employee WHERE ei=#{ei} AND employee_id = #{employeeId}  order by bind_status desc limit 1")
    DingMappingEmployeeResult findMappingByEmployeeId(@Param("ei") Integer ei, @Param("employeeId") Integer employeeId);

    @Select("SELECT * FROM ding_mapping_employee WHERE ei = #{ei} AND ding_union_id = #{unionId} order by bind_status desc limit 1 ")
    DingMappingEmployeeResult findMappingByUnionId(@Param("ei") Integer ei, @Param("unionId") String unionId);

    @Select("SELECT * FROM ding_mapping_employee WHERE ei = #{ei} AND ding_employee_id = #{dingUserId} order by bind_status desc limit 1 ")
    DingMappingEmployeeResult findMappingByDingUserId(@Param("ei") Integer ei, @Param("dingUserId") String dingUserId);

    @Select("SELECT * FROM ding_mapping_employee WHERE ding_union_id = #{dingUnionId} " +
            "and employee_status=1 " +
            "group by ei order by bind_status desc")
    List<DingMappingEmployeeResult> queryEmpByDingUserId( @Param("dingUnionId") String dingUnionId);

    @Select("<script>" +
            "SELECT * FROM ding_mapping_employee where ei=#{ei} and employee_id in " +
            "<foreach collection=\"crmUserIds\" item=\"item\"  separator=\",\" open=\"(\" close=\")\" >" +
            "(#{item})" +
            "</foreach>" +
            "group by employee_id"+
            "</script>")
    List<DingMappingEmployeeResult> batchQueryMapping(@Param("ei") Integer ei, @Param("crmUserIds") List<Integer> crmUserIds);

    @Select("SELECT * FROM ding_mapping_employee WHERE ei = #{ei} and employee_id in <foreach collection=\"userIds\" index=\"index\" item=\"item\" open=\"(\" separator=\",\" close=\")\">\n" +
            "#{item}       \n" +
            "</foreach>  ")
    List<DingMappingEmployeeResult> findMappingByCrmUserIds(@Param("ei") Integer ei, @Param("userIds") List<Integer> crmUserIds);

    @Select("SELECT ei, employee_id FROM ding_mapping_employee WHERE bind_status=2 " +
            "ORDER BY create_time LIMIT #{offset}, #{limit}")
    List<BindFxUserResult> queryBindFxEmp(@Param("offset") Integer offset, @Param("limit") Integer limit);

    @Update("<script>"
            + "UPDATE ding_mapping_employee"
            + "<trim prefix=\"set\" suffixOverrides=\",\">"
            + " <trim prefix=\"ding_employee_phone =case\" suffix=\"end,\">" +
            " <foreach collection=\"employeeList\" item=\"i\" index=\"index\">"
            + "<if test=\"i.mobile!=null and i.mobile!=''\">"
            + "when ding_employee_id=#{i.userid} then #{i.mobile}"
            + "          </if>\n"
            + "</foreach>"
            + " </trim>"
            + " <trim prefix=\"ding_employee_name =case\" suffix=\"end,\">" +
            " <foreach collection=\"employeeList\" item=\"i\" index=\"index\"  >"
            + "<if test=\"i.name!=null and i.name!=''\">"
            + " when ding_employee_id=#{i.userid} then #{i.name}"
            + "          </if>\n"
            + "</foreach>"
            + " </trim>"
            + " </trim>" +
            "where ding_employee_id in \n" +
            "<foreach collection=\"employeeList\" index=\"index\" item=\"item\" separator=\",\" open=\"(\" close=\")\">" +
            "#{item.userid}"
            + "</foreach>"
//       " <foreach collection=\"employeeList\" item=\"i\" index=\"index\" separator=\",\" >"+
//            "              ding_employee_id=#{i.userid}\n" +
////            "              ding_employee_id in (1,2,3)\n" +
//            "          </foreach>"
            + "</script>")
    Integer batchUpdateEmployee(@Param("employeeList") List<User> employeeList);


    @Insert("<script>" +
            "INSERT IGNORE INTO ding_mapping_employee" +
            "(ei,ding_employee_status,ding_employee_name,ding_employee_id,ding_employee_phone," +
            "ding_union_id,ding_dept_id,crm_dept_id,ding_dept_name,bind_status,create_by,update_by) values " +
            "<foreach collection=\"list\" item=\"item\"  separator=\",\" >" +
            "(#{ei},1," +
            "#{item.name},#{item.userid},#{item.mobile}," +
            "#{item.unionid},#{deptId},#{crmDeptId},#{deptName},0,#{userId},#{userId})" +
            "</foreach>" +
            "</script>")
    Integer insertModeEmployee(@Param("list") List<User> list,  @Param("ei") Integer ei, @Param("userId") Integer userId,@Param("deptId") Long dingDeptId,@Param("crmDeptId") Integer crmDeptId, @Param("deptName") String dingDeptName);

//    @Update("<script>"+
//            "            update ding_mapping_employee\n" +
//            "            <trim prefix=\"set\" suffixOverrides=\",\">\n" +
//            "             <trim prefix=\"ding_employee_phone =case\" suffix=\"end,\">\n" +
//            "                 <foreach collection=\"employeeList\" item=\"i\" index=\"index\">\n" +
//            "                         <if test=\"i.mobile!=null\">\n" +
//            "                          when ding_employee_id=#{i.userid} then #{i.ding_employee_phone}\n" +
//            "                         </if>\n" +
//            "                 </foreach>\n" +
//            "              </trim>\n" +
//            "              <trim prefix=\" ding_employee_name =case\" suffix=\"end,\">\n" +
//            "                 <foreach collection=\"employeeList\" item=\"i\" index=\"index\">\n" +
//            "                         <if test=\"i.name!=null\">\n" +
//            "                          when ding_employee_id=#{i.userid} then #{i.name}\n" +
//            "                         </if>\n" +
//            "                 </foreach>\n" +
//            "              </trim>\n"+
//            "              </trim>\n"+
//            "            where\n" +
//            "            <foreach collection=\"employeeList\" separator=\"or\" item=\"i\" index=\"index\" >\n" +
//            "              ding_employee_id=#{i.ding_employee_id}\n" +
//            "          </foreach>\n"
//           +"<script>"
//    )
//    Integer batchUpdateEmployeew(@Param("employeeList") List<User> employeeList);



    @Select("select distinct employee_id from  ding_mapping_employee where  ei=#{ei} and bind_status=2")
    List<Integer> queryEmpByEi(@Param("ei") Integer ei);


    @Update("<script>" +
            "update ding_mapping_employee set employee_status=#{status} where ei=#{ei} and employee_id in" +
            "<foreach collection=\"empIds\" index=\"index\" item=\"item\" separator=\",\" open=\"(\" close=\")\">" +
            "#{item}"
            + "</foreach>"+
            "</script>")
    Integer batchUpdateEmpStatus(@Param("status") Integer status,@Param("ei") Integer ei,@Param("empIds") List<Integer> empIds);

    @Select("<script>" +
            "select * from ding_mapping_employee where ei=#{ei}" +
            "<if test=\"empIds!=null and empIds!=''\">" +
            "and employee_id in" +
            "<foreach collection=\"empIds\" index=\"index\" item=\"item\" separator=\",\" open=\"(\" close=\")\">" +
            "#{item}"
            +"</foreach>" +
            "</if> " +
            "GROUP BY employee_id"+
            "</script>")
    List<DingMappingEmployee> batchGetEmpIds(@Param("ei") Integer ei,@Param("empIds") List<Integer> empIds);


    @Select("<script>" +
            "SELECT ding_employee_id FROM ding_mapping_employee WHERE ei = #{ei} and employee_id in " +
            "<foreach collection='ids' item='item' separator=',' open='(' close=')' >"+
            "#{item}" +
            "</foreach>" +
            "</script>")
    List<String> queryUserIds(@Param("ei") int ei, @Param("ids") List<Integer> receiverIds);

    @Select("<script>" +
            "select * from ding_mapping_employee where ei=#{ei} <if test='id!=null'> and id > #{id} </if> " +
            "order by id asc limit #{limit}" +
            "</script>")
    List<DingMappingEmployee> pageById(@Param("ei") Integer ei, @Param("id") Long id, @Param("limit") Integer limit);
}
