package com.facishare.open.ding.api.vo;

import com.alibaba.fastjson.JSON;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaDeptDataEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.dingtalk.OutDeptData;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaDepartmentBindEntity;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2020/7/24 19:35
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@Builder
@AllArgsConstructor
@Slf4j
public class DeptVo implements Serializable {
    private Long id;
    private Integer ei;
    private Integer crmDeptId;
    private Integer crmParentId;
    private Long dingDeptId;
    private Long dingParentId;
    private String name;
    private Integer seq;
    //钉钉的部门负责人
    private String dingDeptOwner;
    //crm的部门负责人
    private Integer crmDeptOwner;
    private Date createTime;
    private Date updateTime;

    public DeptVo (Integer ei,String name,Integer crmDeptOwner,Integer crmParentId){
        this.ei=ei;
        this.crmParentId=crmParentId;
        this.name=name;
        this.crmDeptOwner=crmDeptOwner;
    }

    public DeptVo (Integer ei,String name,Integer crmDeptOwner,Integer crmParentId,Integer crmDeptId){
        this.ei=ei;
        this.crmParentId=crmParentId;
        this.crmDeptId=crmDeptId;
        this.name=name;
        this.crmDeptOwner=crmDeptOwner;
    }

    public static DeptVo convertToVo(OuterOaDepartmentBindEntity bindEntity, OuterOaDeptDataEntity deptDataEntity) {
        if (bindEntity == null) {
            return null;
        }

        DeptVo deptVo = new DeptVo();
        deptVo.setEi(Integer.parseInt(bindEntity.getFsEa()));
        deptVo.setCrmDeptId(Integer.parseInt(bindEntity.getFsDepId()));
        deptVo.setDingDeptId(Long.parseLong(bindEntity.getOutDepId()));

        if (deptDataEntity != null) {
            try {
                OutDeptData outDeptData = deptDataEntity.getOutDeptInfo().toJavaObject(OutDeptData.class);
                if (outDeptData != null) {
                    deptVo.setName(outDeptData.getName());
                    deptVo.setDingParentId(outDeptData.getParentId());
                    deptVo.setDingDeptOwner(outDeptData.getOwner());
                }
            } catch (Exception e) {
                log.error("convertToVo parse outDeptInfo error", e);
            }
        }

        return deptVo;
    }

    public OuterOaDepartmentBindEntity convertToBindEntity(String outEa, String appId, String dcId) {
        OuterOaDepartmentBindEntity entity = new OuterOaDepartmentBindEntity();
        entity.setChannel(ChannelEnum.dingding);
        entity.setFsEa(outEa);
        entity.setOutEa(outEa);
        entity.setAppId(appId);
        entity.setDcId(dcId);
        entity.setFsDepId(String.valueOf(this.getCrmDeptId()));
        entity.setOutDepId(String.valueOf(this.getDingDeptId()));
        entity.setBindStatus(BindStatusEnum.normal);
        entity.setCreateTime(Objects.isNull(this.getCreateTime()) ? System.currentTimeMillis() : getCreateTime().getTime());
        entity.setUpdateTime(Objects.isNull(this.getUpdateTime()) ? System.currentTimeMillis() : getUpdateTime().getTime());
        return entity;
    }

    public OuterOaDeptDataEntity convertToDeptDataEntity(String outEa, String appId) {
        OutDeptData outDeptData = new OutDeptData();
        outDeptData.setDeptId(this.getDingDeptId());
        outDeptData.setParentId(this.getDingParentId());
        outDeptData.setName(this.getName());
        outDeptData.setOwner(this.getDingDeptOwner());

        OuterOaDeptDataEntity entity = new OuterOaDeptDataEntity();
        entity.setChannel(ChannelEnum.dingding);
        entity.setOutEa(outEa);
        entity.setAppId(appId);
        entity.setOutDeptId(String.valueOf(this.getDingDeptId()));
        entity.setOutDeptInfo(JSON.parseObject(JSON.toJSONString(outDeptData)));
        entity.setCreateTime(Objects.isNull(this.getCreateTime()) ? System.currentTimeMillis() : this.getCreateTime().getTime());
        entity.setUpdateTime(Objects.isNull(this.getUpdateTime()) ? System.currentTimeMillis() : this.getUpdateTime().getTime());
        return entity;
    }
}
