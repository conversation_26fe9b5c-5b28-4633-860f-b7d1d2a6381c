package com.facishare.open.ding.cloud.service.impl;


import com.facishare.open.ding.cloud.template.outer.msg.DingTalkSendTextCardMsgHandlerTemplate;
import com.facishare.open.ding.cloud.template.outer.msg.DingTalkSendTextMsgHandlerTemplate;
import com.fxiaoke.message.extrnal.platform.api.ExternalMessageService;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextCardMessageArg;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextMessageArg;
import com.fxiaoke.message.extrnal.platform.model.result.SendTextCardMessageResult;
import com.fxiaoke.message.extrnal.platform.model.result.SendTextMessageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR> crm提醒
 * @Date 2021/5/10 19:20
 * @Version 1.0
 */
@Service("cloudExternalMessageServiceImpl")
@Slf4j
public class CloudExternalMessageServiceImpl implements ExternalMessageService {

//    @Autowired
//    private ObjectMappingService objectMappingService;
//    @Autowired
//    private DingCorpMappingService corpMappingService;
//    @Autowired
//    private AppAuthService appAuthService;
//    @Autowired
//    private DingManager dingManager;
//    @Autowired
//    private CrmManager crmManager;
    @Resource
    private DingTalkSendTextMsgHandlerTemplate dingTalkSendTextMsgHandlerTemplate;
    @Resource
    private DingTalkSendTextCardMsgHandlerTemplate dingTalkSendTextCardMsgHandlerTemplate;

    @Override
    public SendTextMessageResult sendTextMessage(SendTextMessageArg sendTextMessageArg) {
//        log.info("cloud send sendTextMessage message arg:{}", sendTextMessageArg);
//        Integer ei = sendTextMessageArg.getEi();
//        SendTextMessageResult result = new SendTextMessageResult();
//        result.setCode(200);
//        result.setMessage("发送成功");
//        Result<List<DingCorpMappingVo>> corpResult = corpMappingService.queryByEi(ei);
//        if(CollectionUtils.isEmpty(corpResult.getData())){
//            return result;
//        }
//        String dingCorpId = corpResult.getData().get(0).getDingCorpId();
//        Result<List<AppAuthResult>> appAuthResult = appAuthService.conditionAppAuth(dingCorpId, ConfigCenter.APP_CRM_ID, null);
//        if(ObjectUtils.isEmpty(appAuthResult.getData())){
//            log.info("enterprise not support send todo :{}",dingCorpId);
//            return result;
//        }
//        Long agentId = appAuthResult.getData().get(0).getAgentId();
//        List<Integer> userIds = sendTextMessageArg.getReceiverIds();
//
//        Result<List<DingMappingEmployeeResult>> dataResult = objectMappingService.batchQueryMapping(ei, userIds);
//        if (CollectionUtils.isEmpty(dataResult.getData())) {
//            log.info("crm no user bind ea:{},userIds:{}", sendTextMessageArg.getEa(), sendTextMessageArg.getReceiverIds());
//            return result;
//        }
//        List<String> dingEmpIds = dataResult.getData().stream().map(DingMappingEmployeeResult::getDingEmployeeId).collect(Collectors.toList());
//        Map<String, Object> argMap = Maps.newHashMap();
//        String empIds = Joiner.on(",").join(dingEmpIds);
//        argMap.put("userIdList", empIds);
//        String message = sendTextMessageArg.getMessageContent();
//        argMap.put("message","<font color=\"#A2A3A5\" style=\"line-height:22px;font-size:16px;\">"+message+"/<font>");
//
//        SendCardMessageArg arg = new SendCardMessageArg();
//        arg.setAgentId(agentId);
//        arg.setDingCorpId(dingCorpId);
//        arg.setDingMessageArg(argMap);
//        arg.setTemplateId(ConfigCenter.MESSAGE_TEXT_ID);
//        arg.setIsCardMessage(false);
//        arg.setSuiteId(ConfigCenter.CRM_SUITE_ID);
//        dingManager.sendCardMessage(arg);
//        //dingManager.sendCardMessage(agentId, dingCorpId, argMap, ConfigCenter.MESSAGE_TEXT_ID, false,ConfigCenter.CRM_SUITE_ID);
//        return result;
        SendTextMessageResult result = (SendTextMessageResult) dingTalkSendTextMsgHandlerTemplate.execute(sendTextMessageArg).getData();
        log.info("sendTextMessage,result={}",result);
        return result;
    }

    @Override
    public SendTextCardMessageResult sendTextCardMessage(SendTextCardMessageArg sendTextCardMessageArg) {

//        log.info("cloud sendTextCardMessage message arg:{}", sendTextCardMessageArg);
//        Integer ei = sendTextCardMessageArg.getEi();
//        SendTextCardMessageResult result = new SendTextCardMessageResult();
//        result.setCode(200);
//        result.setMessage("发送成功");
//        Result<List<DingCorpMappingVo>> corpResult = corpMappingService.queryByEi(ei);
//        if(CollectionUtils.isEmpty(corpResult.getData())){
//            return result;
//        }
//        String dingCorpId = corpResult.getData().get(0).getDingCorpId();
//        Result<List<AppAuthResult>> appAuthResult = appAuthService.conditionAppAuth(dingCorpId, ConfigCenter.APP_CRM_ID, null);
//        if(ObjectUtils.isEmpty(appAuthResult.getData())){
//            log.info("enterprise not support send todo :{}",dingCorpId);
//            return result;
//        }
//        Long agentId = appAuthResult.getData().get(0).getAgentId();
//        List<Integer> userIds = sendTextCardMessageArg.getReceiverIds();
//
//        Result<List<DingMappingEmployeeResult>> dataResult = objectMappingService.batchQueryMapping(ei, userIds);
//        if (CollectionUtils.isEmpty(dataResult.getData())) {
//            log.info("crm no user bind ea:{},userIds:{}", sendTextCardMessageArg.getEa(), sendTextCardMessageArg.getReceiverIds());
//            return result;
//        }
//        StringBuilder markdown = new StringBuilder();
//        if (CollectionUtils.isNotEmpty(sendTextCardMessageArg.getForm())) {
//            Map<String,String> argMap=Maps.newHashMap();
//            for (int i = 0; i < sendTextCardMessageArg.getForm().size(); i++) {
//                if(i==0){
//                    markdown.append("<font color=\"#181C25\" style=\"line-height:22px;font-size:16px;\">"+sendTextCardMessageArg.getForm().get(i).getKey()+":"+sendTextCardMessageArg.getForm().get(i).getValue()+"</font>\n"+  "<br>\n" );
//                }else{
//                    markdown.append("<font color=\"#A2A3A5\" style=\"line-height:20px;font-size:14px;\">"+sendTextCardMessageArg.getForm().get(i).getKey()+"：</font><font color=\"#181C25\" style=\"line-height:20px;font-size:14px;\">"+sendTextCardMessageArg.getForm().get(i).getValue()+"</font>"+"<br>\n");
//                }
//            }
//        } else {
//            markdown.append("<font color=\"#181C25\" style=\"line-height:22px;font-size:16px;\">"+sendTextCardMessageArg.getTitle()+"\n"+sendTextCardMessageArg.getMessageContent()+"</font>\n"+  "        <br>\n" );
//        }
//
//        List<String> dingEmpIds = dataResult.getData().stream().map(DingMappingEmployeeResult::getDingEmployeeId).collect(Collectors.toList());
//        Map<String, Object> argMap = Maps.newHashMap();
//        String empIds = Joiner.on(",").join(dingEmpIds);
//        argMap.put("userIdList", empIds);
//        Integer taskId = sendTextCardMessageArg.getGenerateUrlType();
//        argMap.put("taskId", ObjectUtils.isEmpty(taskId)?"100":taskId);
//        argMap.put("ei", sendTextCardMessageArg.getEi());
//        argMap.put("markdown", markdown);
//        Boolean messageIdType = Boolean.FALSE;
//        if(sendTextCardMessageArg.getGenerateUrlType() ==  GenerateUrlTypeEnum.BI_MESSAGE_URL.getType()) {
//            String commonWebviewUrl = ConfigCenter.AVA_FS_COMMON_WEBVIEW_URL.replace("{url}", URLEncoder.encode(URLEncoder.encode(ConfigCenter.DING_FUNCTION_URL + sendTextCardMessageArg.getUrl())));
//            //短链形式
//            Result<String> shortUrlResult = crmManager.createShortUrl(ei, commonWebviewUrl);
//            if(!shortUrlResult.isSuccess()) {
//                log.info("create to do short url error, arg :{}", shortUrlResult);
//                return result;
//            }
//            byte[] commonWebviewUrlBytes = shortUrlResult.getData().getBytes();
//            String param = new String(Base64.encodeBase64(commonWebviewUrlBytes));
//            argMap.put("url", URLEncoder.encode(param));
//            messageIdType = Boolean.TRUE;
//        } else if(sendTextCardMessageArg.getGenerateUrlType() ==  GenerateUrlTypeEnum.FILE_MESSAGE_URL.getType()) {
//            Map<String, String> fileInfo = new HashMap<>();
//            fileInfo.put("path", sendTextCardMessageArg.getExtraDataMap().get("filePath"));
//            fileInfo.put("filename", URLEncoder.encode(URLEncoder.encode(sendTextCardMessageArg.getExtraDataMap().get("fileName"))));
//
//            // 使用 ObjectMapper 将对象转换为 JSON
//            ObjectMapper objectMapper = new ObjectMapper();
//            String jsonString;
//            try {
//                jsonString = URLEncoder.encode(objectMapper.writeValueAsString(new Object[]{fileInfo}));
//            } catch (JsonProcessingException e) {
//                throw new RuntimeException(e);
//            }
//            String commonWebviewUrl = ConfigCenter.DING_FUNCTION_URL + ConfigCenter.FILE_VIEW_URL.replace("{nfDatas}", jsonString);
//
//            //短链形式
//            Result<String> shortUrlResult = crmManager.createShortUrl(ei, commonWebviewUrl);
//            if(!shortUrlResult.isSuccess()) {
//                log.info("create to do short url error, arg :{}", shortUrlResult);
//                return result;
//            }
//            byte[] commonWebviewUrlBytes = shortUrlResult.getData().getBytes();
//            String param = new String(Base64.encodeBase64(commonWebviewUrlBytes));
//            argMap.put("url", URLEncoder.encode(param));
//            messageIdType = Boolean.TRUE;
//        } else if(sendTextCardMessageArg.getGenerateUrlType() ==  GenerateUrlTypeEnum.ATME_URL.getType()) {
//            //at我的事件
//            String commonWebviewUrl = ConfigCenter.DING_ATME_URL.replace("{feedId}", sendTextCardMessageArg.getExtraDataMap().get("feedId"));
//            byte[] commonWebviewUrlBytes = commonWebviewUrl.getBytes();
//            String param = new String(Base64.encodeBase64(commonWebviewUrlBytes));
//            argMap.put("url", URLEncoder.encode(param));
//            log.info("create to do atme arg :{}", argMap);
//            dingManager.sendCardMessage(agentId, dingCorpId, argMap, ConfigCenter.COMMENT_CAR_MESSAGE_ID, true,ConfigCenter.CRM_SUITE_ID);
//            return result;
//        } else {
//            String objectApiName = sendTextCardMessageArg.getExtraDataMap().get("objectApiName");
//            String objectId = sendTextCardMessageArg.getExtraDataMap().get("objectId");
//            String instanceId = sendTextCardMessageArg.getExtraDataMap().get("workflowInstanceId");
//            argMap.put("apiname", objectApiName);
//            argMap.put("objectId", objectId);
//            //crm没传某些参数，但是不能为空
//            argMap.put("instanceId", ObjectUtils.isEmpty(instanceId)?"100":instanceId);
//            argMap.put("bizType", "0");//消息卡片没有bizType，默认为0
//        }
//        log.info("create to do arg :{}", argMap);
//
//        SendCardMessageArg arg = new SendCardMessageArg();
//        arg.setAgentId(agentId);
//        arg.setDingCorpId(dingCorpId);
//        arg.setDingMessageArg(argMap);
//        arg.setTemplateId(messageIdType == Boolean.TRUE ? ConfigCenter.COMMENT_MESSAGE_ID : ConfigCenter.MESSAGE_CARD_ID);
//        arg.setIsCardMessage(false);
//        arg.setSuiteId(ConfigCenter.CRM_SUITE_ID);
//        dingManager.sendCardMessage(arg);
//        //dingManager.sendCardMessage(agentId, dingCorpId, argMap, messageIdType == Boolean.TRUE ? ConfigCenter.COMMENT_MESSAGE_ID : ConfigCenter.MESSAGE_CARD_ID, true,ConfigCenter.CRM_SUITE_ID);
//        return result;

        SendTextCardMessageResult result = (SendTextCardMessageResult) dingTalkSendTextCardMsgHandlerTemplate.execute(sendTextCardMessageArg).getData();
        log.info("sendTextCardMessage,result={}",result);
        return result;
    }

}
