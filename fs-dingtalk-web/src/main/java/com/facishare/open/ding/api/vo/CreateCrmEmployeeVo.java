package com.facishare.open.ding.api.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/7/27 15:05
 * @Version 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CreateCrmEmployeeVo implements Serializable {
    //主键ID
    private Integer id;
    private String name;

    private String gender = "M";

    private String mobile;

    private String dingEmployeeId;

    private Integer updateBy;

    /**
     * 纷享ei
     **/
    private Integer ei;

    /**
     * 主属钉钉部门
     */
    private Long dingDeptId;

    /**
     * crm的主属部门
     */
    private String crmMainDeptId;

    /**
     * 附属部门
     */

    private List<String> crmViceDepts;

    /**
     * 对应纷享的员工ID
     */

    private Integer crmEmpId;
    //汇报对象
    private Integer leader;

    private String unionid;
}
