package com.facishare.open.ding.api.enums;

import com.alibaba.fastjson.JSON;
import com.jayway.jsonpath.JsonPath;

import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/1/17 10:58 钉钉官方CRM的对象名
 * @Version 1.0
 */
public enum DingObjTypeEnum {

    //
    CRM_ACCOUNT_TYPE("AccountObj","crm_customer","customer_id", "/objectDescribe/customer_describe.json"),
    CRM_CONTACT_TYPE("ContactObj","crm_contact","contact_id", "/objectDescribe/contact_describe.json"),
    CRM_PERSONAL_CONTACT_TYPE("AccountObj","crm_customer_personal","customer_id", "/objectDescribe/personal_customer_describe.json"),
    CRM_LEADS_TYPE("LeadsObj","crm_customer_personal","customer_id", "/objectDescribe/personal_customer_describe.json"),

    ;

    private String crmObjApiName;
    private  String dingObjApiName;
    private String idFieldName;
    private String describePath;

    DingObjTypeEnum(String crmObjApiName,String dingObjApiName,String field,String describePath){
        this.crmObjApiName=crmObjApiName;
        this.dingObjApiName=dingObjApiName;
        this.idFieldName=field;
        this.describePath=describePath;
    }

    public static DingObjTypeEnum getEnumByDingApiName(String dingObjApiName){
        for (DingObjTypeEnum e : DingObjTypeEnum.values()){
            if(e.dingObjApiName.equals(dingObjApiName)){
                return e;
            }
        }
        return null;
    }

    public static DingObjTypeEnum getEnumByCrmApiName(String crmObjApiName){
        for (DingObjTypeEnum e : DingObjTypeEnum.values()){
            if(e.crmObjApiName.equals(crmObjApiName)){
                return e;
            }
        }
        return null;
    }

    public String getCrmObjApiName(){
        return this.crmObjApiName;
    }
    public String getDingObjApiName(){
        return this.dingObjApiName;
    }
    public String getIdFieldName(){
        return this.idFieldName;
    }


    public List<Map<String,Object>> getObjFieldType(){
        String data="";
        try (InputStream inputStream = DingObjTypeEnum.class.getResourceAsStream(this.describePath)) {
            data= JSON.parseObject(inputStream, String.class);
        } catch (Exception e) {
            throw new RuntimeException("Fail to getObjectDescribe", e);
        }
        List<Map<String,Object>> dataMap =  JsonPath.read(data, "$.fields.*");

        return dataMap;
    }

}
