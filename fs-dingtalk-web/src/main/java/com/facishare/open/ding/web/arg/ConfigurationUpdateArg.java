package com.facishare.open.ding.web.arg;

import com.facishare.open.ding.common.model.BaseArg;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * Created by system on 2018/4/10.
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ConfigurationUpdateArg extends BaseArg {

    /** 配置id **/
    private Integer id;

    /** 数据同步类型：1、手动同步 2、定时同步 **/
    private Integer syncType;

    /** 定时同步频率类型 **/
    private Integer rateType;

    /** 定时同步开始时间，默认值 00:00 **/
    private String startTime;

    /** 定时同步结束时间, 默认值 23:59 **/
    private String endTime;

}
