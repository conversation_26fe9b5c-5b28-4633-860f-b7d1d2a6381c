package com.facishare.open.ding.api.enums;

/**
 * <p>类的详细说明</p>
 * @dateTime 2018/7/23 14:43
 * <AUTHOR> yin<PERSON>@fxiaoke.com
 * @version 1.0 
 */
public enum OperationTypeEnum {

    /** 无操作 **/
    UNDO(0),

    /** 新增 **/
    ADD(1),

    /** 修改 **/
    UPDATE(2),

    /** 作废 **/
    INVALID(3),

    /** 上架 **/
    ON_SHELF(4),

    /** 下架 **/
    OFF_SHELF(5),

    /** 删除 **/
    DELETE(6),

    /** 新增产品单位 */
    ADD_PRODUCT_UNIT(7),

    /** 停用 **/
    STOP(8),
    ;

    private int type;

    OperationTypeEnum(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }

    public static boolean isInvalid(Integer type) {
        if (type != null) {
            for (OperationTypeEnum syncTypeEnum : OperationTypeEnum.values()) {
                if (syncTypeEnum.getType() == type) {
                    return false;
                }
            }
        }
        return true;
    }

}
