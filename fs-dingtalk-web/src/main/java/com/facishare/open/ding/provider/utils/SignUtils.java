package com.facishare.open.ding.provider.utils;

import org.apache.commons.codec.digest.DigestUtils;

import java.util.Random;

/**
 * Created by system on 2018/4/28.
 */
public class SignUtils {

    private static String[] states =
        {"0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "0", "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k",
            "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z", "A", "B", "C", "D", "E", "F", "G",
            "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"};

    private static int bound = 63;

    /**
     * 生成随机文本，由16位大小写字母和数字组成
     * @return
     */
    public static String generateState() {
        Random random = new Random();

        StringBuilder sb = new StringBuilder();
        for (int index = 0; index < 16; index++) {
            sb.append(states[random.nextInt(bound)]);
        }

        return sb.toString();
    }

    /**
     * 生成32位签名串
     * @param isLowerCase
     * @param args
     * @return
     */
    public static String generateSign(boolean isLowerCase, String... args) {
        StringBuilder sb = new StringBuilder();
        for (String arg : args) {
            sb.append(arg);
        }

        String md5Text = DigestUtils.md5Hex(sb.toString());
        if (isLowerCase) {
            return md5Text.toLowerCase();
        } else {
            return md5Text.toUpperCase();
        }
    }

}
