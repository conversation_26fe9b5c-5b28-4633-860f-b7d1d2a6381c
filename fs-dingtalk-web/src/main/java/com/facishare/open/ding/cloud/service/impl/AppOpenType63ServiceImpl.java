package com.facishare.open.ding.cloud.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.ding.api.enums.BindType;
import com.facishare.open.ding.api.enums.BizServiceEnum;
import com.facishare.open.ding.api.model.AppParams;
import com.facishare.open.ding.api.model.AuthEnterPriseModel;
import com.facishare.open.ding.api.model.OrderModel;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.result.OrderInfoResult;
import com.facishare.open.ding.api.service.DingCorpMappingService;
import com.facishare.open.ding.api.service.ObjectMappingService;
import com.facishare.open.ding.api.service.cloud.CloudEmpService;
import com.facishare.open.ding.api.service.cloud.CloudOrderService;
import com.facishare.open.ding.api.vo.DingCorpMappingVo;
import com.facishare.open.ding.cloud.arg.CreateCrmOrderArg;
import com.facishare.open.ding.cloud.arg.CreateCustomerArg;
import com.facishare.open.ding.cloud.constants.ConfigCenter;
import com.facishare.open.ding.cloud.dao.OpenSyncBizDataDao;
import com.facishare.open.ding.cloud.entity.HighBizDataDo;
import com.facishare.open.ding.cloud.manager.CrmManager;
import com.facishare.open.ding.cloud.manager.DingManager;
import com.facishare.open.ding.provider.redis.RedisDataSource;
import com.facishare.open.ding.cloud.service.api.DingEventService;
import com.facishare.open.ding.cloud.utils.PinyinUtils;
import com.facishare.open.ding.cloud.utils.RedisLockUtils;
import com.facishare.open.ding.cloud.utils.SpringUtils;
import com.facishare.open.ding.common.model.EmployeeDingVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.outer.oa.connector.common.api.constants.GlobalValue;
import com.facishare.restful.common.StopWatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/5/7 10:41 应用内部授权以及个人开通 biztype=63
 * @Version 1.0
 */
@Service("appOpenType63ServiceImpl")
@Slf4j
// IgnoreI18nFile
public class AppOpenType63ServiceImpl implements DingEventService {

    @Autowired
    private CrmManager crmManager;
    @Autowired
    private DingManager dingManager;
    @Autowired
    private DingCorpMappingService dingCorpMappingService;
    @Autowired
    private ObjectMappingService objectMappingService;
    @Autowired
    private CloudEmpService cloudEmpService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private CloudOrderService cloudOrderService;
    @Autowired
    private RedisDataSource redisDataSource;
    @Autowired
    private OpenSyncBizDataDao openSyncBizDataDao;
    public Map<Integer, DingEventService> serviceMap = Maps.newHashMap();
    @Autowired
    private SpringUtils springUtils;
    @PostConstruct
    public void init() {
        for (BizServiceEnum bizServiceEnum : BizServiceEnum.values()) {
            DingEventService service = (DingEventService) springUtils.getApplicationContext().getBean(bizServiceEnum.getBeanName());
            serviceMap.put(bizServiceEnum.getBizType(), service);
        }
    }


    private static final String ENTERPRISE_PREFIX = "dt";

    public static final String NEW_TYPE = "BUY";
    public static final String RENEW = "RENEW";
    public static final String UPGRADE = "UPGRADE";
    public static final String RENEW_UPGRADE = "RENEW_UPGRADE";
    public static final String RENEW_DEGRADE = "RENEW_DEGRADE";
    public static final String DT_MOCK_UPGRADE = "DT_MOCK_UPGRADE";//虚拟钉钉规格，对应纷享升级产品
    public static String ORDER_FORMAT="DING_ORDER_%S";
    @Override
    public void executeEvent(HighBizDataDo eventData) {

        StopWatch stopWatch = StopWatch.create("free execute");
        OrderModel orderModel = JSONObject.parseObject(eventData.getBizData(), new TypeReference<OrderModel>() {
        });
        Integer count = cloudOrderService.queryCount(orderModel.getOrderId(), null);
        if (count > 0) return;

        String appId = ConfigCenter.APP_PARAMS_MAP.get(orderModel.getSuiteId()).getAppId();
        //添加分布式锁
        if(RedisLockUtils.tryGetDistributedLock(redisDataSource.getRedisClient(),String.format(ORDER_FORMAT,orderModel.getCorpId()), orderModel.getOrderId().toString(), 15)){
            switch (orderModel.getOrderType()) {
                case NEW_TYPE:
                    newOrder(orderModel, appId);
                    break;
                case RENEW:
                    renewOrder(orderModel);
                    break;
                case UPGRADE:
                    upgradeOrder(orderModel);
                    break;
                default:
                    renewOrder(orderModel);
            }
            //保存订单
            Integer orderCount = cloudOrderService.addOrder(orderModel);
            log.info("order event17 orderModel:{},count:{}",orderModel,orderCount);
            //避免前面高级事件查询的时候，漏掉bizType=4的事件，这边添加处理，处理完订单后，主动触发bizType=4
            AppParams appParams = ConfigCenter.APP_PARAMS_MAP.get(orderModel.getSuiteId());
            HighBizDataDo bizDataDo = openSyncBizDataDao.selectBizTypeByCorpId(4, eventData.getCorpId(),appParams.getSuiteId());
            serviceMap.get(bizDataDo.getBizType()).executeEvent(bizDataDo);
            log.info("orderEvent fix bizType4 data:{}",bizDataDo);
            stopWatch.lap("createOrder");
            stopWatch.log();
        }
        return;
    }

    //新购订单
    public void newOrder(OrderModel orderModel, String appId) {
        StopWatch stopWatch = StopWatch.create("newOrder execute");
        Result<List<DingCorpMappingVo>> corpResult = dingCorpMappingService.queryCorpMappingByCorpId(orderModel.getCorpId(), appId);
        if ("TRYOUT".equals(orderModel.getOrderChargeType())) {
            //试用订单提供的人数是设置成企业人数
//            Integer empCount = dingManager.getEmpCount(orderModel.getCorpId(),orderModel.getSuiteId());
//            orderModel.setSubQuantity(empCount);
            //默认为 10000
            orderModel.setSubQuantity(ConfigCenter.createCrmAccount);
        }
        if (ObjectUtils.isEmpty(corpResult.getData())) {
            Map<String, Object> objectMap = initEnterPrise(orderModel, appId);
            orderModel.setEa(objectMap.get("ea").toString());
        } else {
            String enterPriseAccount = corpResult.getData().get(0).getEa();
            createCrmOrder(orderModel, enterPriseAccount);
            orderModel.setEa(enterPriseAccount);
        }
        stopWatch.lap("new order");
        stopWatch.log();
    }

    private Map<String, Object> initEnterPrise(OrderModel orderModel, String appId) {
        StopWatch stopWatch = StopWatch.create("initEnterPrise");
        log.info("init enterPrise model:{}",orderModel);
        String userId = dingManager.getUserIdByUnionId(orderModel.getCorpId(), orderModel.getUnionId(),orderModel.getSuiteId());
        List<EmployeeDingVo> dingEmp = dingManager.getDingEmp(orderModel.getCorpId(), Lists.newArrayList(userId),orderModel.getSuiteId());
        //获取授权企业信息
        AuthEnterPriseModel.BizData enterPriseData = dingManager.getEnterPriseData(orderModel.getCorpId(),orderModel.getSuiteId());
        stopWatch.lap("getEnterPriseData");
        //去掉空格
        String enterPriseName = PinyinUtils.saveSpecialSymbol(enterPriseData.getAuthCorpInfo().getCorpName()).replace(" ","");
        //创建客户
        String enterPriseAccount = createCustomEnterpriseAccount(enterPriseName);
        log.info("init enterPrise account:{}",enterPriseAccount);
        CreateCustomerArg createCustomerArg = CreateCustomerArg.builder().enterpriseAccount(enterPriseAccount).
                enterpriseName(enterPriseName).managerName(dingEmp.get(0).getName()).source(1).outEid(orderModel.getCorpId())
                .build();
        Result<String> customer = crmManager.createCustomer(createCustomerArg);
        //TODO 如果客户重名 加后缀
        stopWatch.lap("customer");
        log.info("enterprise auth create customer result:{}", customer);
        //创建订单
        Result<String> createOrder = createCrmOrder(orderModel, enterPriseAccount);
        log.info("order event result:{}", createOrder);
        stopWatch.lap("createOrder");
        //等待ei
        Integer ei = null;
        do {
            try {
                ei = eieaConverter.enterpriseAccountToId(enterPriseAccount);
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                log.info("waiting ei create:{}", orderModel.getCorpId());
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

        } while (ObjectUtils.isEmpty(ei));
        stopWatch.lap("getEi");
        //创建系统管理员（避免等待同步通讯录太久）
        createManager(dingEmp.get(0), ei, appId);
        Result<Integer> countMapping = insertCorpMapping(enterPriseAccount, ei, orderModel, enterPriseName);
        log.info("init corp mapping count:{},corpId:{},ei:{}", countMapping, orderModel.getCorpId(), ei);
        Map<String, Object> nameMap = Maps.newHashMap();
        nameMap.put("ea", enterPriseAccount);
        nameMap.put("ei", ei);
        stopWatch.log();
        return nameMap;
    }


    //续费
    public void renewOrder(OrderModel orderModel) {
        Result<List<DingCorpMappingVo>> corpResult = dingCorpMappingService.queryCorpMappingByCorpId(orderModel.getCorpId(), null);
        String ea = corpResult.getData().get(0).getEa();
        createCrmOrder(orderModel, ea);
        orderModel.setEa(ea);
    }

    //升级
    public void upgradeOrder(OrderModel orderModel) {
//        //查询上一次订单信息
        OrderInfoResult lastOrder = cloudOrderService.queryLastOrder(orderModel.getCorpId(),ConfigCenter.CRM_SUITE_ID);

        if (!lastOrder.getSubQuantity().equals(orderModel.getSubQuantity())) {
            //升级人数
            Integer addQuantity = orderModel.getSubQuantity() - lastOrder.getSubQuantity();
            orderModel.setSubQuantity(addQuantity);
        } else {
            //升版本
            orderModel.setItemCode(DT_MOCK_UPGRADE);
        }
        orderModel.setEa(lastOrder.getEa());
        Result<String> upgradeResult = createCrmOrder(orderModel, lastOrder.getEa());
        log.info("upgrade order result:{},ea:{}",upgradeResult,lastOrder.getEa());
    }


    private Result<Integer> insertCorpMapping(String ea, Integer ei, OrderModel orderModel, String enterPriseName) {
        String dingCorpId = orderModel.getCorpId();
        log.info("insert mapping orderModel:{},marketing:{}", orderModel, ConfigCenter.MARKETING_APP_MAPPING);
        Long appCode = ConfigCenter.MARKETING_APP_MAPPING.get(orderModel.getGoodsCode());
        DingCorpMappingVo corpMappingVo = DingCorpMappingVo.builder().ea(ea).ei(ei).dingCorpId(dingCorpId).enterpriseName(enterPriseName)
                .appCode(appCode).bindType(BindType.ORDER_BIND.getType()).isInit(0).extend(GlobalValue.enterprise_extend).build();
        Result<Integer> corpCount = dingCorpMappingService.insertCorpMapping(corpMappingVo);
        return corpCount;
    }


    //企业创建，会默认创建客户的负责人，1000
    private Integer createManager(EmployeeDingVo employeeDingVo, Integer ei, String appId) {
        DingMappingEmployeeResult employeeResult = new DingMappingEmployeeResult();
        employeeResult.setBindStatus(2);
        employeeResult.setEi(ei);
        employeeResult.setEmployeeStatus(1);
        employeeResult.setDingEmployeeStatus(1);
        employeeResult.setEmployeeName(employeeDingVo.getName());
        employeeResult.setDingDeptId(employeeDingVo.getMainDept());
        employeeResult.setDingUnionId(employeeDingVo.getUnionid());
        employeeResult.setDingEmployeeId(employeeDingVo.getUserid());
        employeeResult.setDingEmployeeName(employeeDingVo.getName());
        employeeResult.setEmployeeId(1000);
        Result<Integer> createCount = cloudEmpService.insertEmpData(Lists.newArrayList(employeeResult), appId);
        return createCount.getData();
    }

    /**
     * 创建纷享的订单
     */
    public Result<String> createCrmOrder(OrderModel orderModel, String enterpriseAccount) {
        StopWatch stopWatch=StopWatch.create("createCrmOrder");

        String crmProductId = ObjectUtils.isEmpty(ConfigCenter.GOODS_CODE.get(orderModel.getItemCode())) ? ConfigCenter.TEMP_CODE : ConfigCenter.GOODS_CODE.get(orderModel.getItemCode());
        CreateCrmOrderArg createCrmOrderArg = new CreateCrmOrderArg();
//        //todo 钉钉的订单与纷享的订单匹配
        CreateCrmOrderArg.CrmOrderDetailInfo crmOrderDetailInfo = new CreateCrmOrderArg.CrmOrderDetailInfo();
        crmOrderDetailInfo.setOrderId(orderModel.getOrderId().toString());
        crmOrderDetailInfo.setEnterpriseAccount(enterpriseAccount);
        Integer orderType = orderModel.getItemCode().equals(ConfigCenter.TRY_GOOD) ? ConfigCenter.ORDER_TRY_TYPE : ConfigCenter.ORDER_BUY_TYPE;
        crmOrderDetailInfo.setOrderTpye(orderType);
        crmOrderDetailInfo.setOrderTime(orderModel.getPaidTime());
        createCrmOrderArg.setCrmOrderDetailInfo(crmOrderDetailInfo);
        CreateCrmOrderArg.CrmOrderProductInfo crmOrderProductInfo = new CreateCrmOrderArg.CrmOrderProductInfo();
        crmOrderProductInfo.setProductId(crmProductId);
        crmOrderProductInfo.setQuantity(orderModel.getSubQuantity());
        crmOrderProductInfo.setAllResourceCount(orderModel.getSubQuantity());
        //钉钉推送的金额是分
        Double payCount = Double.parseDouble(orderModel.getPayFee().toString()) / 100;
        crmOrderProductInfo.setOrderAmount(payCount.toString());
        Date fromDate = new Date(orderModel.getServiceStartTime());
        Date endDate = new Date(orderModel.getServiceStopTime());
        crmOrderProductInfo.setBeginTime(fromDate.getTime());
        crmOrderProductInfo.setEndTime(endDate.getTime());
        createCrmOrderArg.setCrmOrderProductInfo(crmOrderProductInfo);
        Result<String> orderResult = crmManager.createCrmOrder(createCrmOrderArg);
        stopWatch.lap("createOrder");
        stopWatch.log();
        log.info("create crm order arg :{} result:{},orderModel:{}", createCrmOrderArg, orderResult, orderModel);
        return orderResult;
    }


//    //下单人、企业管理人员设置系统管理人员
//    private void createSystemRole(EmployeeDingVo employeeDingVo, Integer ei, Long appId, String dingCorpId) {
//        log.info("batch createSystemRole :{}", employeeDingVo);
//        cloudEmpService.cloudCreateEmp(employeeDingVo, ei, appId, dingCorpId);
//        Result<DingMappingEmployeeResult> managerResult = objectMappingService.queryEmpByDingUserId(ei, employeeDingVo.getUserid());
//        Integer empId = managerResult.getData().getEmployeeId();
//        //设置系统管理员
//        Result<String> roleResult = crmManager.addManagerRole(ei, empId);
//        log.info("create managerResult:{}", roleResult);
//    }

    private void updateUser(EmployeeDingVo employeeDingVo, Integer ei, Long appId, String dingCorpId) {
        Result<Integer> result = cloudEmpService.cloudUpdateEmp(employeeDingVo, ei, appId, dingCorpId);
        log.info("updateUser managerResult:{}", result);
    }


    /**
     * 生成自定义ea
     */
    public String createCustomEnterpriseAccount(String enterpriseName) {
        StringBuilder enterPriseName = new StringBuilder();
        Long incrId = RedisLockUtils.getIncrId(redisDataSource.getRedisClient());
        if(incrId<1000){
            //避免redis内存不足，淘汰一些value
            incrId = Math.round((Math.random() + 1) * 1000);
        }
        String suffix = String.valueOf(incrId);
        String ea = ENTERPRISE_PREFIX + PinyinUtils.converterToFirstSpell(enterpriseName);
        enterPriseName.append(StringUtils.substring(ea, 0, 6)).append(suffix);
        log.info("createCustomEnterpriseAccount result :{}",enterPriseName.toString());
        return enterPriseName.toString();
    }


    public static void main(String[] args) {
        AppOpenType63ServiceImpl orderEvent17Service=new AppOpenType63ServiceImpl();
        String customEnterpriseAccount = orderEvent17Service.createCustomEnterpriseAccount("纷享销客CRM钉钉版–样板间");
        log.info(customEnterpriseAccount);
    }


}
