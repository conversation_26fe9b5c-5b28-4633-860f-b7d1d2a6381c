package com.facishare.open.ding.api.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/11/5 14:26
 * @Version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DingOrderResult implements Serializable {
    /**
     * 订单ID
     */
    private String orderId;
    /**
     * 企业CorpID
     */
    private String corpId;
    /**
     * 企业EA
     */
    private String ea;
    /**
     * 购买数量
     */
    private Integer quantity;
    /**
     * 产品编码
     */
    private String goodsCode;
    /**
     * 产品规格编码
     */
    private String itemCode;
    /**
     * 服务开始时间
     */
    private Date serviceStartTime;
    /**
     * 服务结束时间
     */
    private Date serviceStopTime;
    /**
     * 支持最大人数
     */
    private Integer maxPeople;
    /**
     * 支持最小人数
     */
    private Integer minPeople;

    /**
     * 支付价格 以分为单位
     */
    private Long payFee;

}
