package com.facishare.open.ding.cloud.model;

import io.protostuff.Tag;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2022/3/7 19:45
 * @Version 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DingChatRoomLogDTO {

    @Tag(1)
    private String app;
    @Tag(2)
    private String serverIp;
    @Tag(3)
    private String traceId;
    @Tag(4)
    private String ei;
    @Tag(5)
    private String ea;
    @Tag(6)
    private String corpId;
    @Tag(7)
    private String roomId;
    @Tag(8)
    private String name;
    @Tag(9)
    private String action;
    @Tag(10)
    private long createTime;

}