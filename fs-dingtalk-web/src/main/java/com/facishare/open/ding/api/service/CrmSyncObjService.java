package com.facishare.open.ding.api.service;

import com.facishare.open.ding.api.CrmSyncObjVo;
import com.facishare.open.ding.api.arg.NeedSyncDataArg;
import com.facishare.open.ding.api.model.NeedSyncDataModel;
import com.facishare.open.ding.api.result.CrmSyncObjResult;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/1/18 16:17
 * @Version 1.0
 */
public interface CrmSyncObjService {

    CrmSyncObjResult queryNeedSyncData(NeedSyncDataModel needSyncDataModel);

    Integer initSetting(String dingCorpId,Integer ei);

    Integer updateStatus(NeedSyncDataArg needSyncDataArg);

    Integer queryIsSyncData(String dingCorpId);

    Integer updateLastSyncTIme(NeedSyncDataArg needSyncDataArg);
}
