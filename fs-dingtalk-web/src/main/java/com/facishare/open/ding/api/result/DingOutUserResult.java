package com.facishare.open.ding.api.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/10/20 17:27
 * @Version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DingOutUserResult implements Serializable {
    /** 纷享EI **/
    private Integer ei;

    /** 纷享职员ID**/
    private Integer fsEmployeeId;

    /** 职员名称 **/
    private String employeeName;

    /** 钉钉职员ID **/
    private String dingEmployeeId;

    /** 钉钉corpId **/
    private String dingCorpId;
}
