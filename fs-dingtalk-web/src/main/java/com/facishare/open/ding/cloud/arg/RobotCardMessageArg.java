package com.facishare.open.ding.cloud.arg;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/1/14 17:03
 * @Version 1.0
 */
@Data
public class RobotCardMessageArg implements Serializable {
    private String cardTemplateId;//互动卡片消息模板id
    private String openConversationId;//群ID
    private List<String> receiverUserIdList;//接收人列表
    private String outTrackId;//唯一标识卡片外部编码
    private String robotCode;//机器人编码
    private Integer conversationType;//发起会话类型 0 单聊 1 群聊
    private String callBackRouteKey;//回调key
    private CardData cardData;
    private String chatBotId;//robotCode或chatBotId二选一必填。
    private Integer userIdType;//1 默认 useriD模式 2 unionid模式
    private Map<String,String> atOpenIds;//如果key、value都为**@ALL**则判断@所有人 {123456:"钉三多"}

    @Data
    public static class CardData implements Serializable {
        private Map<String, String> cardParamMap;
        private Map<String, String> cardMediaIdParamMap;

    }

    @Data
    public class privateData implements Serializable {
        private Map<String, String> cardParamMap;
        private Map<String, String> cardMediaIdParamMap;

    }



}

