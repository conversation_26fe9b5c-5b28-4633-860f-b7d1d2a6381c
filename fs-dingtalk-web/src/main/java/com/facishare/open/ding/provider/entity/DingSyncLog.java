package com.facishare.open.ding.provider.entity;

import java.util.Date;
import javax.persistence.Id;
import lombok.Data;

/**
 * <p>同步日志</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2018/7/12 14:59
 */
@Data
public class DingSyncLog {
    @Id
    private Long id;

    /** 纷享EI**/
    private Integer ei;

    /** 同步任务ID **/
    private Long taskId;

    /** 同步方向(1from纷享2to纷享) SyncDirectionEnum **/
    private Integer syncDirection;

    /** 同步类型(1自动2手动) SyncTypeEnum **/
    private Integer syncType;

    /** 对象名称 **/
    private String apiName;

    /** 数据ID **/
    private String dataId;

    /** 操作状态 SyncLogStatusEnum */
    private Integer status;

    /** 操作类型(新增/修改/作废/上架/下架) OperationTypeEnum **/
    private Integer operationType;

    /** 操作状态(0同步失败1同步成功2部分成功) OperationStatusEnum **/
    private Integer operationStatus;

    /** 操作详情 **/
    private String operationDetails;

    /** 同步总数 **/
    private Integer totalNum;

    /** 同步成功数 **/
    private Integer successNum;

    /** 同步失败数 **/
    private Integer failNum;

    /** 消息内容 **/
    private String content;

    /** 请求参数 **/
    private String request;

    /** 请求响应 **/
    private String response;

    /** 创建时间 **/
    private Date createTime;

    /** 更新时间 **/
    private Date updateTime;

    /** 创建人 **/
    private Integer createBy;

    /** 修改人 **/
    private Integer updateBy;
}
