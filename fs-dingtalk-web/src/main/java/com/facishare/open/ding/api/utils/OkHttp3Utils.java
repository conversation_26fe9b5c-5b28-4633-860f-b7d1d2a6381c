package com.facishare.open.ding.api.utils;

import com.facishare.open.ding.api.result.HttpResponseMessage;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * OkHttp3工具类，直接基于okhttp3，不会上报蜂眼
 * Created by system on 2018/6/19.
 */
@Slf4j
public class OkHttp3Utils {

    /** 链接超时时间 **/
    private static int connectTimeout = 30;

    /** 读超时时间 **/
    private static int readTimeout = 30;

    /** 写超时时间 **/
    private static int writeTimeout = 30;

    /**
     * Asynchronous http request
     * Dispatcher 配置
     */
    private static int maxRequests = 64;
    private static int maxRequestsPerHost = 5;

    /**
     * Synchronous http request
     * ConnectionPool 配置
     */
    private static int maxIdleConnections = 5;
    // keepAliveDuration要小于被请求服务端的长连接时间,
    // 否则会报eof异常(被请求服务端可能配置会在一定时间内关闭与客户端的空闲连接)
    private static int keepAliveDuration = 40;

    private static final OkHttpClient client;

    static {
        Dispatcher dispatcher = new Dispatcher();
        dispatcher.setMaxRequests(maxRequests);
        dispatcher.setMaxRequestsPerHost(maxRequestsPerHost);
        ConnectionPool connectionPool = new ConnectionPool(maxIdleConnections, keepAliveDuration, TimeUnit.SECONDS);
        client = new OkHttpClient.Builder().connectTimeout(connectTimeout, TimeUnit.SECONDS)
                                           .readTimeout(readTimeout, TimeUnit.SECONDS)
                                           .writeTimeout(writeTimeout, TimeUnit.SECONDS)
                                           .retryOnConnectionFailure(true)
                                           .dispatcher(dispatcher)
                                           .connectionPool(connectionPool)
                                           .cookieJar(CookieJar.NO_COOKIES)
                                           .build();
    }

    /**
     * 发送Form格式的Post Request
     * @param url
     * @param headers
     * @param params
     * @return
     * @throws Throwable
     */
    public static HttpResponseMessage sendOkHttp3Post(String url,
                                                      Map<String, String> headers,
                                                      Map<String, Object> params) throws Exception {
        Request request = UrlUtils.buildFormPostRequest(params, headers, url);
        return execute(request);
    }

    /**
     * 调用HTTP Request请求
     * @param request
     * @return
     * @throws Throwable
     */
    private static HttpResponseMessage execute(Request request) throws Exception {
        Response response = null;
        try {
            response = client.newCall(request).execute();
            return HttpRequestUtils.buildHttpResponseMessage(response);
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }

}
