package com.facishare.open.ding.api.exception;

import com.facishare.open.ding.api.enums.K3CloudCodeEnum;
import com.facishare.open.ding.common.result.ErrCode;
import com.facishare.open.ding.common.result.Result;

/**
 * <p>类的详细说明</p>
 * @dateTime 2018/7/12 10:51
 * <AUTHOR> y<PERSON><PERSON>@fxiaoke.com
 * @version 1.0 
 */
public class DingtalkException extends RuntimeException {

    private static final long serialVersionUID = -7949170734224838097L;
    private int errCode;
    private String errMessage;
    private String errDescription;

    public DingtalkException(ErrCode codeEnum) {
        this(codeEnum.getErrCode(), codeEnum.getErrMessage(), codeEnum.getErrDescription());
    }
    public DingtalkException(K3CloudCodeEnum codeEnum) {
        this(codeEnum.getErrCode(), codeEnum.getErrMessage(), codeEnum.getErrDescription());
    }

    public DingtalkException(K3CloudCodeEnum codeEnum, String format) {
        this(codeEnum.getErrCode(), codeEnum.getErrMessage(), String.format(codeEnum.getErrDescription(), format));
    }

    public DingtalkException(ErrCode error, Throwable cause) {
        super(error.getErrCode() + " : " + error.getErrMessage() + " : " + error.getErrDescription(), cause);
        this.errCode = error.getErrCode();
        this.errMessage = error.getErrMessage();
        this.errDescription = error.getErrDescription();
    }

    public DingtalkException(int errCode, Throwable cause, String errDescription) {
        super(errCode + " : " + errDescription + " : " + cause.getLocalizedMessage(), cause);
        this.errCode = errCode;
        this.errMessage = errDescription;
        this.errDescription = errDescription;
    }

    public DingtalkException(int errCode, Throwable cause, String errMessage, String errDescription) {
        super(errCode + " : " + errMessage + " : " + errDescription + " : " + cause.getLocalizedMessage(), cause);
        this.errCode = errCode;
        this.errMessage = errMessage;
        this.errDescription = errDescription;
    }

    public DingtalkException(int errCode, String errDescription) {
        this(errCode, "", errDescription);
    }

    public DingtalkException(int errCode, String errMessage, String errDescription) {
        super(errCode + " : " + errMessage + " : " + errDescription);
        this.errCode = errCode;
        this.errMessage = errMessage;
        this.errDescription = errDescription;
    }

    public DingtalkException(Result result) {
        this(result.getErrorCode(), result.getErrorMessage(), null);
    }

    public DingtalkException(int errCode, Result result, String errDescription) {
        super(errCode + " : " + result.getErrorCode() + "+" + result.getErrorMessage() + "+" + result.getErrorDescription() + " : " + errDescription);
        this.errCode = errCode;
        this.errDescription = errDescription;
    }

    public int getErrCode() {
        return this.errCode;
    }

    public String getErrMessage() {
        return this.errMessage;
    }

    public String getErrDescription() {
        return this.errDescription;
    }

    public void setErrCode(int errCode) {
        this.errCode = errCode;
    }

    public void setErrMessage(String errMessage) {
        this.errMessage = errMessage;
    }

    public void setErrDescription(String errDescription) {
        this.errDescription = errDescription;
    }

    public String toString() {
        return "DingtalkException(errCode=" + this.getErrCode() + ", errMessage=" + this.getErrMessage() + ", errDescription=" + this.getErrDescription() + ")";
    }

    public DingtalkException() {
    }

}
