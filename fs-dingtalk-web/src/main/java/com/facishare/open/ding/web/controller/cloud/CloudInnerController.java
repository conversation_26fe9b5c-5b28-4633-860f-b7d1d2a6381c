package com.facishare.open.ding.web.controller.cloud;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.ding.api.service.cloud.CloudToolsService;
import com.facishare.open.ding.api.service.cloud.DingAuthService;
import com.facishare.open.ding.api.vo.HighBizDataVo;
import com.facishare.open.ding.cloud.manager.EventManager;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.web.base.BaseController;
import com.facishare.open.ding.web.base.UserVo;
import com.facishare.open.ding.web.constants.ConfigCenter;
import com.facishare.open.ding.web.manager.ExcelListener.BaseListener;
import com.facishare.open.ding.web.manager.FileManager;
import com.facishare.open.ding.web.manager.excel.ReadExcel;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaDepartmentBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.fxiaoke.message.extrnal.platform.api.ExternalTodoService;
import com.fxiaoke.message.extrnal.platform.api.ExternalMessageService;
import com.fxiaoke.message.extrnal.platform.model.arg.*;
import com.fxiaoke.message.extrnal.platform.model.result.*;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>钉钉云工具类接口</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2013-06-25 11:52
 */
@CrossOrigin
@Slf4j
@RestController
@RequestMapping("/cloud/inner")
// IgnoreI18nFile
public class CloudInnerController extends BaseController {

    @Resource(name = "cloudExternalTodoServiceImpl")
    private ExternalTodoService externalTodoService;
    
    @Resource(name = "cloudExternalMessageServiceImpl")
    private ExternalMessageService externalMessageService;
    
    @Resource(name = "dingAuthServiceImpl")
    private DingAuthService dingAuthService;
    
    @Autowired
    private EventManager eventManager;

    @PostMapping(value = "/trigger/task")
    public Result<Void> updateDeptBind(@RequestParam(value = "sheetName", required = false) String sheetName, MultipartFile file) {

        return Result.newSuccess();
    }

    // ==================== TaskData转发接口 ====================
    
    /**
     * 任务数据转发接口
     * 接收老服务转发的任务数据，调用新环境的EventManager#doProcessEvent方法
     * @param highBizDataVo 任务数据
     * @return 处理结果
     */
    @PostMapping("/task/transfer")
    public Result<String> transferTaskData(@RequestBody HighBizDataVo highBizDataVo) {
        log.info("接收任务数据转发请求, corpId: {}, bizType: {}, bizId: {}", 
                highBizDataVo.getCorpId(), highBizDataVo.getBizType(), highBizDataVo.getBizId());
        
        try {
            // 将单个HighBizDataVo包装成List进行处理
            List<HighBizDataVo> bizDataVos = Arrays.asList(highBizDataVo);
            
            // 调用EventManager的doProcessEvent方法
            Result<Void> processResult = eventManager.doProcessEvent(bizDataVos);
            
            if (processResult.isSuccess()) {
                log.info("任务数据转发处理成功, corpId: {}, bizType: {}", 
                        highBizDataVo.getCorpId(), highBizDataVo.getBizType());
                return Result.newSuccess("任务数据转发处理成功");
            } else {
                log.error("任务数据转发处理失败, corpId: {}, bizType: {}, error: {}", 
                        highBizDataVo.getCorpId(), highBizDataVo.getBizType(), processResult.getErrorMessage());
                return Result.newError(processResult.getErrorCode(), "任务数据转发处理失败: " + processResult.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("任务数据转发异常, corpId: {}, bizType: {}, error: {}", 
                    highBizDataVo.getCorpId(), highBizDataVo.getBizType(), e.getMessage(), e);
            
            Result<String> errorResult = new Result<>();
            errorResult.setErrorCode(ResultCode.SYSTEM_ERROR.getErrorCode());
            errorResult.setErrorMessage("任务数据转发失败: " + e.getMessage());
            errorResult.setData(null);
            return errorResult;
        }
    }

    // ==================== DingAuthService转发接口 ====================
    
    /**
     * DingAuthService转发接口
     * 接收老服务的HTTP转发请求，调用新环境的DingAuthService
     * 请求路径：/api/dingtalk/auth/forward
     * @param requestData 转发请求数据
     * @return 转发结果
     */
    @PostMapping("/api/dingtalk/auth/forward")
    public Map<String, Object> transferDingAuthRequest(@RequestBody Map<String, Object> requestData) {
        String methodName = (String) requestData.get("methodName");
        Object[] args = null;
        
        // 解析参数数组
        Object argsObj = requestData.get("args");
        if (argsObj != null) {
            if (argsObj instanceof List) {
                List<?> argsList = (List<?>) argsObj;
                args = argsList.toArray();
            } else if (argsObj instanceof Object[]) {
                args = (Object[]) argsObj;
            }
        }
        
        if (args == null) {
            args = new Object[0];
        }
        
        log.info("接收DingAuthService转发请求, method: {}, argsCount: {}, serviceType: {}", 
                methodName, args.length, requestData.get("serviceType"));
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 使用反射调用DingAuthService的方法
            Object result = invokeDingAuthServiceMethod(methodName, args);
            
            // 处理返回结果
            response.put("success", true);
            response.put("data", result);
            response.put("message", "转发成功");
            
            log.info("DingAuthService转发成功, method: {}, result: {}", methodName, result);
            
        } catch (Exception e) {
            log.error("DingAuthService转发失败, method: {}, args: {}, error: {}", methodName, args, e.getMessage(), e);
            
            response.put("success", false);
            response.put("data", createDefaultDingAuthResult(methodName));
            response.put("message", "转发处理失败: " + e.getMessage());
        }
        
        return response;
    }
    
    /**
     * 使用反射调用DingAuthService的指定方法
     * @param methodName 方法名
     * @param args 参数数组
     * @return 调用结果
     */
    private Object invokeDingAuthServiceMethod(String methodName, Object[] args) throws Exception {
        Class<?> serviceClass = dingAuthService.getClass();
        Method[] methods = serviceClass.getMethods();
        
        // 查找匹配的方法
        Method targetMethod = null;
        for (Method method : methods) {
            if (method.getName().equals(methodName) && method.getParameterCount() == args.length) {
                // 找到方法名和参数数量匹配的方法
                targetMethod = method;
                break;
            }
        }
        
        if (targetMethod == null) {
            // 如果没有找到精确匹配的方法，尝试按方法名找第一个匹配的
            for (Method method : methods) {
                if (method.getName().equals(methodName)) {
                    targetMethod = method;
                    log.warn("DingAuthService方法参数数量不匹配，使用第一个同名方法: {}, 期望参数数: {}, 实际参数数: {}", 
                            methodName, method.getParameterCount(), args.length);
                    break;
                }
            }
        }
        
        if (targetMethod == null) {
            throw new NoSuchMethodException("未找到方法: " + methodName);
        }
        
        // 参数类型转换
        Object[] convertedArgs = convertArgumentTypes(targetMethod, args, methodName);
        
        // 调用方法
        return targetMethod.invoke(dingAuthService, convertedArgs);
    }
    
    /**
     * 转换参数类型以匹配目标方法的参数要求
     * @param targetMethod 目标方法
     * @param args 原始参数数组
     * @param methodName 方法名（用于日志）
     * @return 转换后的参数数组
     */
    private Object[] convertArgumentTypes(Method targetMethod, Object[] args, String methodName) {
        Class<?>[] parameterTypes = targetMethod.getParameterTypes();
        Object[] convertedArgs = new Object[args.length];
        
        for (int i = 0; i < args.length; i++) {
            Object arg = args[i];
            Class<?> expectedType = parameterTypes[i];
            
            if (arg == null) {
                convertedArgs[i] = null;
                continue;
            }
            
            // 如果类型已经匹配，直接使用
            if (expectedType.isAssignableFrom(arg.getClass())) {
                convertedArgs[i] = arg;
                continue;
            }
            
            // 进行类型转换
            try {
                convertedArgs[i] = convertSingleArgument(arg, expectedType);
                log.debug("参数类型转换成功: method={}, index={}, from={} to={}, value={}", 
                        methodName, i, arg.getClass().getSimpleName(), expectedType.getSimpleName(), arg);
            } catch (Exception e) {
                log.error("参数类型转换失败: method={}, index={}, from={} to={}, value={}, error={}", 
                        methodName, i, arg.getClass().getSimpleName(), expectedType.getSimpleName(), arg, e.getMessage());
                // 转换失败时，使用原始参数
                convertedArgs[i] = arg;
            }
        }
        
        return convertedArgs;
    }
    
    /**
     * 转换单个参数的类型
     * @param arg 原始参数
     * @param expectedType 期望的类型
     * @return 转换后的参数
     */
    private Object convertSingleArgument(Object arg, Class<?> expectedType) {
        String argStr = arg.toString();
        
        // String类型转换
        if (expectedType == String.class) {
            return argStr;
        }
        
        // Long类型转换
        if (expectedType == Long.class || expectedType == long.class) {
            if (arg instanceof Number) {
                return ((Number) arg).longValue();
            } else {
                return Long.parseLong(argStr);
            }
        }
        
        // Integer类型转换
        if (expectedType == Integer.class || expectedType == int.class) {
            if (arg instanceof Number) {
                return ((Number) arg).intValue();
            } else {
                return Integer.parseInt(argStr);
            }
        }
        
        // Double类型转换
        if (expectedType == Double.class || expectedType == double.class) {
            if (arg instanceof Number) {
                return ((Number) arg).doubleValue();
            } else {
                return Double.parseDouble(argStr);
            }
        }
        
        // Float类型转换
        if (expectedType == Float.class || expectedType == float.class) {
            if (arg instanceof Number) {
                return ((Number) arg).floatValue();
            } else {
                return Float.parseFloat(argStr);
            }
        }
        
        // Boolean类型转换
        if (expectedType == Boolean.class || expectedType == boolean.class) {
            return Boolean.parseBoolean(argStr);
        }
        
        // 其他类型，返回原始参数
        return arg;
    }
    
    /**
     * 创建默认的DingAuthService返回结果
     * @param methodName 方法名
     * @return 默认结果
     */
    private Object createDefaultDingAuthResult(String methodName) {
        log.info("创建DingAuthService默认返回结果, method: {}", methodName);
        
        // 创建失败的Result对象
        Result<Object> failResult = new Result<>();
        failResult.setErrorCode(ResultCode.SYSTEM_ERROR.getErrorCode());
        failResult.setErrorMessage(ResultCode.SYSTEM_ERROR.getErrorMessage());
        failResult.setData(null);
        
        return failResult;
    }

    // ==================== ExternalTodoService转发接口 ====================

    /**
     * 转发createTodo方法
     * @param createTodoArgMap 创建Todo参数
     * @return 创建Todo结果
     */
    @PostMapping("/todo/create")
    public CreateTodoResult transferCreateTodo(@RequestBody Map<String,Object> createTodoArgMap) {
        log.info("转发createTodo请求到新环境, ea: {}, title: {}", createTodoArgMap);
        
        try {
            CreateTodoArg createTodoArg = JSONObject.parseObject(JSONObject.toJSONString(createTodoArgMap),CreateTodoArg.class);
            CreateTodoResult result = externalTodoService.createTodo(createTodoArg);
            log.info("createTodo转发完成, result: {}", result);
            return result;
        } catch (Exception e) {
            log.error("createTodo转发失败, ea: {}, error: {}",createTodoArgMap);
            CreateTodoResult result = new CreateTodoResult();
            result.setCode(500);
            result.setMessage("转发处理失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 转发updateTodo方法
     * @param updateTodoArgMap 更新Todo参数
     * @return 更新Todo结果
     */
    @PostMapping("/todo/update")
    public UpdateTodoResult transferUpdateTodo(@RequestBody Map<String,Object> updateTodoArgMap) {
        log.info("转发updateTodo请求到新环境, ea: {},",
                updateTodoArgMap);
        
        try {
            UpdateTodoArg updateTodoArg = JSONObject.parseObject(JSONObject.toJSONString(updateTodoArgMap),UpdateTodoArg.class);
            UpdateTodoResult result = externalTodoService.updateTodo(updateTodoArg);
            log.info("updateTodo转发完成, result: {}", result);
            return result;
        } catch (Exception e) {
            log.error("updateTodo转发失败, ea: {}, error: {}", updateTodoArgMap, e);
            UpdateTodoResult result = new UpdateTodoResult();
            result.setCode(500);
            result.setMessage("转发处理失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 转发dealTodo方法
     * @param dealTodoArgMap 处理Todo参数
     * @return 处理Todo结果
     */
    @PostMapping("/todo/deal")
    public DealTodoResult transferDealTodo(@RequestBody Map<String,Object> dealTodoArgMap) {
        log.info("转发dealTodo请求到新环境, ea: {}",
                dealTodoArgMap);

        try {
            DealTodoArg dealTodoArg= JSONObject.parseObject(JSONObject.toJSONString(dealTodoArgMap),DealTodoArg.class);
            DealTodoResult result = externalTodoService.dealTodo(dealTodoArg);
            log.info("dealTodo转发完成, result: {}", result);
            return result;
        } catch (Exception e) {
            log.error("dealTodo转发失败, ea: {}, error: {}", dealTodoArgMap, e.getMessage(), e);
            DealTodoResult result = new DealTodoResult();
            result.setCode(500);
            result.setMessage("转发处理失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 转发deleteTodo方法
     * @param deleteTodoArgMap 删除Todo参数
     * @return 删除Todo结果
     */
    @PostMapping("/todo/delete")
    public DeleteTodoResult transferDeleteTodo(@RequestBody Map<String,Object> deleteTodoArgMap) {
        log.info("转发deleteTodo请求到新环境, ea: {}",
                deleteTodoArgMap);
        
        try {
            DeleteTodoArg deleteTodoArg= JSONObject.parseObject(JSONObject.toJSONString(deleteTodoArgMap),DeleteTodoArg.class);
            DeleteTodoResult result = externalTodoService.deleteTodo(deleteTodoArg);
            log.info("deleteTodo转发完成, result: {}", result);
            return result;
        } catch (Exception e) {
            log.error("deleteTodo转发失败, ea: {}, error: {}",deleteTodoArgMap, e.getMessage(), e);
            DeleteTodoResult result = new DeleteTodoResult();
            result.setCode(500);
            result.setMessage("转发处理失败: " + e.getMessage());
            return result;
        }
    }

    // ==================== ExternalMessageService转发接口 ====================
    
    /**
     * 转发sendTextMessage方法
     * @param sendTextMessageArg 发送文本消息参数
     * @return 发送文本消息结果
     */
    @PostMapping("/message/sendText")
    public SendTextMessageResult transferSendTextMessage(@RequestBody SendTextMessageArg sendTextMessageArg) {
        log.info("转发sendTextMessage请求到新环境, ea: {}, receiverCount: {}", 
                sendTextMessageArg.getEa(), 
                sendTextMessageArg.getReceiverIds() != null ? sendTextMessageArg.getReceiverIds().size() : 0);
        
        try {
            SendTextMessageResult result = externalMessageService.sendTextMessage(sendTextMessageArg);
            log.info("sendTextMessage转发完成, result: {}", result);
            return result;
        } catch (Exception e) {
            log.error("sendTextMessage转发失败, ea: {}, error: {}", sendTextMessageArg.getEa(), e.getMessage(), e);
            SendTextMessageResult result = new SendTextMessageResult();
            result.setCode(500);
            result.setMessage("转发处理失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 转发sendTextCardMessage方法
     * @param sendTextCardMessageArg 发送卡片消息参数
     * @return 发送卡片消息结果
     */
    @PostMapping("/message/sendTextCard")
    public SendTextCardMessageResult transferSendTextCardMessage(@RequestBody SendTextCardMessageArg sendTextCardMessageArg) {
        log.info("转发sendTextCardMessage请求到新环境, ea: {}, title: {}, receiverCount: {}", 
                sendTextCardMessageArg.getEa(), 
                sendTextCardMessageArg.getTitle(), 
                sendTextCardMessageArg.getReceiverIds() != null ? sendTextCardMessageArg.getReceiverIds().size() : 0);
        
        try {
            SendTextCardMessageResult result = externalMessageService.sendTextCardMessage(sendTextCardMessageArg);
            log.info("sendTextCardMessage转发完成, result: {}", result);
            return result;
        } catch (Exception e) {
            log.error("sendTextCardMessage转发失败, ea: {}, error: {}", sendTextCardMessageArg.getEa(), e.getMessage(), e);
            SendTextCardMessageResult result = new SendTextCardMessageResult();
            result.setCode(500);
            result.setMessage("转发处理失败: " + e.getMessage());
            return result;
        }
    }
}
