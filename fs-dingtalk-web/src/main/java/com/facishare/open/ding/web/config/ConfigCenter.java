package com.facishare.open.ding.web.config;

import com.alibaba.fastjson.JSONArray;
import com.github.autoconf.ConfigFactory;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 配置中心
 * <AUTHOR>
 * @date 2023.5.17
 */
@Component
@Data
public class ConfigCenter {
    //需要鉴权的接口列表
    public static List<String> needAuthInterfaceList;
    //合法的用户角色
    public static List<String> validRoleCodeList;

    static {
        ConfigFactory.getInstance().getConfig("fs-open-dingtalk-all", config -> {
            needAuthInterfaceList = JSONArray.parseArray(config.get("need_auth_interface_list","[]"),String.class);
            validRoleCodeList = JSONArray.parseArray(config.get("valid_role_code_list","[]"),String.class);
        });
    }
}
