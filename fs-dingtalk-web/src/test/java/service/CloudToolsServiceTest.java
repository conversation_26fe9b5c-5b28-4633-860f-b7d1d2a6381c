package service;

import com.facishare.open.ding.api.service.cloud.CloudToolsService;
import com.facishare.open.ding.common.result.Result;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class CloudToolsServiceTest extends BaseAbstractTest {
    @Autowired
    private CloudToolsService cloudToolsService;

    @Test
    public void stringResult() {
        Result<String> stringResult = cloudToolsService.queryFsEnterpriseOpen("dingfeca3fa3352c7d4ca39a90f97fcb1e09");
        System.out.println(stringResult);
    }

    @Test
    public void queryEnterpriseBindType() {
        Result<String> stringResult = cloudToolsService.queryEnterpriseBindType("ddqybhzyl");
        System.out.println(stringResult);
    }

    @Test
    public void queryFsEmployeeOpen() {
        Result<String> stringResult = cloudToolsService.queryFsEmployeeOpen("dingfeca3fa3352c7d4ca39a90f97fcb1e09", "0258205136694200");
        System.out.println(stringResult);
    }

    @Test
    public void queryFsEmployeeStatus() {
        Result<String> stringResult = cloudToolsService.queryFsEmployeeStatus("dingfeca3fa3352c7d4ca39a90f97fcb1e09", "0258205136694200");
        System.out.println(stringResult);
    }

    @Test
    public void updateFsDeptOwner() {
        cloudToolsService.updateFsDeptOwner(82379, null, null);
    }

    @Test
    public void updateEventStatus() {
        cloudToolsService.updateEventStatus("M", 1, 1717581421000L);
    }

    @Test
    public void updateEventById() {
        cloudToolsService.updateEventById("H", 3000114L, 1, null);
    }
}
