//package service;
//
//import com.alibaba.fastjson.JSONObject;
//import com.facishare.open.ding.api.constants.DataModelId;
//import com.facishare.open.ding.api.model.connector.CustomerModel;
//import com.facishare.open.ding.api.model.connector.OpportunityModel;
//import com.facishare.open.ding.api.model.connector.SalesOrderModel;
//import com.facishare.open.ding.api.service.DingCorpMappingService;
//import com.facishare.open.ding.api.service.cloud.connector.ConnectorObjectDataCacheService;
//import com.facishare.open.ding.api.vo.DingCorpMappingVo;
//import com.facishare.open.ding.cloud.arg.BatchPollDataArg;
//import com.facishare.open.ding.cloud.arg.BatchSendDataArg;
//import com.facishare.open.ding.cloud.constants.ConfigCenter;
//import com.facishare.open.ding.cloud.constants.TriggerAction;
//import com.facishare.open.ding.cloud.constants.TriggerEventId;
//import com.facishare.open.ding.cloud.manager.DingConnectorManager;
//import com.facishare.open.ding.cloud.manager.DingManager;
//import com.facishare.open.ding.cloud.result.BatchPollDataResult;
//import com.facishare.open.ding.cloud.result.BatchSendDataResult;
//import com.facishare.open.ding.cloud.result.PollDataResult;
//import com.facishare.open.ding.common.result.Result;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//
//import java.util.HashMap;
//import java.util.Map;
//
//import static com.facishare.open.ding.cloud.constants.Constant.APP_ID;
//
//@Slf4j
//public class DingConnectorManagerTest extends BaseAbstractTest {
//    @Autowired
//    private DingManager dingManager;
//    @Autowired
//    private DingConnectorManager dingConnectorManager;
//    @Autowired
//    private DingCorpMappingService dingCorpMappingService;
//    @Autowired
//    private ConnectorObjectDataCacheService connectorObjectDataCacheService;
//
//    @Test
//    public void test1() {
//        Result<DingCorpMappingVo> result = dingCorpMappingService.queryMappingByAppId("ding5bc08f4c8f18f5f64ac5d6980864d335",70480L);
//        System.out.println(result);
//    }
//
////    @Test
////    public void sendEvent2Connector() {
////        String accessToken = dingManager.getAccessToken("ding0c8ee222b9a38265f2c783f7214b6d69");
////        CustomerModel customerModel = new CustomerModel();
////        customerModel.setOperateUserId("wubb");
////        customerModel.getData().setName("测试客户名称100");
////        customerModel.getData().setCode("test_customer_code100");
////        customerModel.getData().setEmpCode("1000");
////        customerModel.getData().setRemark("remark1");
////        String jsonData = JSONObject.toJSONString(customerModel);
////        SendEventResult result = dingConnectorManager.sendEvent(accessToken,jsonData,false);
////        System.out.println(result);
////    }
//
//    @Test
//    public void batchSendCustomerData() {
//        String accessToken = dingManager.getAccessToken("ding2ecc12d52d60e1bba39a90f97fcb1e091",ConfigCenter.CRM_SUITE_ID).getData();
//
//        CustomerModel customerModel = new CustomerModel();
//        customerModel.setOperateUserId("manager8812");
//        customerModel.getData().setName("测试客户名称100");
//        customerModel.getData().setCode("test_customer_code100");
//        customerModel.getData().setEmpCode("manager8812");
//        customerModel.getData().setRemark("remark1");
//
//        CustomerModel.ContactPerson contactPerson = new CustomerModel.ContactPerson();
//        contactPerson.setName("测试联系人1");
//        CustomerModel.Address address = new CustomerModel.Address();
//        address.setProvince("广东省");
//        address.setCity("深圳市");
//        address.setDistrict("南山区");
//        address.setDetailAddress("广东省深圳市南山区大冲国际中心22楼");
//        contactPerson.setAddress(address);
//
//        CustomerModel.ContactType emailContactType = new CustomerModel.ContactType();
//        emailContactType.setType("email");
//        emailContactType.setValue("<EMAIL>");
//
//        CustomerModel.ContactType mobileContactType = new CustomerModel.ContactType();
//        mobileContactType.setType("mobile");
//        mobileContactType.setValue("18926584793");
//
//        contactPerson.getContactTypeList().add(emailContactType);
//        contactPerson.getContactTypeList().add(mobileContactType);
//
//        customerModel.getData().getContactPersonList().add(contactPerson);
//
//        String jsonData = JSONObject.toJSONString(customerModel);
//
//
//
//        BatchSendDataArg arg = new BatchSendDataArg();
//        arg.setAppId(APP_ID);
//
//        BatchSendDataArg.TriggerData triggerData = new BatchSendDataArg.TriggerData();
//        triggerData.setTriggerId(TriggerEventId.FS_CUSTOMER_EVENT);
//        triggerData.setJsonData(jsonData);
//        triggerData.setDataGmtCreate(System.currentTimeMillis());
//        triggerData.setDataGmtModified(System.currentTimeMillis());
//        triggerData.setAction(TriggerAction.UPDATE);
//
//        arg.getTriggerDataList().add(triggerData);
//
//        BatchSendDataResult result = dingConnectorManager.batchSendData(accessToken,arg);
//        System.out.println(result);
//    }
//
//    @Test
//    public void batchSendSalesOrderData() {
//        String accessToken = dingManager.getAccessToken("ding5bc08f4c8f18f5f64ac5d6980864d335", ConfigCenter.CRM_SUITE_ID).getData();
//
//        SalesOrderModel model = new SalesOrderModel();
//        model.setOperateUserId("manager8812");
//        model.getData().setCode("test_sales_order_code1");
//        model.getData().setCustomerCode("test_customer_code100");
//        model.getData().setBillDate(System.currentTimeMillis());
//        model.getData().setEmpCode("manager8812");
//        model.getData().setRemark("remark1");
//
//        SalesOrderModel.ProductModel productModel = new SalesOrderModel.ProductModel();
//        productModel.setPrice("120");
//        productModel.setQuantity("3");
//        productModel.setProductCode("SP00001");
//        productModel.setUnitCode("kg");
//        productModel.setDeliveryDate(System.currentTimeMillis());
//
//        model.getData().getProductList().add(productModel);
//
//        String jsonData = JSONObject.toJSONString(model);
//
//
//
//        BatchSendDataArg arg = new BatchSendDataArg();
//        arg.setAppId(APP_ID);
//
//        BatchSendDataArg.TriggerData triggerData = new BatchSendDataArg.TriggerData();
//        triggerData.setTriggerId(TriggerEventId.FS_SALES_ORDER_EVENT);
//        triggerData.setJsonData(jsonData);
//        triggerData.setDataGmtCreate(System.currentTimeMillis());
//        triggerData.setDataGmtModified(System.currentTimeMillis());
//        triggerData.setAction(TriggerAction.UPDATE);
//
//        arg.getTriggerDataList().add(triggerData);
//
//        BatchSendDataResult result = dingConnectorManager.batchSendData(accessToken,arg);
//        System.out.println(result);
//    }
//
//    @Test
//    public void batchSendOpportunityData() {
//        String accessToken = dingManager.getAccessToken("dingc28ba1d84eea5cd924f2f5cc6abecb85", "16896002").getData();
//
//        OpportunityModel model = new OpportunityModel();
//        model.setOperateUserId("manager8812");
//        model.getData().setCode("test_opportunity_code101");
//        model.getData().setName("test_opportunity_name101");
//        model.getData().setExpectedExpiredAt(System.currentTimeMillis());
//        model.getData().setExpectedAmount(100.0);
//        model.getData().setOwnerEmpCode("manager8812");
//        model.getData().setRelatedCustomerId("test_customer_code100");
//        model.getData().setRemark("remark1");
//
//        String jsonData = JSONObject.toJSONString(model);
//
//        BatchSendDataArg arg = new BatchSendDataArg();
//        arg.setAppId(APP_ID);
//
//        BatchSendDataArg.TriggerData triggerData = new BatchSendDataArg.TriggerData();
//        triggerData.setTriggerId(TriggerEventId.FS_OPPORTUNITY_EVENT);
//        triggerData.setJsonData(jsonData);
//        triggerData.setDataGmtCreate(System.currentTimeMillis());
//        triggerData.setDataGmtModified(System.currentTimeMillis());
//        triggerData.setAction(TriggerAction.ADD);
//
//        arg.getTriggerDataList().add(triggerData);
//
//        BatchSendDataResult result = dingConnectorManager.batchSendData(accessToken,arg);
//        System.out.println(result);
//    }
//
//    @Test
//    public void batchPollData() {
//        String accessToken = dingManager.getAccessToken("ding0c8ee222b9a38265f2c783f7214b6d69",ConfigCenter.CRM_SUITE_ID).getData();
//
//        BatchPollDataArg arg = new BatchPollDataArg();
//        arg.setDataModelId(DataModelId.CUSTOMER_DATA_MODEL_ID);
//        arg.setAppId("71075");
//
//        BatchPollDataResult result = dingConnectorManager.batchPollData(accessToken,arg);
//        System.out.println(result);
//    }
//
//    @Test
//    public void pollData() {
//        String accessToken = dingManager.getAccessToken("ding0c8ee222b9a38265f2c783f7214b6d69",ConfigCenter.CRM_SUITE_ID).getData();
//
//        BatchPollDataArg arg = new BatchPollDataArg();
//        arg.setDataModelId(DataModelId.CUSTOMER_DATA_MODEL_ID);
//        arg.setAppId("71075");
//
//        PollDataResult result = dingConnectorManager.pollData(accessToken,
//                DataModelId.CUSTOMER_DATA_MODEL_ID,
//                "test_customer_code100",
//                "71075");
//        System.out.println(result);
//    }
//}
