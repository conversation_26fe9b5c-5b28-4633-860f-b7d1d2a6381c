package service;

import com.facishare.open.ding.cloud.manager.OAConnectorDataManager;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class OAConnectorDataManagerTest extends BaseAbstractTest {
    @Autowired
    private OAConnectorDataManager oaConnectorDataManager;

    @Test
    public void sendTest() {
        oaConnectorDataManager.send(null, null, "dingding", "suite_ticket", "1231sdfgvfzdsgdfd", null, "853005", "钉钉suite_ticket缓存失效");
    }

}
