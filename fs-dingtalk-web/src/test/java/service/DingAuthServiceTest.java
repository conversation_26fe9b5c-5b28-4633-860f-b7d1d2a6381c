package service;

import com.alibaba.fastjson.JSON;
import com.facishare.open.ding.api.arg.DingOutSendMessageArg;
import com.facishare.open.ding.api.result.DingAppResult;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.result.DingOutUserResult;
import com.facishare.open.ding.api.result.OrderInfoResult;
import com.facishare.open.ding.api.service.cloud.CloudOrderService;
import com.facishare.open.ding.api.service.cloud.DingAuthService;
import com.facishare.open.ding.cloud.constants.ConfigCenter;
import com.facishare.open.ding.common.result.Result;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * Created by shun on 2021/10/20
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:dubbo-rest-test.xml")
@Slf4j
public class DingAuthServiceTest {
    @Resource
    private DingAuthService dingAuthService;

    @BeforeClass
    public static void SetUp() {
        System.setProperty("process.profile", "fstest");
        System.setProperty("process.name", "fs-dingtalk-cloud");
    }

    @Test
    public void queryInfo() {
         Result<DingOutUserResult> queryUserByCode = dingAuthService.queryUserByCode("4f194d07f80a3739933593ae97c0ca3b", "84625", "ding216ede3869d777b6a1320dcb25e91351");
        System.out.println(JSON.toJSONString(queryUserByCode));
    }
    @Test
    public  void testAuth(){
        List<String> crmToBizTypes = ConfigCenter.CRM_TO_BIZ_TYPES;
        log.info("bizType:{}");
    }

    @Test
    public void testSendMessage(){
        DingOutSendMessageArg dingOutSendMessageArg=new DingOutSendMessageArg();
        dingOutSendMessageArg.setDingCorpId("ding216ede3869d777b6a1320dcb25e91351");
        Map<String,Object> dataMap= Maps.newHashMap();
        dataMap.put("single_url","eapp://pages/share/share?marketingActivityId=618105b8eecda200014eb5f4&spreadType=1&isGroupSend=1&objectId=f29a15cfac6e4d779ab693d4a0a23462&objectType=3");
        dataMap.put("single_title","UYRRR");
        dataMap.put("title","测试消息内容占位符");
        dataMap.put("content","444444");
        dingOutSendMessageArg.setTemplateId("40ce9c7bdd6f4d7f9b6ceae71ca48ed2");
        dingOutSendMessageArg.setDataMap(dataMap);
        dingOutSendMessageArg.setSuiteId("19854003");
        dingOutSendMessageArg.setDingEmpIds(Lists.newArrayList("010762510468893014052","195817495426254224"));
        Result<String> stringResult = dingAuthService.sendMessage(dingOutSendMessageArg);
        log.info("result:{}");
    }
    @Test
    public void testType(){
        String bizType="402";
        boolean contains = ConfigCenter.CRM_TO_BIZ_TYPES.contains(bizType);
        log.info("contains");

    }

    @Test
    public void createJsApiSignature(){
        Result<DingAppResult> result = dingAuthService.createJsApiSignature("https://www.ceshi112.com/hcrm/dingtalk",
                "84801",
                70480L);
        System.out.println(result);
    }


}
