package controller;

import base.BaseAbstractTest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.dingtalk.api.response.OapiUserGetResponse;
import com.dingtalk.oapi.lib.aes.DingTalkEncryptException;
import com.dingtalk.oapi.lib.aes.DingTalkEncryptor;
import com.facishare.open.ding.api.model.AppParams;
import com.facishare.open.ding.api.model.RegisterObjMapping;
import com.facishare.open.ding.api.result.BindFxUserResult;
import com.facishare.open.ding.api.result.RedirectResult;
import com.facishare.open.ding.api.service.EnterpriseService;
import com.facishare.open.ding.api.service.SyncBizDataService;
import com.facishare.open.ding.api.vo.ConnectionVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.web.base.UserVo;
import com.facishare.open.ding.web.constants.ConfigCenter;
import com.facishare.open.ding.web.constants.Constant;
import com.facishare.open.ding.web.controller.EnterpriseController;
import com.facishare.open.ding.web.dingding.DingRequestUtil;

import java.io.IOException;

import com.facishare.open.ding.web.utils.HttpManager;
import com.facishare.open.ding.web.utils.SignatureUtils;
import com.facishare.userlogin.api.model.CreateUserTokenDto;
import com.facishare.userlogin.api.model.UserTokenDto;
import com.facishare.userlogin.api.service.SSOLoginService;
import com.fxiaoke.message.extrnal.platform.api.ExternalMessageService;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextCardMessageArg;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.jayway.jsonpath.JsonPath;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.junit.Test;
import org.omg.CORBA.PUBLIC_MEMBER;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Base64Utils;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2018/7/17 18:01
 */
@Slf4j
public class EnterpriseControllerTest extends BaseAbstractTest {
    @Autowired
    private EnterpriseController enterpriseController;

    @Autowired
    private EnterpriseService enterpriseService;

    @Autowired
    private SSOLoginService ssoLoginService;

    @Autowired
    private ExternalMessageService messageService;
    @Autowired
    private SyncBizDataService syncBizDataService;
    @Autowired
    private HttpManager httpManager;

    @Test
    public void testQuery() {

        String hasAuth="a";
        Boolean aBoolean = Boolean.valueOf(hasAuth);

        SendTextCardMessageArg arg = new SendTextCardMessageArg();
        arg.setEi(71658);
        arg.setReceiverIds(Lists.newArrayList(1003));
        arg.setMessageContent("测试123");
        arg.setTitle("待办测试");
        arg.setUrl("https://www.ceshi112.com/FHH/EM0HXUL/SSO/Login?token=%s");
        messageService.sendTextCardMessage(arg);

        ConnectionVo vo = new ConnectionVo();

        vo.setEa("test");
        Result result = enterpriseController.queryEnterprise();
        log.info("result = " + result);
    }

    @Test
    public void test() {
        CreateUserTokenDto.Argument userTokenArg = new CreateUserTokenDto.Argument(new UserTokenDto(71658, 1003));
        CreateUserTokenDto.Result ssoResult = ssoLoginService.createUserToken(userTokenArg);
        CreateUserTokenDto.LoginStatus loginStatus = ssoResult.getLoginStatus();
        String fsToken = ssoResult.getToken();

        String dicUrl = "https://www.ceshi112.com/FHH/EM0HXUL/SSO/Login?token=%s";

        BASE64Encoder encoder = new BASE64Encoder();
        String base64 = encoder.encode(dicUrl.getBytes());
        System.out.println("编码后--" + base64);
        //解码
        BASE64Decoder decoder = new BASE64Decoder();
        try {
            byte[] d = decoder.decodeBuffer(base64);
            System.out.println("解码后--" + new String(d));
        } catch (IOException e) {
            e.printStackTrace();
        }

        String redirectUrl = String.format(dicUrl, fsToken);
        String url = "dingtalk://dingtalkclient/page/link?url=" + URLEncoder.encode(redirectUrl);
        System.out.println(url);
    }

    @Test
    public void  testLogin(){
        CreateUserTokenDto.Argument userTokenArg = new CreateUserTokenDto.Argument(new UserTokenDto(82414, 1000));
        CreateUserTokenDto.Result userToken = ssoLoginService.createUserToken(userTokenArg);
        CreateUserTokenDto.LoginStatus loginStatus = userToken.getLoginStatus();
        userToken.setLoginStatus(CreateUserTokenDto.LoginStatus.EnterpriseNotReady);
        boolean name = userToken.getLoginStatus().name().equals(CreateUserTokenDto.LoginStatus.EnterpriseNotReady.name());

        if(userToken.getLoginStatus().name().equals(CreateUserTokenDto.LoginStatus.EnterpriseNotReady.name())){

            log.info("ssss");
        }
        log.info("21212");

    }


    //加解密的信息
    @Test
    public void testEncry() throws DingTalkEncryptException {
//        DingTalkEncryptor dingTalkEncryptor = new DingTalkEncryptor(Constant.TOKEN, Constant.ENCODING_AES_KEY,
//                "ding5fe5aa1ba078facc24f2f5cc6abecb85");
//
        String plaintext = "{\"CorpId\":\"ding5fe5aa1ba078facc24f2f5cc6abecb85\",\"EventType\":\"org_dept_create\",\"DeptId\":[394773729],\"TimeStamp\":\"1597236510165\"}";
        /**
         * args=[21f765fd1ebcc0bce9b95c440ae1ff61f1256e25, 159750845
         * 2640, ilIUV9zM, 76246, {"encrypt":"VlPdwvlJADvTPWRJ0r0HVS20/usV8cn22XceVua3BXXVf7COeYe3G2DxqRxoVofdZYpmNJwzfyKDrwjiqD2IxdZaZ8IphRVtB1wAqdeF/VxIgS0/FJpjfbc6ag3arAFc3/1077aiivNAWkUc2wQedT1Y7CBQS4tELrQpGp67Xf1TvY5hxAHEHVIw51EYSQ15r1gD3x2P+3qPFQ2jnwYAw5nldf
         * y8tYXcNElFVIvIvOZXT59FKEAblA3nJ7+gabcK"}]
         */
//        Map<String, String> irEx13mD = dingTalkEncryptor.getEncryptedMap(plaintext, 1597236510165L, "irEx13mD");
//        log.info(""+irEx13mD);
//
//
//        String decryptMsg = dingTalkEncryptor.getDecryptMsg("e6ff36d44e311d4b61c24ea6a253684aeddebefb", "1597236510165", "irEx13mD", "5G4ozpINjd7It6gR0LuU4lgHVs4/nMnQLSUa//zNhHK/Y6u2tRadugz48Ji23Cd5FNT+emqu7ntGQQljEs3XH2k1aWzBI4XiSPZfR/JaRQFFofiOwobPR/kRc2OmvfpZ6s51AH9mG2AogGK/FXkDnqiAEJniGfZi26GAFVJnMw4UBKC2ZiK3KtPdw+h0DjWTGAbhDaLACzxRAqrBPGY4D2HYUY7Ikn3RJIuteI/2jjlq5h8/Gw3xfijVQzwhRV8C");
//        log.info(""+decryptMsg);
        DingTalkEncryptor dingTalkEncryptor = new DingTalkEncryptor(Constant.TOKEN, Constant.ENCODING_AES_KEY,
                "ding5fe5aa1ba078facc24f2f5cc6abecb85");
        //从post请求的body中获取回调信息的加密数据进行解密处理
        String encryptMsg = "kawV27KQQYejsj\n" +
                "FLzKcz8X1x4Y4+tX6Om2sruYbVDzU1Equ9cm8eJOCFcf4T/PnVmbIwFsPo7Z3LEc3LIZtDAa1KNJwTxS7ImY8KbZ68tfrMbhqQv34LMAQMkKaIn3yQZUG81DFmg/7C/xkuuFdjbg2koePbSFcyv1lV7sTb6Hs/vvufMqz2iU3DGleQVNWfmjfnovFu0Q0BLXk2f7BrQuDLKbwP+s/7gpLTGVc/rlNDnASnpuLuIJV+k5+6GTVE";
        String plainText = dingTalkEncryptor.getDecryptMsg("466a0f21ddf2ac7cc086b564408e2d44a8f30bfc", "1615434047958", "6QB9hla0", encryptMsg);
        log.info("plainText={}", plainText);
        //加密
        DingTalkEncryptor newDingTalk = new DingTalkEncryptor(Constant.TOKEN, Constant.ENCODING_AES_KEY,
                "ding5fe5aa1ba078facc24f2f5cc6abecb85");
        Map<String, String> dingMap = newDingTalk.getEncryptedMap(plaintext, 1597236510165L, "irEx13mD");

        log.info("" + dingMap);

        String decryptMsg = newDingTalk.getDecryptMsg(dingMap.get("msg_signature").toString(), dingMap.get("timeStamp").toString(), dingMap.get("nonce").toString(), dingMap.get("encrypt").toString());
        log.info("" + decryptMsg);
    }
    /**
     * 绿米回调
     */
    @Test
    public void encryTest() throws DingTalkEncryptException {

//        String nonce = "irEx13mD";
//        String plainText = "{\r\n" +
//                "     'EventType': 'user_add_org',\r\n" +
//                "     'TimeStamp': '1597371357080',\r\n" +
//                "     'CorpId': 'ding1d769e1e02af8dfc35c2f4657eb6378f',\r\n" +
//                "     'UserId': ['172515221729079951']\r\n" +
//                " }";
//        String timestamp = "1597371357080";




        String nonce="irEx13mD";
        String plaintext = "{\"CorpId\":\"ding1d769e1e02af8dfc35c2f4657eb6378f\",\"EventType\":\"user_add_org\",\"UserId\":['172515221729079951'],\"TimeStamp\":\"1597371357080\"}";
        String timestamp="1597371357080";
        DingTalkEncryptor dingTalkEncryptor = new DingTalkEncryptor(Constant.TOKEN, Constant.ENCODING_AES_KEY,
                "ding1d769e1e02af8dfc35c2f4657eb6378f");
        Map<String, String> encryptedMap = dingTalkEncryptor.getEncryptedMap(plaintext, Long.valueOf(timestamp), nonce);
        log.info(""+encryptedMap);
        String decryptMsg = dingTalkEncryptor.getDecryptMsg(encryptedMap.get("msg_signature").toString(), encryptedMap.get("timeStamp").toString(), encryptedMap.get("nonce").toString(), encryptedMap.get("encrypt").toString());
        log.info("" + decryptMsg);
    }

    @Test
    public void testUser(){
        OapiUserGetResponse dingskqye5ljk4pogfbf = DingRequestUtil.getUser("dingskqye5ljk4pogfbf", "D4F0V5a8-HRUvXck3LDtP_AGw7UDQP_D_0k2l8kmttFOURFIYyrJkUcBGvvt_OwL", "011037513926803436702");
        log.info("user:{}",dingskqye5ljk4pogfbf);

    }

    @Test
    public void testGetToken(){
        Map<String, String> accessToken = DingRequestUtil.getProxyToken("dingd0vww6pgdoy16ozi", "28lTExiCrOCpDOfNGg-MaugItuNJWph99VZRFQsiYr6EMoZCJAfqLC-PHcfmDo53");
        System.out.println("EnterpriseControllerTest.testGetToken");
    }

    @Test
    public void testAuth(){
        Long timeStamp = new Date().getTime();
        String signature = SignatureUtils.snsInfoSignature(ConfigCenter.SUITE_SECRET,timeStamp);
        String url = "https://oapi.dingtalk.com/sns/getuserinfo_bycode?accessKey=" + ConfigCenter.SUITE_KEY + "&timestamp=" + timeStamp + "&signature=" + signature;
        Map<String, Object> argMap = Maps.newHashMap();
        argMap.put("tmp_auth_code", "30c38e818a85388c90fda2e49b176620");
         httpManager.postUrl(url, argMap, createHeader());

    }

    public String getSuiteTicket() {
        Result<String> ticketResult = syncBizDataService.queryByTicket(ConfigCenter.CRM_SUITE_ID);
        if (org.apache.commons.lang3.ObjectUtils.isEmpty(ticketResult.getData())) {
            log.warn("query ticket fail");
            return null;
        }
        return ticketResult.getData();
    }

    public static Map<String, String> createHeader() {
        Map<String, String> headers = Maps.newHashMapWithExpectedSize(1);
        headers.put("Content-Type", "application/json");
        return headers;
    }

    @Test
    public void testSuite(){
        List<AppParams> listParams=Lists.newArrayList();

        AppParams appParams=new AppParams();
        appParams.setAppId("90901");
        appParams.setSuiteId("90901");
        appParams.setSuiteKey("90901");
        appParams.setSuiteSecret("90901");
        AppParams appParams1=new AppParams();
        appParams1.setAppId("90901");
        appParams1.setSuiteId("90901");
        appParams1.setSuiteKey("90901");
        appParams1.setSuiteSecret("90901");
        listParams.add(appParams);
        listParams.add(appParams1);
        String params = JSONObject.toJSONString(listParams);


        log.info("test suite params");
    }
    @Test
    public void testMessage(){
        String suiteId="20107003";
        suiteId=Optional.ofNullable(suiteId).orElse(ConfigCenter.CRM_SUITE_ID);
        log.info("suite");
    }
    @Test
    public void testBase(){
        RegisterObjMapping registerObjMapping = ConfigCenter.COMPONENT_MAP.get(1);

        String nnn="https%3A%2F%2Fwww.ceshi112.com%2Fhcrm%2Fdingtalk%23%2Fcrm%2Fbi%3Fid%3Dxxx";
        try {
            String data = Base64Utils.encodeToString(nnn.getBytes("UTF-8"));
            log.info("data");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testObj(){
        String initEmpUrl = ConfigCenter.ERP_PUSH_DATA_URL.concat("/out/ding/crm/connector/dingCrmObjectId2FsCrmObjectId?tenantId=").
                concat("82379").concat("&dingObjectApiName=").concat("crm_customer").concat("&dingObjectId=").concat("9b296ddf-32b1-4f8f-81d7-ee1c6a1c91de");
        Map<String, Object> objectMap = httpManager.postUrl(initEmpUrl, Maps.newHashMap(), createHeader());
        String errCode= JsonPath.read(objectMap.get("body").toString(),"$.errCode");
        if(errCode.equals("s106240000")){
            String data=JsonPath.read(objectMap.get("body").toString(),"$.data");
        }
        log.info("result");
    }

    @Test
    public void testData(){
        String dataUrl="https://www.ceshi112.com/dingtalk/module.jsp?corpId=$CORPID$&suiteId=********&redirectUrl=https%3A%2F%2Fwww.ceshi112.com%2FXV%2FUI%2FHome%23crm%2Fform%2F%3D%2Fadd%2Fapiname%3DCasesObj%26account_id%3D61e9394cc1b33c00011a5c35%26account_id__r%3D同步数据1111";
        String replaceUrl = dataUrl.replace("$","").replaceAll("CORPID", "dingfeca3fa3352c7d4ca39a90f97fcb1e09");
        log.info("replaceUrl");
    }

}
