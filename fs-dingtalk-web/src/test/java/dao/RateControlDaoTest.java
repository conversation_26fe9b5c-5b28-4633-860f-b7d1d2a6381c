package dao;

import base.BaseAbstractTest;
import com.facishare.open.ding.provider.mongodb.RateControlPolicyDao;
import com.facishare.open.ding.provider.mongodb.entity.RateControlPolicyDo;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 *<p>类的详细说明</p>
 * @dateTime 2018/7/11 14:49
 * <AUTHOR> <EMAIL>
 * @version 1.0 
 */
@Slf4j
public class RateControlDaoTest extends BaseAbstractTest {

    @Autowired
    private RateControlPolicyDao rateControlPolicyDao;

    @Test
    public void findByIdTest() {
        RateControlPolicyDo byId = rateControlPolicyDao.findById(new ObjectId("58a14db667e2046acb9d12ca"));
//        DingEnterprise fktest = kcEnterpriseDao.findByEA("fktest");
        Assert.assertNotNull("findById is not null", byId);

    }


}