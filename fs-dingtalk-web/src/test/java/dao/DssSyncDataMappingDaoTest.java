package dao;

import base.BaseAbstractTest;
import com.facishare.open.ding.api.service.DssSyncDataMappingService;
import com.facishare.open.ding.api.vo.CropMappingVo;
import com.facishare.open.ding.provider.dss.dao.DssSyncDataMappingDao;
import com.facishare.open.ding.provider.dss.entity.DssSYncDataMappingEntity;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class DssSyncDataMappingDaoTest extends BaseAbstractTest {

    @Autowired
    private DssSyncDataMappingDao dssSyncDataMappingDao;

    @Autowired
    private DssSyncDataMappingService dataMappingMove;

    @Test
    public void test(){
        List<DssSYncDataMappingEntity> accountObj =
          dssSyncDataMappingDao.queryMappingByDestCrmObjectApiName(String.valueOf(81243), "AccountObj",0,1000);
        log.info("res:{}",accountObj);
    }

    @Test
    public void dataMappingMove(){
            List<CropMappingVo>cropMappingVos=new ArrayList<>();
            CropMappingVo cropMappingVo=new CropMappingVo();
            cropMappingVo.setTenantId(82379);
            cropMappingVo.setCropId("dingfeca3fa3352c7d4ca39a90f97fcb1e09");
            cropMappingVos.add(cropMappingVo);
          dataMappingMove.dataMappingMove(cropMappingVos);
        log.info("res:{}",cropMappingVos);
    }

}
