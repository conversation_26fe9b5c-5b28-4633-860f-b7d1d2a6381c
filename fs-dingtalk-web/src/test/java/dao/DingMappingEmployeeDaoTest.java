package dao;

import base.BaseAbstractTest;
import com.dingtalk.api.response.OapiUserListResponse;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.service.DingCorpMappingService;
import com.facishare.open.ding.api.vo.DingCorpMappingVo;
import com.facishare.open.ding.common.model.User;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.provider.dao.DingCorpMappingDao;
import com.facishare.open.ding.provider.dao.DingMappingEmployeeDao;
import com.facishare.open.ding.provider.entity.DingMappingEmployee;
import com.github.mybatis.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>企业绑定</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2018/7/11 17:01
 */
@Slf4j
public class DingMappingEmployeeDaoTest extends BaseAbstractTest {
    @Autowired
    private DingMappingEmployeeDao dingMappingEmployeeDao;
    @Autowired
    private DingCorpMappingService dingCorpMappingService;

    @Test
    public void testFindByEI(){
        DingMappingEmployee dingMappingEmployee = new DingMappingEmployee();
        dingMappingEmployee.setEi(2);
        dingMappingEmployee.setEmployeeStatus(1);
//        dingMappingEmployeeDao.insert(dingMappingEmployee);
        List<DingMappingEmployee> dingMappingEmployee2 = dingMappingEmployeeDao.findByEI(71658,1,0,10,"","",null);
        Assert.assertNotNull("dingMappingEmployee2 is not null", dingMappingEmployee2);
    }

    @Test
    public void testFindCount(){
        Integer count = dingMappingEmployeeDao.findCountByEI(71658,0, "", "");

    }

    @Test
    public void testBatchUpdate(){
        DingMappingEmployeeResult kcMappingEmployee = new DingMappingEmployeeResult();
        kcMappingEmployee.setEmployeePhone("3333");

//        DingMappingEmployeeResult kcMappingEmployee2 = new DingMappingEmployeeResult();
//        kcMappingEmployee2.setEmployeePhone("13312112112");
//        kcMappingEmployee2.setCloudStatus(1);
//        kcMappingEmployee2.setCloudCode("00001");

        List<DingMappingEmployeeResult> list = new ArrayList<>();
        list.add(kcMappingEmployee);
//        list.add(kcMappingEmployee2);
        System.out.println(list);
        dingMappingEmployeeDao.updateMappingEmployee(kcMappingEmployee);
    }

    @Test
    public void testDelete(){
        DingMappingEmployeeResult kcMappingEmployee = new DingMappingEmployeeResult();
        kcMappingEmployee.setEmployeePhone("3333");
        dingMappingEmployeeDao.deleteBind(71658,"lisixx001");
    }

    @Test
    public void testRelieve(){
        Integer count = dingMappingEmployeeDao.relieveBind(71658,"29114832441156570159");
        System.out.println(count);
    }

    @Test
    public void testUpdateMapping(){
        DingMappingEmployeeResult kcMappingEmployee = new DingMappingEmployeeResult();
        kcMappingEmployee.setEmployeeName("444");
        kcMappingEmployee.setEi(708205);
        kcMappingEmployee.setEmployeeId(1052);
        dingMappingEmployeeDao.updateMappingEmployee(kcMappingEmployee);
    }

    @Test
    public void testInitEmp(){
//        List<User> list = new ArrayList<>();
//        User userlist = new User();
//        userlist.setMobile("13324439877");
//        userlist.setName("李四");
//        userlist.setUserid("lisixx001");
//        list.add(userlist);
//        dingMappingEmployeeDao.initMappingEmployee(list,71658,1001,1L,"dept1");

    }

    @Test
    public void insert() {
        DingMappingEmployee dingMappingEmployee = new DingMappingEmployee();
        dingMappingEmployee.setEi(81243);
        dingMappingEmployee.setEmployeeId(1001);
        dingMappingEmployee.setEmployeeName("小贝");
        dingMappingEmployee.setEmployeePhone("18926584794");
        dingMappingEmployee.setDingEmployeeId("xiaobei");
        dingMappingEmployee.setDingEmployeeName("小贝");
        dingMappingEmployee.setDingEmployeePhone("18926584794");
        int count = dingMappingEmployeeDao.insert(dingMappingEmployee);
        System.out.println(count);
    }

    @Test
    public void test3() {
        Result<DingCorpMappingVo> dingCorpMappingVoResult = dingCorpMappingService.queryMappingByAppId("ding0c8ee222b9a38265f2c783f7214b6d69",71075L);
        DingCorpMappingVo dingCorpMappingVo = dingCorpMappingVoResult.getData();
        dingCorpMappingVo.setCategory("bbb");
        dingCorpMappingService.update(dingCorpMappingVo);
    }
}
