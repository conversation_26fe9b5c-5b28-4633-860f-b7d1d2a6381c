package com.facishare.open.order.contacts.proxy.api.service;

import com.facishare.open.order.contacts.proxy.api.arg.DealCrmTodoArg;
import com.facishare.open.order.contacts.proxy.api.result.Result;
import com.facishare.open.order.contacts.proxy.api.result.ResultV2;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;

import java.util.List;

public interface FsObjServiceProxy {
    /**
     * 查询对象列表
     * @return
     */
    Result<List<ObjectDescribe>> listAllObjects(Integer ei);

    /**
     * 查询详细对象
     * @param ei
     * @param objectApiName
     * @param ObjectId
     * @return
     */
    Result<ControllerGetDescribeResult> queryObjectData(Integer ei, String objectApiName, String ObjectId);

    ResultV2<Boolean> dealCrmTodo(Integer ei, String userId, DealCrmTodoArg arg);

    Result<List<ObjectData>> queryEnterpriseInterconnect(Integer ei, String param);

    Result<List<ObjectData>> queryEmployeeInterconnect(Integer ei, String param);

    Result<List<ObjectData>> queryWechatFriendsRecordObj(Integer ei, String param);

    Result<Void> bulkInvalidWechatFriendsRecordObj(Integer ei, String param);
}
