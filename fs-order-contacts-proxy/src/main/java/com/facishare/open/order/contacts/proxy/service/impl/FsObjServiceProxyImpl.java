package com.facishare.open.order.contacts.proxy.service.impl;

import com.alibaba.fastjson.TypeReference;
import com.facishare.open.order.contacts.proxy.api.arg.DealCrmTodoArg;
import com.facishare.open.order.contacts.proxy.api.network.ProxyHttpClient;
import com.facishare.open.order.contacts.proxy.api.result.Result;
import com.facishare.open.order.contacts.proxy.api.result.ResultCodeEnum;
import com.facishare.open.order.contacts.proxy.api.result.ResultV2;
import com.facishare.open.order.contacts.proxy.api.service.FsObjServiceProxy;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.order.contacts.proxy.config.ConfigCenter;
import com.facishare.open.order.contacts.proxy.limiter.CrmRateLimiter;
import com.fxiaoke.crmrestapi.arg.ControllerDetailArg;
import com.fxiaoke.crmrestapi.arg.FindDescribeManageListArg;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.crmrestapi.service.ObjectDescribeCrmService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

@Service("fsObjServiceProxy")
public class FsObjServiceProxyImpl implements FsObjServiceProxy {
    @Resource
    private ObjectDescribeCrmService objectDescribeCrmService;
    @Resource
    private MetadataControllerService metadataControllerService;
    @Resource
    private ProxyHttpClient proxyHttpClient;
    @Resource
    private FsEmployeeAndDepartmentProxy fsEmployeeAndDepartmentProxy;

    @Override
    public Result<List<ObjectDescribe>> listAllObjects(Integer ei) {
        HeaderObj headerObj = HeaderObj.newInstance(ei, -10000);
        List<ObjectDescribe> objectDescribeList = objectDescribeCrmService.findDescribeManageList(headerObj, new FindDescribeManageListArg()).getData().getObjectDescribeList();
        return Result.newSuccess(objectDescribeList);
    }


    @Override
    public Result<ControllerGetDescribeResult> queryObjectData(Integer ei, String objectApiName, String ObjectId) {
        HeaderObj headerObj = HeaderObj.newInstance(ei, -10000);

        ControllerDetailArg controllerDetailArg = new ControllerDetailArg();
        controllerDetailArg.setObjectDataId(ObjectId);
        controllerDetailArg.setObjectDescribeApiName(objectApiName);
        controllerDetailArg.setIsFromRecycleBin (Boolean.TRUE);
        com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> detail = metadataControllerService.detail(headerObj, objectApiName, controllerDetailArg);

        return Result.newSuccess(detail.getData());
    }

    @Override
    public ResultV2<Boolean> dealCrmTodo(Integer ei, String userId, DealCrmTodoArg arg) {
        if(!CrmRateLimiter.isAllowed(null)) {
            return ResultV2.newError(ResultCodeEnum.SYSTEM_ERROR);
        }

        String url = ConfigCenter.DEAL_CRM_TODO_URL;
        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-Type", "application/json; charset=utf-8");
        headerMap.put("x-tenant-id", String.valueOf(ei));
        headerMap.put("x-user-id", userId);
        ResultV2<Boolean> result = proxyHttpClient.postUrl(url, arg, headerMap, new TypeReference<ResultV2<Boolean>>() {
        });
        return result;
    }

    @Override
    public Result<List<ObjectData>> queryEnterpriseInterconnect(Integer ei, String param) {
        String url = ConfigCenter.CRM_REST_OBJ_URL+ "/EnterpriseRelationObj/controller/List";

        Result<List<ObjectData>> result = fsEmployeeAndDepartmentProxy.postUrl3(url, param, ei + "");
        LogUtils.info("FsOrderServiceProxyImpl.queryCustomer,ei={},result={}", ei, result);
        return result;
    }

    @Override
    public Result<List<ObjectData>> queryEmployeeInterconnect(Integer ei, String param) {
        String url = ConfigCenter.CRM_REST_OBJ_URL+ "/PublicEmployeeObj/controller/List";

        Result<List<ObjectData>> result = fsEmployeeAndDepartmentProxy.postUrl3(url, param, ei + "");
        LogUtils.info("FsOrderServiceProxyImpl.queryCustomer,ei={},result={}", ei, result);
        return result;
    }

    @Override
    public Result<List<ObjectData>> queryWechatFriendsRecordObj(Integer ei, String outUserId, Integer limit, Integer offset) {
        String url = ConfigCenter.CRM_REST_OBJ_URL+ "/WechatFriendsRecordObj/controller/List";

        String param = "{\"serializeEmpty\":false,\"extractExtendInfo\":true,\"object_describe_api_name\":\"WechatFriendsRecordObj\",\"search_template_id\":\"5d0c806a7cfed91f3e95ba8e\",\"include_describe\":false,\"include_layout\":false,\"need_tag\":true,\"search_template_type\":\"default\",\"ignore_scene_record_type\":true,\"search_query_info\":\"{\\\"limit\\\":{limit},\\\"offset\\\":{offset},\\\"filters\\\":[{\\\"field_name\\\":\\\"qywx_user_id\\\",\\\"field_values\\\":[\\\"{qywx_user_id }\\\"],\\\"operator\\\":\\\"EQ\\\"}],\\\"orders\\\":[{\\\"fieldName\\\":\\\"last_modified_time\\\",\\\"isAsc\\\":false}]}\",\"pageSizeOption\":[20,50,100],\"list_component\":{\"isRunning\":true,\"type\":\"list\",\"isConnectApp\":false,\"api_name\":\"list_component\",\"header\":\"列表页\",\"nameI18nKey\":\"paas.udobj.list_page\",\"view_info\":[{\"name\":\"list_view\",\"is_default\":true,\"is_show\":true},{\"name\":\"split_view\",\"is_default\":false,\"is_show\":true}],\"filters_info\":[{\"fields\":[],\"page_type\":\"list\"}],\"button_info\":[{\"hidden\":[],\"page_type\":\"list\",\"render_type\":\"list_normal\",\"order\":[\"Add_button_default\",\"IntelligentForm_button_default\",\"Import_button_default\",\"Export_button_default\",\"ExportFile_button_default\"],\"exposed_button\":1},{\"hidden\":[],\"page_type\":\"list\",\"render_type\":\"list_batch\",\"order\":[\"FriendInherit_button_default\",\"ChangeOwner_button_default\",\"Abolish_button_default\",\"AddTeamMember_button_default\",\"DeleteTeamMember_button_default\",\"Lock_button_default\",\"Unlock_button_default\",\"Export_button_default\",\"ExportFile_button_default\",\"ChangePartnerOwner_button_default\",\"Print_button_default\",\"button_xt521__c\"],\"exposed_button\":null},{\"hidden\":[],\"page_type\":\"list\",\"render_type\":\"list_single\",\"order\":[],\"exposed_button\":0}],\"define_view_info\":[\"list_view\",\"split_view\"],\"scene_info\":[{\"hidden\":[],\"page_type\":\"list\",\"render_type\":\"drop_down\",\"order\":[\"All\",\"Participate\",\"InCharge\",\"SubInCharge\",\"InChargeDept\",\"Shared\",\"SubParticipate\"]}],\"attributes\":{\"field_align\":null,\"enable_mobile_layout\":null},\"sourceId\":\"WechatFriendsRecordObj\",\"pluginParams\":{\"entryType\":\"\",\"objectApiName\":\"WechatFriendsRecordObj\"},\"disableLazyload\":true}}"
                .replace("{qywx_user_id}", outUserId)
                .replace("{limit}", limit + "")
                .replace("{offset}", offset + "");

        Result<List<ObjectData>> result = fsEmployeeAndDepartmentProxy.postUrl3(url, param, ei + "");
        LogUtils.info("FsOrderServiceProxyImpl.queryWechatFriendsRecordObj,ei={},result={}", ei, result);
        return result;
    }

    @Override
    public Result<Void> bulkInvalidWechatFriendsRecordObj(Integer ei, String id) {
        String url = ConfigCenter.CRM_REST_OBJ_URL+ "/WechatFriendsRecordObj/action/BulkInvalid";
        String param = "{\"json\":\"{\\\"dataList\\\":[{\\\"object_describe_api_name\\\":\\\"WechatFriendsRecordObj\\\",\\\"tenant_id\\\":\\\"84883\\\",\\\"_id\\\":\\\"{_id}\\\"}]}\"}"
                .replace("{_id}", id);
        Result<Void> result = fsEmployeeAndDepartmentProxy.postUrl7(url, param, ei + "");
        LogUtils.info("FsOrderServiceProxyImpl.bulkInvalidWechatFriendsRecordObj,ei={},result={}", ei, result);
        return result;
    }


}
