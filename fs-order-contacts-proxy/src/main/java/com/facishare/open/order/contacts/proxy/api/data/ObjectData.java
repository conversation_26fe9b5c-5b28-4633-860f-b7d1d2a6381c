package com.facishare.open.order.contacts.proxy.api.data;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ObjectData implements Serializable {
    @JsonProperty("owner")
    private List<String> owner;
    
    @JsonProperty("UDSText7__c")
    private String udsText7;
    
    @JsonProperty("UDSText6__c")
    private String udsText6;

    @JsonProperty("UDSText2__c")
    private String udsText2;
    
    @JsonProperty("UDSText9__c")
    private String udsText9;
    
    @JsonProperty("field_fsj82__c")
    private String fieldFsj82;
    
    @JsonProperty("object_describe_api_name")
    private String objectDescribeApiName;
    
    @JsonProperty("name")
    private String name;
    
    @JsonProperty("UDTel1__c")
    private String udTel1;
    
    @JsonProperty("change_type")
    private int changeType;
    
    @JsonProperty("account_source")
    private String accountSource;
}
