package com.facishare.open.order.contacts.proxy.api.model;

import com.facishare.open.order.contacts.proxy.api.arg.DealCrmTodoArg;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DealCrmTodoModel implements Serializable {
    private Integer fsEi;
    private String fsUserId;
    private DealCrmTodoArg dealCrmTodoArg;
}
