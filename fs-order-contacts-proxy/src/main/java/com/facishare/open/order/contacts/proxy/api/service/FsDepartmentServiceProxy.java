package com.facishare.open.order.contacts.proxy.api.service;

import com.facishare.open.order.contacts.proxy.api.arg.FsDeptArg;
import com.facishare.open.order.contacts.proxy.api.result.Result;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.fxiaoke.crmrestapi.common.data.ObjectData;

import java.util.List;

/**
 * 纷享部门服务代理
 * <AUTHOR>
 * @date 20220731
 */
public interface FsDepartmentServiceProxy {
    /**
     * 创建部门
     * @param arg
     * @return
     */
    Result<ObjectData> create(FsDeptArg arg);

    /**
     * 更新部门
     * @param arg
     * @return
     */
    Result<Void> update(FsDeptArg arg);

    /**
     * 批量停用部门
     * @param ei
     * @param depIdList
     * @return
     */
    Result<Void> bulkStop(String ei,List<String> depIdList);

    /**
     * 批量启用员工
     * @param ei
     * @param depIdList
     * @return
     */
    Result<Void> bulkResume(String ei,List<String> depIdList);

    /**
     * 启用或停用部门
     */
    Result<Void> toggle(String ei,String id,boolean enable);

    /**
     * 获取当前部门下的所有子部门，包括当前部门
     * @param ei
     * @return
     */
    Result<List<ObjectData>> list(Integer ei,String depId);

    /**
     * 获取部门详情
     * @param ei
     * @param depId
     * @return
     */
    Result<ObjectData> detail(int ei,String depId);

    /**
     * 获取企业下所有的部门信息
     * @param ei
     * @return
     */
    Result<List<DepartmentDto>> getAllDepartmentDto(Integer ei);

    /**
     * 获取所有下级部门
     * @param ei
     * @param departmentId
     * @return
     */
    Result<List<DepartmentDto>> getChildrenDepartment(Integer ei, Integer departmentId);

    /**
     * 获取部门信息
     * @param ei
     * @return
     */
    Result<DepartmentDto> getDepartmentDto(Integer ei, Integer departmentId);
}