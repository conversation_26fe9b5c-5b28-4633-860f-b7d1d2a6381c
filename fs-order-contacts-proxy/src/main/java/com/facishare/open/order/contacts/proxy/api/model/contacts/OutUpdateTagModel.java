package com.facishare.open.order.contacts.proxy.api.model.contacts;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OutUpdateTagModel implements Serializable {
    private String tagId;
    private List<String> addEmpList;
    private List<String> removeEmpList;
    private List<String> addDepList;
    private List<String> removeDepList;
}
