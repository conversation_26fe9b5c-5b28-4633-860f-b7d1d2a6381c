<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <description>dubbo消费者接口</description>

    <dubbo:reference id="enterpriseEditionService"
                     interface="com.facishare.uc.api.service.EnterpriseEditionService"
                     protocol="dubbo"
                     retries="0"/>
    <dubbo:reference id="smsSender"
                     interface="com.facishare.sms.api.provider.SMSSender"
                     protocol="dubbo"
                     retries="0"/>
    <dubbo:reference id="permissionService"
                     interface="com.facishare.organization.adapter.api.permission.service.PermissionService"
                     version="5.7"/>
<!--    <dubbo:reference id="employeeProviderService"-->
<!--                     interface="com.facishare.organization.api.service.EmployeeProviderService"-->
<!--                     version="5.7"/>-->
<!--    <dubbo:reference id="departmentProviderService"-->
<!--                     interface="com.facishare.organization.api.service.DepartmentProviderService"-->
<!--                     version="5.7"/>-->
    <dubbo:reference id="coreRegisterService"
                     interface="com.facishare.register.api.service.CoreRegisterService"
                     timeout="120000"/>
    <dubbo:reference id="enterpriseConfigService"
                     interface="com.facishare.organization.adapter.api.config.service.EnterpriseConfigService"/>
    <dubbo:reference id="enterpriseService"
                     interface="com.facishare.organization.adapter.api.service.EnterpriseService"/>
    <dubbo:reference id="employeeEditionService"
                     interface="com.facishare.uc.api.service.EmployeeEditionService"/>
    <dubbo:reference id="versionRegisterService"
                     interface="com.facishare.webhook.api.service.VersionRegisterService"/>

    <dubbo:reference id="fsEventService"
                     interface="com.facishare.open.order.contacts.proxy.api.service.FsEventService"
                     protocol="dubbo"
                     version="1.0"
                     check="false"/>
</beans>