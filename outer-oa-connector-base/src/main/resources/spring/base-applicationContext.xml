<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:task="http://www.springframework.org/schema/task" xmlns:c="http://www.springframework.org/schema/c"
       xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
       http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd
       http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task.xsd">

    <context:annotation-config/>
    <task:annotation-driven executor="taskExecutor"/>
    <context:component-scan base-package="com.facishare.open.erpdss.outer.oa.connector.base"/>
    <import resource="classpath:spring/fs-fsc-rest-client.xml"/>
    <import resource="all-rest-api.xml"/>
    <import resource="classpath:fs-plat-privilege-api-rest-client.xml"/>
<!--    <import resource="spring-job.xml"/>-->
    <import resource="classpath:spring/fs-qixin-rest-client.xml"/>
    <!-- 引入license-->
    <import resource="classpath:spring/license-client.xml"/>
    <!--id生成器-->
    <import resource="classpath:spring/vesta-service-property-factory-bean.xml"/>
    <import resource="classpath:spring/fs-warehouse-rest-client.xml"/>
<!--    <import resource="classpath:otherrest/otherrest.xml"/>-->
    <!--监控-->
    <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>

<!--    &lt;!&ndash;redis配置&ndash;&gt;-->
<!--    <bean id="publishRedis" class="com.github.jedis.support.JedisFactoryBean" scope="singleton"-->
<!--          p:configName="fs-feishu-config"/>-->
<!--    <bean id="redisDataSource" class="com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource">-->
<!--        <property name="jedisCmd" ref="publishRedis"/>-->
<!--    </bean>-->

<!--    &lt;!&ndash;okHttp&ndash;&gt;-->
<!--    <bean id="okHttpSupport" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean"-->
<!--          p:configName="fs-feishu-config"/>-->


<!--    <bean id="stoneProxyApi" class="com.facishare.restful.client.FRestApiProxyFactoryBean">-->
<!--        <property name="type" value="com.facishare.stone.sdk.StoneProxyApi"/>-->
<!--    </bean>-->

    <!-- 纷享内部调用组织架构服务 非元数据团队建议使用该配置-->
    <import resource="classpath:spring/fs-organization-api-rest-client.xml"/>

    <bean id="globalConfigService" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">
        <property name="factory" ref="fsiServiceProxyFactory"/>
        <property name="type" value="com.facishare.fsi.proxy.service.GlobalConfigService"/>
    </bean>
    <bean id="fsiWarehouseProxyFactory" class="com.facishare.fsi.proxy.FsiWarehouseProxyFactory" init-method="init">
        <property name="configKey" value="fs-qixin-fsi-proxy"/>
    </bean>
    <bean id="fsiServiceProxyFactory" class="com.facishare.fsi.proxy.FsiServiceProxyFactory" init-method="init">
        <property name="configKey" value="fs-qixin-fsi-proxy"/>
    </bean>

    <bean id="nFileStorageService" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">
        <property name="factory" ref="fsiWarehouseProxyFactory"/>
        <property name="type" value="com.facishare.fsi.proxy.service.NFileStorageService"/>
    </bean>

    <bean id="aFileStorageService" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">
        <property name="factory" ref="fsiWarehouseProxyFactory"/>
        <property name="type" value="com.facishare.fsi.proxy.service.AFileStorageService"/>
    </bean>

<!--    <bean id="globalConfigService" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">-->
<!--        <property name="factory" ref="fsiServiceProxyFactory"/>-->
<!--        <property name="type" value="com.facishare.fsi.proxy.service.GlobalConfigService"/>-->
<!--    </bean>-->
<!--    <bean id="fsiWarehouseProxyFactory" class="com.facishare.fsi.proxy.FsiWarehouseProxyFactory" init-method="init">-->
<!--        <property name="configKey" value="fs-qixin-fsi-proxy"/>-->
<!--        <property name="globalConfigService" ref="globalConfigService"/>-->
<!--    </bean>-->
<!--    <bean id="fsiServiceProxyFactory" class="com.facishare.fsi.proxy.FsiServiceProxyFactory" init-method="init">-->
<!--        <property name="configKey" value="fs-qixin-fsi-proxy"/>-->
<!--    </bean>-->

<!--    <bean id="nFileStorageService" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">-->
<!--        <property name="factory" ref="fsiWarehouseProxyFactory"/>-->
<!--        <property name="type" value="com.facishare.fsi.proxy.service.NFileStorageService"/>-->
<!--    </bean>-->

    <aop:aspectj-autoproxy proxy-target-class="true"/>

    <!-- 蜂眼监控 -->
    <bean id="serviceProfiler" class="com.github.trace.aop.ServiceProfiler"/>
<!--&lt;!&ndash;    日志类&ndash;&gt;-->
<!--    <bean class="com.facishare.open.order.contacts.proxy.api.utils.LogUtils"/>-->
<!--    <bean id="serviceAspect" class="com.facishare.open.order.contacts.proxy.api.aop.ServiceAspect"/>-->


    <!-- 异步线程池 -->
    <bean id="taskExecutor" class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
        <!-- 核心线程数  -->
        <property name="corePoolSize" value="1"/>
        <!-- 最大线程数 -->
        <property name="maxPoolSize" value="32"/>
        <!-- 队列最大长度 >=mainExecutor.maxSize -->
        <property name="queueCapacity" value="1800"/>
        <!-- 线程池维护线程所允许的空闲时间 -->
        <property name="keepAliveSeconds" value="300"/>
        <!--允许核心线程超时销毁-->
        <property name="allowCoreThreadTimeOut" value="true"/>
        <!-- 线程池对拒绝任务(无线程可用)的处理策略 -->
        <property name="rejectedExecutionHandler">
            <bean class="java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy"/>
        </property>
    </bean>

    <!--redisson-->
    <bean id="redissonClient" class="com.fxiaoke.common.redisson.RedissonFactoryBean">
        <property name="configName" value="fs-feishu-config"/>
    </bean>

</beans>