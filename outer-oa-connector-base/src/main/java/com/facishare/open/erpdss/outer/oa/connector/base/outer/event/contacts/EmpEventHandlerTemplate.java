package com.facishare.open.erpdss.outer.oa.connector.base.outer.event.contacts;

import com.facishare.open.erpdss.outer.oa.connector.base.HandlerTemplate;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import lombok.extern.slf4j.Slf4j;

/**
 * 外部员工事件处理器模型
 * <AUTHOR>
 * @date 2024-08-20
 */

@Slf4j
public abstract class EmpEventHandlerTemplate extends HandlerTemplate {
    @Override
    public TemplateResult execute(Object data) {
        MethodContext context = MethodContext.newInstance(data);

        readOuterEmpInfo(context);
        log.info("EmpEventHandlerTemplate.execute,readOuterEmpInfo,context={}",context);
        if(context.isError()) {
            return context.getResult();
        }

        judgeEmpAction(context);
        log.info("EmpEventHandlerTemplate.execute,judgeEmpAction,context={}",context);
        if(context.isError()) {
            return context.getResult();
        }

        writeCrmEmp(context);
        log.info("EmpEventHandlerTemplate.execute,writeCrmEmp,context={}",context);

        return context.getResult();
    }

    /**
     * 读取外部系统员工信息
     * @param context
     */
    public abstract void readOuterEmpInfo(MethodContext context);

    /**
     * 判断员工是新增或更新或停用
     * @param context
     */
    public abstract void judgeEmpAction(MethodContext context);

    /**
     * 新增或更新或停用CRM员工
     * @param context
     */
    public abstract void writeCrmEmp(MethodContext context);
}
