package com.facishare.open.aliyun.market.arg;

import lombok.Data;
import org.joda.time.DateTime;

import java.io.Serializable;

@Data
public class RenewInstanceArg implements Serializable {
    private String action;//固定值：renewInstance
    private String instanceId;//实例 ID
    private String orderId;//云市场订单 ID
    private String expiredOn;//过期时间（yyyy-MM-dd HH:mm:ss）
    private String token;//安全令牌
}
