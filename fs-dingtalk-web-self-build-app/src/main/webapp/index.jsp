<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
       <title>CRM</title>
    <style>
        .weui-loading {
            width: 20px;
            height: 20px;
            display: inline-block;
            vertical-align: middle;
            animation: weuiLoading 1s steps(12, end) infinite;
            background: transparent url("data:image/svg+xml;charset=utf8, %3Csvg xmlns='http://www.w3.org/2000/svg' width='120' height='120' viewBox='0 0 100 100'%3E%3Cpath fill='none' d='M0 0h100v100H0z'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23E9E9E9' rx='5' ry='5' transform='translate(0 -30)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23989697' rx='5' ry='5' transform='rotate(30 105.98 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%239B999A' rx='5' ry='5' transform='rotate(60 75.98 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23A3A1A2' rx='5' ry='5' transform='rotate(90 65 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23ABA9AA' rx='5' ry='5' transform='rotate(120 58.66 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23B2B2B2' rx='5' ry='5' transform='rotate(150 54.02 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23BAB8B9' rx='5' ry='5' transform='rotate(180 50 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23C2C0C1' rx='5' ry='5' transform='rotate(-150 45.98 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23CBCBCB' rx='5' ry='5' transform='rotate(-120 41.34 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23D2D2D2' rx='5' ry='5' transform='rotate(-90 35 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23DADADA' rx='5' ry='5' transform='rotate(-60 24.02 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23E2E2E2' rx='5' ry='5' transform='rotate(-30 -5.98 65)'/%3E%3C/svg%3E") no-repeat;
            background-size: 100%;
        }
        .weui-icon_toast.weui-loading {
            margin:30px 0 0;
            width:38px;
            height:38px;
            vertical-align: baseline;
        }
        @-webkit-keyframes weuiLoading {
            0% {
                transform: rotate3d(0, 0, 1, 0deg);
            }

            100% {
                transform: rotate3d(0, 0, 1, 360deg);
            }
        }

        @keyframes weuiLoading {
            0% {
                transform: rotate3d(0, 0, 1, 0deg);
            }

            100% {
                transform: rotate3d(0, 0, 1, 360deg);
            }
        }
        .weui-toast {
            position: fixed;
            z-index: 5000;
            width: 7.6em;
            min-height: 7.6em;
            top: 180px;
            left: 50%;
            margin-left: -3.8em;
            background: rgba(17,17,17,0.7);
            text-align: center;
            border-radius: 5px;
            color: #FFFFFF;
        }
        .weui-toast__content {
            margin: 0 0 15px;
        }
        .common-class {
            background-size: 100%;
            background-repeat: no-repeat;
            height: 100vh;
            padding-top: 313px;
            text-align: center;
            font-size: 14px;
            color: #16263C;
            line-height: 22px;
            display: none;
            background-size: 375px;
            background-position: top center;
        }
        .error {
            background-image:url('https://a9.fspage.com/FSR/uipaas/dd-error.svg');
        }
        .initialize-error {
            background-image:url('https://a9.fspage.com/FSR/uipaas/dd-error2.svg');
        }
        .user-excess-error {
            background-image:url('https://a9.fspage.com/FSR/uipaas/ddUserExcess.svg');
        }
        .expiration-reminder-error {
            background-image:url('https://a9.fspage.com/FSR/uipaas/ddExpirationReminder.svg');
        }
        .title {
            color: #000;
            font-size: 20px;
        }
        .text {
            color: #67717F;
            font-size: 13px;
        }
        .new-text{
            font-size: 16px;
            color: #545861;
            line-height: 30px;
        }
        .new-text-orange {
            color: #FF8000;
        }
        .expiration-reminder-button {
            width: 351px;
            height: 48px;
            background: #FF8000;
            border-radius: 4px;
            margin: 24px auto;
            font-weight: 400;
            color: #FFFFFF;
            line-height: 48px;
            text-align: center;
        }
    </style>
    <div class="index-wrapper">
        <div class="weui-loading_toast">
            <div class="weui-mask_transparent"></div>
            <div class="weui-toast">
                <i class="weui-loading weui-icon_toast"></i>
                <p class="weui-toast__content">加载中</p>
            </div>
        </div>
        <div class="error common-class">
        </div>
        <div class="user-excess-error common-class">
        </div>
        <div class="expiration-reminder-error common-class">
        </div>
        <!-- Failed initialize -->
        <div class="initialize-error common-class">
            <div class="toast">
                <p class="title">初始化中</p>
                <p class="text">正在进行初始化，可能需要几分钟，请耐心等待</p>
            </div>
        </div>
    </div>
    <script src="https://a9.fspage.com/open/cdn/jquery/2.2.4/jquery.min.js"></script>
    <script src="https://g.alicdn.com/dingding/dingtalk-jsapi/2.10.3/dingtalk.open.js"></script>
    <script src="https://a9.fspage.com/open/cdn/axios.min.js"></script>
    <script>
        var corpId = <%= "\"" + request.getParameter("corpId") + "\"" %>,
            appKey = <%=  "\"" + request.getParameter("appKey") + "\""  %>,
            browserLocation = <%=  "\"" + request.getParameter("browserLocation") + "\""  %>,
            corpSource = <%=  "\"" + request.getParameter("corpSource") + "\""  %>;
        UA = typeof window !== 'undefined' && window.navigator.userAgent.toLowerCase(),
            isAndroid = (UA && UA.indexOf('android') > 0),
            isIOS = (UA && /iphone|iPhone|ipad|ipod|ios/.test(UA)),
            isPC = !isAndroid && !isIOS;
        window.onload = function () {
            dd.ready(function () {
                var url;
                var code;
                dd.runtime.permission.requestAuthCode({
                    corpId: corpId,
                    onSuccess: function (info) {
                        code = info.code;
                        var domain = null;
                        if(corpSource !== null) {
                            if(corpSource === 'fstest') {
                                domain = "https://www.ceshi112.com/dingtalkinner"
                            } else if(corpSource === 'foneshare') {
                                domain = "https://www.fxiaoke.com/dingtalk"
                            } else if(corpSource === 'hwcloud') {
                                domain = "https://hwcloud.fxiaoke.com/dingtalk"
                            } else if(corpSource === 'ale') {
                                domain = "https://ale.fxiaoke.com/dingtalkinner"
                            }
                        }
                        if(domain !== null) {
                            url = domain + "/business/authorizeByApp?corpId=" + corpId + "&code=" + code+"&appKey="+appKey;
                        } else {
                            url = "/dingtalk/business/authorizeByApp?corpId=" + corpId + "&code=" + code+"&appKey="+appKey;
                        }
                        axios.get(url).then(function (response) {
                            $('.weui-loading_toast').remove()
                            if(response.data.redirectUrl!=null) {
                                if(browserLocation!==null&&'default'===browserLocation){
                                    window.open(response.data.redirectUrl);
                                }else{
                                    window.open(response.data.redirectUrl, '_self');
                                }
                            }else{
                                alert("页面出错了，请重新打开页面或者校验账号是否绑定")
                            }
                        }).catch(function (err) { alert(err) })
                    },
                    onFail: function (err) {
                        $('.weui-loading_toast').remove()
                        $('.error').show().text('requestAuthCode error: ' + JSON.stringify(err))
                    }
                });
            });
        }
    </script>
</head>
<body>

</body>
</html>