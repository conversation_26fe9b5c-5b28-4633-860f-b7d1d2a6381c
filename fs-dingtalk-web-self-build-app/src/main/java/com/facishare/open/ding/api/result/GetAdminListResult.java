package com.facishare.open.ding.api.result;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class GetAdminListResult implements Serializable {
    // 请求id
    public String request_id;
    // 返回码
    public Integer errcode;
    // 返回码描述
    public String errmsg;
    // 返回结果
    public List<ListAdminResponse> result;

    @Data
    public static class ListAdminResponse implements Serializable {
        // 管理员的userId
        public String userid;
        // 管理员角色：1、主管理员 2、子管理员
        public Integer sys_level;
    }
}
