package com.facishare.open.ding.provider.utils;

import com.facishare.open.ding.common.model.bizlog.DingAPICallNumer;
import com.facishare.open.ding.provider.model.HttpResponseMessage;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.ps.ProtostuffUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Map;

/**
 * OkHttp3工具类，基于http-spring-support，会上报蜂眼
 * Created by system on 2018/4/16.
 */
@Component
@Slf4j
public class OkHttp3MonitorUtilsFromProvider {

    private static OkHttp3MonitorUtilsFromProvider okHttp3Utils;

    @Resource
    @Qualifier("httpClientSupport")
    private OkHttpSupport httpSupport;

    @PostConstruct
    public void init() {
        okHttp3Utils = this;
    }

    /**
     * 发送GET Request
     * @param url
     * @param headers
     * @param params
     * @return
     */
    public static HttpResponseMessage sendOkHttp3Get(String url,
                                                     Map<String, String> headers,
                                                     Map<String, Object> params) {
        try {
            Request request = UrlUtils.buildGetRequest(params, headers, url);
            return (HttpResponseMessage) okHttp3Utils.httpSupport.syncExecute(request, new OkHttp3SyncCallBack());
        } catch (Exception e) {
            if (headers.containsKey("tenantId") && StringUtils.isNotEmpty(headers.get("tenantId"))) {
                StringWriter sw = new StringWriter();
                PrintWriter pw = new PrintWriter(sw);
                e.printStackTrace(pw);
                String stackTrace = sw.toString();
                DingAPICallNumer dumpLog = DingAPICallNumer.builder().logType("erpOABase-ding-request-API").tenantId(headers.get("tenantId"))
                        .url(url).errorMsg(stackTrace)
                        .build();
                BizLogClient.send("generic-biz-log", ProtostuffUtil.serializer(dumpLog));
            }
            return null;
        }
    }

    /**
     * 发送Form格式的Post Request
     * @param url
     * @param headers
     * @param params
     * @return
     */
    public static HttpResponseMessage sendOkHttp3Post(String url,
                                                      Map<String, String> headers,
                                                      Map<String, Object> params) {
        try {
            Request request = UrlUtils.buildFormPostRequest(params, headers, url);
            return (HttpResponseMessage) okHttp3Utils.httpSupport.syncExecute(request, new OkHttp3SyncCallBack());
        } catch (Exception e) {
            if (headers.containsKey("tenantId") && StringUtils.isNotEmpty(headers.get("tenantId"))) {
                StringWriter sw = new StringWriter();
                PrintWriter pw = new PrintWriter(sw);
                e.printStackTrace(pw);
                String stackTrace = sw.toString();
                DingAPICallNumer dumpLog = DingAPICallNumer.builder().logType("erpOABase-ding-request-API").tenantId(headers.get("tenantId"))
                        .url(url).errorMsg(stackTrace)
                        .build();
                BizLogClient.send("generic-biz-log", ProtostuffUtil.serializer(dumpLog));
            }
            return null;
        }
    }

    /**
     * 发送json格式的Post Request
     * @param url
     * @param headers
     * @param json
     * @return
     */
    public static HttpResponseMessage sendOkHttp3Post(String url, Map<String, String> headers, String json) {
        try {
            Request request = UrlUtils.buildJsonPostRequest(json, headers, url);
            return (HttpResponseMessage) okHttp3Utils.httpSupport.syncExecute(request, new OkHttp3SyncCallBack());
        } catch (Exception e) {
            if (headers.containsKey("tenantId") && StringUtils.isNotEmpty(headers.get("tenantId"))) {
                StringWriter sw = new StringWriter();
                PrintWriter pw = new PrintWriter(sw);
                e.printStackTrace(pw);
                String stackTrace = sw.toString();
                DingAPICallNumer dumpLog = DingAPICallNumer.builder().logType("erpOABase-ding-request-API").tenantId(headers.get("tenantId"))
                        .url(url).errorMsg(stackTrace)
                        .build();
                BizLogClient.send("generic-biz-log", ProtostuffUtil.serializer(dumpLog));
            }
            return null;
        }
    }

    /**
     * 发送json格式的Put Request
     * @param url
     * @param headers
     * @param json
     * @return
     */
    public static HttpResponseMessage sendOkHttp3Put(String url, Map<String, String> headers, String json) {
        try {
            Request request = UrlUtils.buildJsonPutRequest(json, headers, url);
            return (HttpResponseMessage) okHttp3Utils.httpSupport.syncExecute(request, new OkHttp3SyncCallBack());
        } catch (Exception e) {
            if (headers.containsKey("tenantId") && StringUtils.isNotEmpty(headers.get("tenantId"))) {
                StringWriter sw = new StringWriter();
                PrintWriter pw = new PrintWriter(sw);
                e.printStackTrace(pw);
                String stackTrace = sw.toString();
                DingAPICallNumer dumpLog = DingAPICallNumer.builder().logType("erpOABase-ding-request-API").tenantId(headers.get("tenantId"))
                        .url(url).errorMsg(stackTrace)
                        .build();
                BizLogClient.send("generic-biz-log", ProtostuffUtil.serializer(dumpLog));
            }
            return null;
        }
    }

    /**
     * 发送Delete Request
     * @param url
     * @param headers
     * @return
     */
    public static HttpResponseMessage sendOkHttp3Delete(String url, Map<String, String> headers) {
        try {
            Request request = UrlUtils.buildDeleteRequest(headers, url);
            return (HttpResponseMessage) okHttp3Utils.httpSupport.syncExecute(request, new OkHttp3SyncCallBack());
        } catch (Exception e) {
            if (headers.containsKey("tenantId") && StringUtils.isNotEmpty(headers.get("tenantId"))) {
                StringWriter sw = new StringWriter();
                PrintWriter pw = new PrintWriter(sw);
                e.printStackTrace(pw);
                String stackTrace = sw.toString();
                DingAPICallNumer dumpLog = DingAPICallNumer.builder().logType("erpOABase-ding-request-API").tenantId(headers.get("tenantId"))
                        .url(url).errorMsg(stackTrace)
                        .build();
                BizLogClient.send("generic-biz-log", ProtostuffUtil.serializer(dumpLog));
            }
            return null;
        }
    }

}
