package com.facishare.open.ding.provider.model.result;

import com.facishare.open.ding.provider.utils.HttpRequestUtils;
import lombok.Data;

/**
 * KIS云平台返回结果封装类
 * Created by system on 2018/4/28.
 */
@Data
public class KisCloudResult {

    /** 返回码 **/
    private Integer code;

    /** 返回描叙 **/
    private String message;

    /** 网络ID，KIS系统管理登录的产品每个产品实例一个NetID **/
    private String netId;

    /** 业务API签名密钥 **/
    private String appSecret;

    /** 业务接口服务器 **/
    private String serverUrl;

    public boolean isSuccess() {
        if (HttpRequestUtils.KIS_SUCCESS == code) {
            return true;
        }
        return false;
    }

    public boolean isNeedRetry() {
        if (HttpRequestUtils.CONNECTION_FAILED == code || HttpRequestUtils.CONNECTION_TIMEOUT == code) {
            return true;
        }
        return false;
    }

}
