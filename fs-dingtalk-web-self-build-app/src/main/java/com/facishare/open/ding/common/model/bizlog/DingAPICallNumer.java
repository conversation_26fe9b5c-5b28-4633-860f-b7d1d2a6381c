package com.facishare.open.ding.common.model.bizlog;

import io.protostuff.Tag;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DingAPICallNumer {
    @Tag(1)
    @Builder.Default
    private String logType = "erpOABase-ding-custom-API";
    @Tag(2)
    private long stamp;
    @Tag(3)
    private String appName="erpOABase";
    @Tag(7)
    private String tenantId;
    @Tag(10)
    private String objectApiName;
    @Tag(11)
    private String objectId;
    //string1
    @Tag(51)
    private String url;
    @Tag(52)
    private String bizName;
    @Tag(53)
    private String title;
    @Tag(54)
    private String appid;
    @Tag(55)
    private String functionName;

    @Tag(201)
    private long senderId;
    @Tag(202)
    private long crmUserId;
    @Tag(203)
    private String errorMsg;
    @Tag(204)
    private String eventType;
}
