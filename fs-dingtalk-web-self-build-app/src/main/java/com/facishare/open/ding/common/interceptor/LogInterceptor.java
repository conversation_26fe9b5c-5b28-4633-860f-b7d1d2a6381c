package com.facishare.open.ding.common.interceptor;

import com.facishare.open.ding.common.utils.ErasePasswordUtils;
import lombok.extern.slf4j.Slf4j;
import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;
import org.apache.commons.lang3.ObjectUtils;

import java.util.Arrays;

/**
 * <p>类的详细说明</p>
 * @dateTime 2018/8/15 11:26
 * @<NAME_EMAIL>
 * @version 1.0 
 */
@Slf4j
public class LogInterceptor implements MethodInterceptor {

	@Override
	public Object invoke(MethodInvocation invocation) throws Throwable {
		String className = invocation.getMethod().getDeclaringClass().getName();
		String methodName = invocation.getMethod().getName();
//		String args = ErasePasswordUtils.erase(ObjectUtils.isNotEmpty(invocation.getArguments()) ? Arrays.toString(invocation.getArguments()) : null);
		long beforeTime = System.currentTimeMillis();
		try {
			log.info("Service started, serviceName={}#{} args={}.", className, methodName, invocation.getArguments());
			Object result = invocation.proceed();
//			String result2 = ErasePasswordUtils.erase(ObjectUtils.isNotEmpty(result) ? String.valueOf(result) : null);
			long afterTime = System.currentTimeMillis();
			log.info("Service finished, serviceName={}#{} args={} result={} duration={}ms",
					className, methodName, invocation.getArguments(), result, afterTime - beforeTime);
			return result;
		} catch (Throwable e) {
			long afterTime = System.currentTimeMillis();
			log.error("Service exception, serviceName={}#{} args={} duration={}ms exception:",
				className, methodName, invocation.getArguments(), afterTime - beforeTime, e);
			throw e;
		}
	}

}
