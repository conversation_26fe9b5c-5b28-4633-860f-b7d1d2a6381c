package com.facishare.open.ding.provider.model.result.crm;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/**
 * <p>类的详细说明</p>
 * @dateTime 2018/7/27 15:08
 * <AUTHOR> <EMAIL>
 * @version 1.0 
 */
@Data
public class KeyValueListData {

    /** **/
    private List<KeyValue> value;

    @Data
    public static class KeyValue {

        @SerializedName("enumDetailID")
        private String id;

        @SerializedName("itemName")
        private String key;

        @SerializedName("itemCode")
        private String value;

    }

}
