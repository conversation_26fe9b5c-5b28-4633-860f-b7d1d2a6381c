package com.facishare.open.ding.provider.handler;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.facishare.open.ding.provider.config.ConfigCenter;
import com.facishare.open.ding.provider.model.HttpResponseMessage;
import com.facishare.open.ding.provider.utils.OkHttp3MonitorUtilsFromProvider;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 调用钉钉接口处理器，主要处理有限制的钉钉接口，没有调钉钉的接口，可以直接使用http组件进行调用
 * 1、增加钉钉限流重试
 */
@Component
@Slf4j
public class DingRetryHandlerFromProvider {

    /**
     * 发送GET Request
     * @param url
     * @param headers
     * @param params
     * @return
     */
    public static HttpResponseMessage sendOkHttp3Get(String url,
                                 Map<String, String> headers,
                                 Map<String, Object> params) {
        HttpResponseMessage messageResult = null;
        for (int i = 0; i < 3; i++) {
            messageResult = OkHttp3MonitorUtilsFromProvider.sendOkHttp3Get(url, headers, params);
            try {
                if(ObjectUtils.isNotEmpty(messageResult)
                        && StringUtils.isNotEmpty(messageResult.getContent()) && JSON.isValidObject(messageResult.getContent())) {
                    JSONObject jsonObject = JSONObject.parseObject(messageResult.getContent());
                    String code = String.valueOf(jsonObject.get("code"));
                    String errcode = String.valueOf(jsonObject.get("errcode"));
                    if(StringUtils.isNotEmpty(code) && ConfigCenter.QPS_LIMIT_CODE.contains(code) ||
                            StringUtils.isNotEmpty(errcode) && ConfigCenter.QPS_LIMIT_CODE.contains(errcode)) {
                        try {
                            long sleepMillis = 1000L - System.currentTimeMillis() % 1000L;
                            log.info("DingRetryHandler.sendOkHttp3Get,api limit,重试次数：{},{}ms后重试", i + 1, sleepMillis);
                            System.out.println(sleepMillis);
                            Thread.sleep(sleepMillis);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                        continue;
                    }
                }
            } catch (Exception e) {
                log.warn("DingRetryHandler.sendOkHttp3Get,json parse error", e);
            }
            break;
        }
        return messageResult;
    }

    /**
     * 发送Form格式的Post Request
     * @param url
     * @param headers
     * @param params
     * @return
     */
    public static HttpResponseMessage sendOkHttp3Post(String url,
                                                      Map<String, String> headers,
                                                      Map<String, Object> params) {
        HttpResponseMessage messageResult = null;
        for (int i = 0; i < 3; i++) {
            messageResult = OkHttp3MonitorUtilsFromProvider.sendOkHttp3Post(url, headers, params);
            try {
                if(ObjectUtils.isNotEmpty(messageResult)
                        && StringUtils.isNotEmpty(messageResult.getContent()) && JSON.isValidObject(messageResult.getContent())) {
                    JSONObject jsonObject = JSONObject.parseObject(messageResult.getContent());
                    String code = String.valueOf(jsonObject.get("code"));
                    String errcode = String.valueOf(jsonObject.get("errcode"));
                    if(StringUtils.isNotEmpty(code) && ConfigCenter.QPS_LIMIT_CODE.contains(code) ||
                            StringUtils.isNotEmpty(errcode) && ConfigCenter.QPS_LIMIT_CODE.contains(errcode)) {
                        try {
                            long sleepMillis = 1000L - System.currentTimeMillis() % 1000L;
                            log.info("DingRetryHandler.sendOkHttp3Get,api limit,重试次数：{},{}ms后重试", i + 1, sleepMillis);
                            System.out.println(sleepMillis);
                            Thread.sleep(sleepMillis);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                        continue;
                    }
                }
            } catch (Exception e) {
                log.warn("DingRetryHandler.sendOkHttp3Get,json parse error", e);
            }
            break;
        }
        return messageResult;
    }

    /**
     * 发送json格式的Post Request
     * @param url
     * @param headers
     * @param json
     * @return
     */
    public static HttpResponseMessage sendOkHttp3Post(String url, Map<String, String> headers, String json) {
        HttpResponseMessage messageResult = null;
        for (int i = 0; i < 3; i++) {
            messageResult = OkHttp3MonitorUtilsFromProvider.sendOkHttp3Post(url, headers, json);
            try {
                if(ObjectUtils.isNotEmpty(messageResult)
                        && StringUtils.isNotEmpty(messageResult.getContent()) && JSON.isValidObject(messageResult.getContent())) {
                    JSONObject jsonObject = JSONObject.parseObject(messageResult.getContent());
                    String code = String.valueOf(jsonObject.get("code"));
                    String errcode = String.valueOf(jsonObject.get("errcode"));
                    if(StringUtils.isNotEmpty(code) && ConfigCenter.QPS_LIMIT_CODE.contains(code) ||
                            StringUtils.isNotEmpty(errcode) && ConfigCenter.QPS_LIMIT_CODE.contains(errcode)) {
                        try {
                            long sleepMillis = 1000L - System.currentTimeMillis() % 1000L;
                            log.info("DingRetryHandler.sendOkHttp3Get,api limit,重试次数：{},{}ms后重试", i + 1, sleepMillis);
                            System.out.println(sleepMillis);
                            Thread.sleep(sleepMillis);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                        continue;
                    }
                }
            } catch (Exception e) {
                log.warn("DingRetryHandler.sendOkHttp3Post,json error,json={}", json, e);
            }
            break;
        }
        return messageResult;
    }

    /**
     * 发送json格式的Put Request
     * @param url
     * @param headers
     * @param json
     * @return
     */
    public static HttpResponseMessage sendOkHttp3Put(String url, Map<String, String> headers, String json) {
        HttpResponseMessage messageResult = null;
        for (int i = 0; i < 3; i++) {
            messageResult = OkHttp3MonitorUtilsFromProvider.sendOkHttp3Put(url, headers, json);
            try {
                if(ObjectUtils.isNotEmpty(messageResult)
                        && StringUtils.isNotEmpty(messageResult.getContent()) && JSON.isValidObject(messageResult.getContent())) {
                    JSONObject jsonObject = JSONObject.parseObject(messageResult.getContent());
                    String code = String.valueOf(jsonObject.get("code"));
                    String errcode = String.valueOf(jsonObject.get("errcode"));
                    if(StringUtils.isNotEmpty(code) && ConfigCenter.QPS_LIMIT_CODE.contains(code) ||
                            StringUtils.isNotEmpty(errcode) && ConfigCenter.QPS_LIMIT_CODE.contains(errcode)) {
                        try {
                            long sleepMillis = 1000L - System.currentTimeMillis() % 1000L;
                            log.info("DingRetryHandler.sendOkHttp3Get,api limit,重试次数：{},{}ms后重试", i + 1, sleepMillis);
                            System.out.println(sleepMillis);
                            Thread.sleep(sleepMillis);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                        continue;
                    }
                }
            } catch (Exception e) {
                log.warn("DingRetryHandler.sendOkHttp3Get,json parse error", e);
            }
            break;
        }
        return messageResult;
    }

    /**
     * 发送Delete Request
     * @param url
     * @param headers
     * @return
     */
    public static HttpResponseMessage sendOkHttp3Delete(String url, Map<String, String> headers) {
        HttpResponseMessage messageResult = null;
        for (int i = 0; i < 3; i++) {
            messageResult = OkHttp3MonitorUtilsFromProvider.sendOkHttp3Delete(url, headers);
            try {
                if(ObjectUtils.isNotEmpty(messageResult)
                        && StringUtils.isNotEmpty(messageResult.getContent()) && JSON.isValidObject(messageResult.getContent())) {
                    JSONObject jsonObject = JSONObject.parseObject(messageResult.getContent());
                    String code = String.valueOf(jsonObject.get("code"));
                    String errcode = String.valueOf(jsonObject.get("errcode"));
                    if(StringUtils.isNotEmpty(code) && ConfigCenter.QPS_LIMIT_CODE.contains(code) ||
                            StringUtils.isNotEmpty(errcode) && ConfigCenter.QPS_LIMIT_CODE.contains(errcode)) {
                        try {
                            long sleepMillis = 1000L - System.currentTimeMillis() % 1000L;
                            log.info("DingRetryHandler.sendOkHttp3Get,api limit,重试次数：{},{}ms后重试", i + 1, sleepMillis);
                            System.out.println(sleepMillis);
                            Thread.sleep(sleepMillis);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                        continue;
                    }
                }
            } catch (Exception e) {
                log.warn("DingRetryHandler.sendOkHttp3Get,json parse error", e);
            }
            break;
        }
        return messageResult;
    }
}
