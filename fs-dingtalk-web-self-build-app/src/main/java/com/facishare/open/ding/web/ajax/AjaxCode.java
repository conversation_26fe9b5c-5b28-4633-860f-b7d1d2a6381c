package com.facishare.open.ding.web.ajax;

/**
 * <p>返回页面的请求错误信息，一般 非0表示有异常.</p>
 * <p>类的详细说明</p>
 * @dateTime 2018/7/17 15:32
 * <AUTHOR> yin<PERSON>@fxiaoke.com
 * @version 1.0 
 */
public interface AjaxCode {

    /**
     * 请求处理成功.
     */
    int OK = 0;

    /**
     * 系统异常
     */
    int SYSTEM_ERROR = -1;

    /**
     * 服务异常
     */
    int SERVER_ERROR = -2;
    /**
     * 用户未登录.
     */
    int USER_NOT_LOGIN = 401;

    /*
     * 权限不足,403 表示登录有问题,或者非网站管理员.
     */
    int NO_AUTHORITY = 403;

    /**
     * 已删除
     */
    int DELETED = 407;

    /*
     * 非法用户，用户身份不正确
     */
    int USER_ILLEGAL = 408;

    /*
     * 权限不足,409 表示不是该企业员工.
     */
    int NOT_IN_ENTERPRISE = 409;

    /**
     * 参数校验错误,400 一般表示参数有问题.
     */
    int PARAM_ERROR = 400;

    /**
     * 业务异常.
     */
    int BIZ_EXCEPTION = 500;

    /**
     * 无法识别的异常.
     */
    int UNKNOWN_EXCEPTION = 550;
}
