package com.facishare.open.ding.config;

import com.alicp.jetcache.CacheBuilder;
import com.alicp.jetcache.anno.CacheConsts;
import com.alicp.jetcache.anno.config.EnableMethodCache;
import com.alicp.jetcache.anno.support.GlobalCacheConfig;
import com.alicp.jetcache.anno.support.JetCacheBaseBeans;
import com.alicp.jetcache.embedded.EmbeddedCacheBuilder;
import com.alicp.jetcache.embedded.LinkedHashMapCacheBuilder;
import com.alicp.jetcache.external.ExternalCacheBuilder;
import com.alicp.jetcache.support.FastjsonKeyConvertor;
import com.alicp.jetcache.support.Kryo5ValueDecoder;
import com.alicp.jetcache.support.Kryo5ValueEncoder;
import com.facishare.open.outer.oa.connector.common.api.jetcache.CustomCacheMonitor;
import com.facishare.open.outer.oa.connector.common.api.jetcache.remoteno.RemoteNoCacheBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * JetCache配置类
 * 启用JetCache的注解支持
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@Configuration
@EnableMethodCache(basePackages = {"com.facishare.open.ding"}, order = 0)
@Import(JetCacheBaseBeans.class) //need since jetcache 2.7+
public class JetCacheConfig {

    private static final String remoteCacheRoom = "erpdssDingtalkJetCacheNotifyRoom";

    @Autowired(required = false)
    private List<CustomCacheMonitor> customCacheMonitors;

    @Bean
    public GlobalCacheConfig config() {
        Map<String, CacheBuilder> localBuilders = new HashMap<>();
        EmbeddedCacheBuilder<LinkedHashMapCacheBuilder.LinkedHashMapCacheBuilderImpl> localBuilder = LinkedHashMapCacheBuilder
                .createLinkedHashMapCacheBuilder()
                .keyConvertor(FastjsonKeyConvertor.INSTANCE);
        localBuilders.put(CacheConsts.DEFAULT_AREA, localBuilder);

        Map<String, CacheBuilder> remoteBuilders = new HashMap<>();

        ExternalCacheBuilder<?> externalCacheBuilder = RemoteNoCacheBuilder.createBuilder();;

        externalCacheBuilder
                .broadcastChannel(remoteCacheRoom)
                .keyPrefix("erpdssDingtalk:cache:")
                //默认十分钟超时
                .expireAfterWrite(10L, TimeUnit.MINUTES)
                //这个性能好
                .valueEncoder(Kryo5ValueEncoder.INSTANCE)
                .valueDecoder(Kryo5ValueDecoder.INSTANCE)
                .keyConvertor(FastjsonKeyConvertor.INSTANCE);
        remoteBuilders.put(CacheConsts.DEFAULT_AREA, externalCacheBuilder);
        if (customCacheMonitors != null && !customCacheMonitors.isEmpty()) {
            //注入自定义监视器！
            for (CustomCacheMonitor customCacheMonitor : customCacheMonitors) {
                localBuilder.addMonitor(customCacheMonitor);
                externalCacheBuilder.addMonitor(customCacheMonitor);
            }
        }
        GlobalCacheConfig globalCacheConfig = new GlobalCacheConfig();
        globalCacheConfig.setLocalCacheBuilders(localBuilders);
        globalCacheConfig.setRemoteCacheBuilders(remoteBuilders);
        globalCacheConfig.setStatIntervalMinutes(15);
        return globalCacheConfig;
    }
}
