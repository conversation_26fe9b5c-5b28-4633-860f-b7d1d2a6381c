package com.facishare.open.ding.common.utils;

import org.apache.commons.lang3.StringUtils;

public class ConvertUrlUtils {

    public static String convertDingUrl(String url, String nginxIp) {
        if(StringUtils.isEmpty(nginxIp) || StringUtils.isEmpty(url)) {
            return url;
        }

        //把钉钉接口转换成nginxIp地址
        String finalUrl = url
                .replace("https://oapi.dingtalk.com", nginxIp + "/oapi")
                .replace("https://api.dingtalk.com", nginxIp + "/api");

        return finalUrl;
    }
}
