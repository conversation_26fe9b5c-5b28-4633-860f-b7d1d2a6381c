package com.facishare.open.ding.provider.model.result;

import lombok.Builder;
import lombok.Data;

/**
 * 数据同步结果类,用于同步订单时顺带同步了客户的情况，保存客户同步结果数据
 * Created by system on 2018/6/6.
 */
@Data
@Builder
public class SyncAccountResult {

    /** 1表示同步完毕 **/
    public static final int SUCCESS = 1;

    /** 0表示无数据,不需要同步 **/
    public static final int NO_NEED = 0;

    /** -1表示同步中止 **/
    public static final int FAILURE = -1;

    /** 状态码 **/
    private Integer code;

    /** 错误提示 **/
    private String message;

    /** 同步成功数 **/
    private Integer success;

    /** 同步失败数 **/
    private Integer failure;

    public boolean isFailed() {
        if (code == FAILURE) {
            return true;
        } else if (code == SUCCESS && failure != null && failure > 0) {
            return true;
        }
        return false;
    }

}
