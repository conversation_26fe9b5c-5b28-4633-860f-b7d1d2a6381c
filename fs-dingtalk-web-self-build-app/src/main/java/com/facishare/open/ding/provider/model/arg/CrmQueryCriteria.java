package com.facishare.open.ding.provider.model.arg;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by system on 2018/5/9.
 */
@Data
public class CrmQueryCriteria {

    /** 页面大小, 预设对象最大值为1000，自定义对象最大值为100 */
    private int limit = 100;

    /** 偏移量 */
    private int offset = 0;

    /** 投影字段(即需要返回的字段) */
    private DataProjection dataProjection;

    /** 条件列表 */
    private List<Condition> conditions;

    /** 范围条件列表，查询为闭区间查询 */
    private List<RangeCondition> rangeConditions;

    /** 排序列表 */
    private List<Order> orders;

    @Data
    public static class DataProjection {

        /** 包含字段列表 */
        private List<String> fieldNames;

    }

    @Data
    public static class Condition {

        /** 条件说明 */
        private Map<String, Object> conditions;

        /** 条件类型，仅支持term_condition 精确匹配 */
        private String conditionType = "term_condition";

    }

    @Data
    public static class RangeCondition {

        /** 字段名称 */
        private String fieldName;

        /** 开始值 */
        private Number from;

        /** 最大值 */
        private Number to;

    }

    @Data
    public static class Order {

        /** true:升序  false:降序 */
        private boolean ascending;

        /** 排序字段名 */
        private String field;

    }

}
