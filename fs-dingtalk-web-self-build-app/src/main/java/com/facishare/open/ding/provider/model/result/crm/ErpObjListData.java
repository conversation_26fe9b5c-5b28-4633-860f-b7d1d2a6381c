package com.facishare.open.ding.provider.model.result.crm;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <p>类的详细说明</p>
 *
 * @version 1.0
 * @dateTime 2018-07-30 11:27
 * @<NAME_EMAIL>
 */
@Data
public class ErpObjListData {

    /** 记录总数 **/
    private Integer total;

    private Integer offset;

    private Integer limit;

    /** 数据列表 **/
    private List<Map<String, Object>> dataList = Lists.newArrayList();

}
