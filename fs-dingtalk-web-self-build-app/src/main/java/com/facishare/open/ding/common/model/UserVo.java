package com.facishare.open.ding.common.model;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/8/3 10:05
 * @Version 1.0
 */
@Data
public class UserVo implements Serializable {
    private String unionid;
    private String userid;
    private String name;
    private String mobile;
    private String email;
    private String position;
    private String jobnumber;
    private List<Long> department;
    private Long mainDepartment;
    private String managerUserid;
    private Integer sexType;
    private String stateCode;
    /**
     * 入职时间
     * 信息面板中入职时间字段内有值才返回。
     * 第三方企业应用，不返回该参数。
     */
    private Long hiredDate;
}
