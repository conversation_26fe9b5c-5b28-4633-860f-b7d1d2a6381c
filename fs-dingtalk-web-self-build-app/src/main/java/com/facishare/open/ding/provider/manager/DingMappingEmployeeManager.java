package com.facishare.open.ding.provider.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.ding.api.enums.EmpStatusEnum;
import com.facishare.open.ding.api.model.FsUserInfoModel;
import com.facishare.open.ding.api.result.BindFxUserResult;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.vo.DeptVo;
import com.facishare.open.ding.common.model.User;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.provider.entity.DingEnterprise;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeDataEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeDataManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaEmployeeBindMapper;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaEmployeeDataMapper;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.object.DingTalkEmployeeObject;
import com.fxiaoke.api.IdGenerator;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018/7/16 10:48
 */
@Slf4j
@Component
public class DingMappingEmployeeManager {

    @Autowired
    private DingDeptMananger dingDeptMananger;

    @Autowired
    private DingEnterpriseManager dingEnterpriseManager;

    @Autowired
    private OuterOaEmployeeDataManager outerOaEmployeeDataManager;

    @Autowired
    private OuterOaEmployeeDataMapper outerOaEmployeeDataMapper;

    @Autowired
    private OuterOaEmployeeBindMapper outerOaEmployeeBindMapper;

    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;

    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;

    @Autowired
    private EIEAConverter eieaConverter;

    /**
     * 将JSONObject转换为EmployeeMongoData
     *
     * @param jsonObject JSONObject对象
     * @return EmployeeMongoData对象
     */
    private DingTalkEmployeeObject convertToEmployeeMongoData(JSONObject jsonObject) {
        if (jsonObject == null) {
            return null;
        }
        return jsonObject.toJavaObject(DingTalkEmployeeObject.class);
    }

    /**
     * 将EmployeeMongoData转换为JSONObject
     *
     * @param employeeData EmployeeMongoData对象
     * @return JSONObject对象
     */
    private JSONObject convertToJsonObject(DingTalkEmployeeObject employeeData) {
        if (employeeData == null) {
            return null;
        }
        return JSONObject.parseObject(JSON.toJSONString(employeeData));
    }

    /**
     * 通过ei查询ding_mapping_employee中的数据
     *
     * @param ei 企业ID
     * @param appId 应用ID
     * @param bindStatus 绑定状态
     * @param pageNumber 页码
     * @param pageSize 每页数量
     * @param dingName 钉钉名称
     * @param dingPhone 钉钉手机号
     * @param dingDeptId 钉钉部门ID
     * @return 查询结果
     */
    public Result<List<DingMappingEmployeeResult>> queryEmpsBindByEi(Integer ei, String appId, Integer bindStatus, Integer pageNumber, Integer pageSize,
                                                                     String dingName, String dingPhone, Long dingDeptId) {
        if (ei == null || StringUtils.isEmpty(appId)) {
            log.warn("查询员工绑定关系参数错误: ei=[{}], appId=[{}]", ei, appId);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        final String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        if (StringUtils.isEmpty(outEa)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        // 使用SQL联表查询，一次性获取所有数据
        Integer offset = pageNumber != null && pageSize != null ? (pageNumber - 1) * pageSize : null;

        // 查询条件
        String nameOrPhone = null;
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(dingName)) {
            nameOrPhone = dingName;
        } else if (org.apache.commons.lang3.StringUtils.isNotEmpty(dingPhone)) {
            nameOrPhone = dingPhone;
        }

        // 查询数据
        final BindStatusEnum bindStatusEnum = bindStatus != null ? BindStatusEnum.convertDingtalkEmployeeBindStatus(bindStatus) : null;
        List<Map<String, Object>> results = outerOaEmployeeBindMapper.queryDingEmployeeBindWithFilters(
                ChannelEnum.dingding.name(),
                bindStatusEnum,
                outEa,
                fsEa,
                appId,
                nameOrPhone,
                Objects.nonNull(dingDeptId) ? Lists.newArrayList(String.valueOf(dingDeptId)) : null,
                pageSize,
                offset
        );

        if (CollectionUtils.isEmpty(results)) {
            return Result.newSuccess(new ArrayList<>());
        }

        // 转换为Result对象
        List<DingMappingEmployeeResult> employeeResults = results.stream()
                .map(result -> convertMapToResult(result, fsEa))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return Result.newSuccess(employeeResults);
    }

    private DingMappingEmployeeResult convertToResult(OuterOaEmployeeBindEntity bindEntity) {
        if (bindEntity == null) {
            return null;
        }

        // 查询员工数据
        OuterOaEmployeeDataEntity dataEntity = getEmployeeDataByOutUserId(
                bindEntity.getOutEa(),
                bindEntity.getAppId(),
                bindEntity.getOutEmpId());

        // 转换为Result对象
        return convertToResult(bindEntity, convertToEmployeeMongoData(dataEntity.getOutUserInfo()));
    }

    /**
     * 将PG数据转换为Result对象
     */
    private DingMappingEmployeeResult convertToResult(OuterOaEmployeeBindEntity bindEntity, DingTalkEmployeeObject employeeData) {
        if (bindEntity == null) {
            return null;
        }

        DingMappingEmployeeResult result = new DingMappingEmployeeResult();
        final Integer ei = StringUtils.isEmpty(bindEntity.getFsEa()) ? null : eieaConverter.enterpriseAccountToId(bindEntity.getFsEa());
        result.setEi(ei);
        result.setEmployeeId(Integer.valueOf(bindEntity.getFsEmpId()));
        result.setDingEmployeeId(bindEntity.getOutEmpId());
        result.setBindStatus(convertBindStatus(bindEntity.getBindStatus()));
        result.setCreateTime(new Date(bindEntity.getCreateTime()));
        result.setUpdateTime(new Date(bindEntity.getUpdateTime()));

        if (Objects.nonNull(employeeData)) {
            result.setDingEmployeeId(employeeData.getUserid());
            result.setDingEmployeeName(employeeData.getName());
            result.setDingEmployeePhone(employeeData.getPhone());
            result.setDingEmployeeEmail(employeeData.getEmail());
            result.setDingEmployeePosition(employeeData.getPosition());
            result.setManagerUserid(employeeData.getManager_userid());
            result.setDingJobNumber(employeeData.getJobNumber());
            result.setDingDeptId(employeeData.getDeptId());
            result.setDingDeptName(employeeData.getDeptName());
            result.setDingEmployeeStatus(employeeData.getStatus());
            result.setDingUnionId(employeeData.getUnionId());
            result.setDingSexType(employeeData.getSexType() != null ?
                    Integer.valueOf(employeeData.getSexType()) : null);
        }

        return result;
    }

    /**
     * 将联表查询结果Map转换为DingMappingEmployeeResult对象
     */
    private DingMappingEmployeeResult convertMapToResult(Map<String, Object> result, String fsEa) {
        if (result == null) {
            return null;
        }

        DingMappingEmployeeResult dingResult = new DingMappingEmployeeResult();
        // 设置基本信息
        dingResult.setEi(eieaConverter.enterpriseAccountToId(fsEa));

        // 设置员工ID
        String fsEmpId = (String) result.get("fs_emp_id");
        if (!StringUtils.isEmpty(fsEmpId)) {
            dingResult.setEmployeeId(Integer.valueOf(fsEmpId));
        }
        
        // 设置绑定状态
        String bindStatusStr = (String) result.get("bind_status");
        if (!StringUtils.isEmpty(bindStatusStr)) {
            dingResult.setBindStatus(convertBindStatus(BindStatusEnum.valueOf(bindStatusStr)));
        }
        
        // 设置时间
        Long createTime = (Long) result.get("create_time");
        if (createTime != null) {
            dingResult.setCreateTime(new Date(createTime));
        }
        
        Long updateTime = (Long) result.get("update_time");
        if (updateTime != null) {
            dingResult.setUpdateTime(new Date(updateTime));
        }

        // 解析员工信息JSON
        String outUserInfoJson = (String) result.get("out_user_info");
        if (!StringUtils.isEmpty(outUserInfoJson)) {
            JSONObject outUserInfo = JSONObject.parseObject(outUserInfoJson);
            if (outUserInfo != null) {
                DingTalkEmployeeObject employeeData = convertToEmployeeMongoData(outUserInfo);
                if (employeeData != null) {
                    dingResult.setDingEmployeeId(employeeData.getUserid());
                    dingResult.setDingEmployeeName(employeeData.getName());
                    dingResult.setDingEmployeePhone(employeeData.getPhone());
                    dingResult.setDingEmployeeEmail(employeeData.getEmail());
                    dingResult.setDingEmployeePosition(employeeData.getPosition());
                    dingResult.setDingJobNumber(employeeData.getJobNumber());
                    dingResult.setDingDeptId(employeeData.getDeptId());
                    dingResult.setDingDeptName(employeeData.getDeptName());
                    dingResult.setDingEmployeeStatus(employeeData.getStatus());
                    dingResult.setDingUnionId(employeeData.getUnionId());
                    dingResult.setDingSexType(employeeData.getSexType() != null ? 
                            Integer.valueOf(employeeData.getSexType()) : null);
                    dingResult.setManagerUserid(employeeData.getManager_userid());
                }
            }
        } else {
            // 如果没有用户信息JSON，则直接从result中获取钉钉员工ID
            String outEmpId = (String) result.get("out_emp_id");
            if (!StringUtils.isEmpty(outEmpId)) {
                dingResult.setDingEmployeeId(outEmpId);
            }
        }
        
        return dingResult;
    }

    /**
     * 转换绑定状态（从BindStatusEnum到Integer）
     */
    private Integer convertBindStatus(BindStatusEnum bindStatus) {
        if (bindStatus == null) {
            return EmpStatusEnum.INIT.getStatus();
        }
        switch (bindStatus) {
            case create:
                return EmpStatusEnum.INIT.getStatus();
            case normal:
                return EmpStatusEnum.BIND.getStatus();
            default:
                return EmpStatusEnum.NOTBIND.getStatus();
        }
    }

    /**
     * 条件查询员工
     */
    public Result<List<DingMappingEmployeeResult>> conditionQueryEmployee(Integer ei, String appId, Integer bindStatus, Integer pageNumber, Integer pageSize, String dingNameOrPhone, Long dingDeptId) {
        if (ei == null || StringUtils.isEmpty(appId)) {
            log.warn("conditionQueryEmployee param illegal, ei=[{}], appId=[{}].", ei, appId);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        final String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        if (StringUtils.isEmpty(outEa)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        Integer offset = null;
        if (Objects.nonNull(pageNumber) && Objects.nonNull(pageSize)) {
            offset = (pageNumber - 1) * pageSize;
        }
        //递归查询子部门
        List<String> deptIds = null;
        if (dingDeptId != null) {
            // 递归获取部门子部门
            List<DeptVo> depts = dingDeptMananger.getDeptByEI(ei, appId);
            if (CollectionUtils.isEmpty(depts)) {
                return Result.newSuccess(new ArrayList<>());
            }
            List<Long> dingParentIds = Lists.newArrayList(dingDeptId);
            List<Long> longDeptIds = recallChildDepts(depts, dingParentIds, new ArrayList<>());
            longDeptIds.add(dingDeptId);
            deptIds = longDeptIds.stream().map(String::valueOf).collect(Collectors.toList());
        }

        // 绑定状态
        final BindStatusEnum bindStatusEnum = bindStatus != null ? BindStatusEnum.convertDingtalkEmployeeBindStatus(bindStatus) : null;

        // 使用联表查询
        List<Map<String, Object>> results = outerOaEmployeeBindMapper.queryDingEmployeeBindWithFilters(
                ChannelEnum.dingding.name(),
                bindStatusEnum,
                outEa,
                fsEa,
                appId,
                dingNameOrPhone,
                deptIds,
                pageSize,
                offset
        );

        if (CollectionUtils.isEmpty(results)) {
            return Result.newSuccess(new ArrayList<>());
        }

        // 转换为Result对象
        List<DingMappingEmployeeResult> employeeResults = results.stream()
                .map(result -> convertMapToResult(result, fsEa))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return Result.newSuccess(employeeResults);
    }

    /**
     * 条件查询总数
     */
    public Integer conditionEmployeeCount(Integer ei, String appId, Integer bindStatus, String dingNameOrPhone, Long dingDeptId) {
        if (ei == null || StringUtils.isEmpty(appId)) {
            log.warn("conditionEmployeeCount param illegal, ei=[{}], appId=[{}].", ei, appId);
            return 0;
        }

        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        final String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        if (StringUtils.isEmpty(outEa)) {
            return 0;
        }

        // 部门ID列表
        List<String> deptIds = null;
        if (dingDeptId != null) {
            dingDeptId = Optional.ofNullable(dingDeptId).orElse(1L);
            List<DeptVo> depts = dingDeptMananger.getDeptByEI(ei, appId);
            if (CollectionUtils.isEmpty(depts)) {
                return 0;
            }
            List<Long> dingParentIds = Lists.newArrayList(dingDeptId);
            List<Long> longDeptIds = recallChildDepts(depts, dingParentIds, new ArrayList<>());
            longDeptIds.add(dingDeptId);
            deptIds = longDeptIds.stream().map(String::valueOf).collect(Collectors.toList());
        }

        // 查询总数
        return outerOaEmployeeBindMapper.countDingEmployeeBindWithFilters(
                ChannelEnum.dingding.name(),
                outEa,
                fsEa,
                appId,
                dingNameOrPhone,
                deptIds,
                Objects.isNull(bindStatus) ? null : BindStatusEnum.convertDingtalkEmployeeBindStatus(bindStatus));
    }

    private List<Long> recallChildDepts(List<DeptVo> dingDeptEntities, List<Long> dingParentIds, List<Long> ids) {
        if (!CollectionUtils.isEmpty(dingParentIds)) {
            for (int i = 0; i < dingParentIds.size(); i++) {
                Long id = dingParentIds.get(i);
                List<Long> deptS = dingDeptEntities.stream().filter(item -> id.equals(item.getDingParentId())).map(DeptVo::getDingDeptId).collect(Collectors.toList());
                List<Long> dingPars = Lists.newArrayList();
                dingPars.addAll(deptS);
                ids.addAll(deptS);
                recallChildDepts(dingDeptEntities, deptS, ids);
            }
        }
        return ids;
    }

    /**
     * public List<Integer> treeDeptIds(HashMap<Long, DingDeptEntity> deptMaps, Long dingDeptId, List<Integer> ids) {
     * //        List<Long> ids = new ArrayList<>();
     * if (deptMaps.get(dingDeptId) != null) {
     * Long crmDeptId = deptMaps.get(dingDeptId).getCrmDeptId();
     * Long dingDeptID = deptMaps.get(dingDeptId).getDingParentId();
     * ids.add(crmDeptId.intValue());
     * treeDeptIds(deptMaps, dingDeptID, ids);
     * }
     * return ids;
     * }
     *
     * @param ei
     * @param bindStatus
     * @param dingName
     * @param dingPhone
     * @param appId
     * @return
     */

    public Integer getEmpTotalCount(Integer ei, Integer bindStatus, String dingName, String dingPhone, String appId) {
        if (ei == null) {
            log.warn("getEmpTotalCount参数错误, ei=[{}]", ei);
            return 0;
        }

        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        final String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);

        String nameOrPhone = null;
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(dingName)) {
            nameOrPhone = dingName;
        } else if (org.apache.commons.lang3.StringUtils.isNotEmpty(dingPhone)) {
            nameOrPhone = dingPhone;
        }

        return outerOaEmployeeBindMapper.countDingEmployeeBindWithFilters(
                ChannelEnum.dingding.name(),
                outEa,
                fsEa,
                appId,
                nameOrPhone,
                null,
                Objects.isNull(bindStatus) ? null : BindStatusEnum.convertDingtalkEmployeeBindStatus(bindStatus)
        );
    }

    public Integer getEmpAllCount(Integer ei, String appId) {
        if (ei == null) {
            log.warn("getEmpAllCount参数错误, ei=[{}]", ei);
            return 0;
        }

        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        final String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        return outerOaEmployeeDataMapper.count(ChannelEnum.dingding.name(), outEa, appId);
    }

    @Transactional
    public Integer initMappingEmployee(List<User> list, Integer ei, Long dingDeptId, String dingDeptName, String appId) {
        if (CollectionUtils.isEmpty(list)) {
            log.warn("initMappingEmployee参数列表为空");
            return 0;
        }
        // 创建一个列表来收集所有要更新的员工数据实体
        List<DingTalkEmployeeObject> dataEntities = new ArrayList<>();

        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        final String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        final String dcId = outerOaEnterpriseBindManager.getDcIdByEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        if (StringUtils.isEmpty(outEa)) {
            return 0;
        }

        for (User user : list) {
            // 创建员工数据
            DingTalkEmployeeObject employeeData = new DingTalkEmployeeObject();
            employeeData.setStatus(0);
            employeeData.setUnionId(user.getUnionid());
            employeeData.setPosition(user.getPosition());
            employeeData.setJobNumber(user.getJobnumber());
            employeeData.setManager_userid(user.getManagerUserid());
            employeeData.setEmail(user.getEmail());
            employeeData.setSexType(String.valueOf(user.getSexType()));
            employeeData.setUserid(user.getUserid());
            employeeData.setName(user.getName());
            employeeData.setPhone(user.getMobile());
            employeeData.setDeptId(dingDeptId);
            employeeData.setDeptName(dingDeptName);

            // 添加到集合而不是直接插入
            dataEntities.add(employeeData);
        }

        // 批量更新或插入员工数据
        return outerOaEmployeeDataManager.batchUpsert(dataEntities, ChannelEnum.dingding, dcId);
    }

    @Transactional
    public Integer insertModeEmployee(List<User> userList, Integer ei, Long dingDeptId, Integer userId, String dingDeptName, Integer crmDeptId, String appId) {
        if (CollectionUtils.isEmpty(userList)) {
            log.warn("initMappingEmployee param list is null.");
            return 0;
        }
        return initMappingEmployee(userList, ei, dingDeptId, dingDeptName, appId);
    }

    //全量数据插入
    @Transactional
    public Integer insertAllModelData(List<DingMappingEmployeeResult> list, Integer ei, String appId) {
        if (CollectionUtils.isEmpty(list)) {
            log.warn("insertAllModelData param list is null.");
            return 0;
        }

        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        final String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        final String dcId = outerOaEnterpriseBindManager.getDcIdByEaAndAppId(ChannelEnum.dingding, fsEa, appId);

        List<OuterOaEmployeeBindEntity> bindEntities = list.stream()
                .map(employee -> {
                    OuterOaEmployeeBindEntity entity = new OuterOaEmployeeBindEntity();
                    entity.setChannel(ChannelEnum.dingding);
                    entity.setFsEa(fsEa);
                    entity.setOutEa(outEa);
                    entity.setAppId(appId);
                    entity.setDcId(dcId);
                    entity.setFsEmpId(String.valueOf(employee.getEmployeeId()));
                    entity.setOutEmpId(employee.getDingEmployeeId());
                    entity.setBindStatus(BindStatusEnum.normal);
                    entity.setCreateTime(System.currentTimeMillis());
                    entity.setUpdateTime(System.currentTimeMillis());
                    return entity;
                })
                .collect(Collectors.toList());

        if (bindEntities.isEmpty()) {
            return 0;
        }

        Integer count = outerOaEmployeeBindManager.batchUpsert(bindEntities);


        List<DingTalkEmployeeObject> dataEntities = list.stream()
                .map(employee -> {
                    DingTalkEmployeeObject employeeData = new DingTalkEmployeeObject();
                    employeeData.setStatus(employee.getDingEmployeeStatus());
                    employeeData.setName(employee.getDingEmployeeName());
                    employeeData.setPhone(employee.getDingEmployeePhone());
                    employeeData.setUnionId(employee.getDingUnionId());
                    employeeData.setDeptId(employee.getDingDeptId());
                    employeeData.setDeptName(employee.getDingDeptName());
                    employeeData.setPhone(employee.getDingEmployeePhone());
                    return employeeData;
                })
                .collect(Collectors.toList());

        if (!dataEntities.isEmpty()) {
            outerOaEmployeeDataManager.batchUpsert(dataEntities, ChannelEnum.dingding, dcId);
        }

        return count;
    }

    @NotNull
    private static DingTalkEmployeeObject getEmployeeMongoData(DingMappingEmployeeResult emp) {
        DingTalkEmployeeObject employeeData = new DingTalkEmployeeObject();
        employeeData.setManager_userid(emp.getManagerUserid());
        employeeData.setUserid(emp.getDingEmployeeId());
        employeeData.setName(emp.getDingEmployeeName());
        employeeData.setPhone(emp.getDingEmployeePhone());
        employeeData.setEmail(emp.getDingEmployeeEmail());
        employeeData.setPosition(emp.getDingEmployeePosition());
        employeeData.setJobNumber(emp.getDingJobNumber());
        employeeData.setDeptId(emp.getDingDeptId());
        employeeData.setDeptName(emp.getDingDeptName());
        employeeData.setStatus(emp.getDingEmployeeStatus());
        employeeData.setUnionId(emp.getDingUnionId());
        employeeData.setSexType(emp.getDingSexType() != null ?
                String.valueOf(emp.getDingSexType()) : null);
        return employeeData;
    }

    /**
     * 批量保存职员
     *
     * @param list
     * @param appId
     * @return 设置超时2s, 钉钉更新员工的时候会发起多次请求
     */
    public Integer saveMappingEmployee(List<DingMappingEmployeeResult> list, String appId) {
        if (CollectionUtils.isEmpty(list)) {
            log.warn("saveMappingEmployee param list is null.");
            return 0;
        }
        // 创建一个列表来收集所有要更新的员工数据实体
        List<DingTalkEmployeeObject> dataEntities = new ArrayList<>();
        List<OuterOaEmployeeBindEntity> bindEntities = new ArrayList<>();

        final Integer ei = list.get(0).getEi();
        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        final String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        final String dcId = outerOaEnterpriseBindManager.getDcIdByEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        for (DingMappingEmployeeResult employee : list) {

            DingTalkEmployeeObject dataEntity = getEmployeeMongoData(employee);
            // 添加到集合而不是直接插入
            dataEntities.add(dataEntity);

            // 只绑定有员工id的
            if (employee.getEmployeeId() != null) {
                final OuterOaEmployeeBindEntity bindEntity = convert2OuterOaEmployeeBindEntity(fsEa, dcId, outEa, appId, employee);
                bindEntities.add(bindEntity);
            }
        }
        log.info("update mapping dataEntities :{} bindEntities:{}", dataEntities, bindEntities);

        // 批量更新或插入员工数据
        outerOaEmployeeDataManager.batchUpsert(dataEntities, ChannelEnum.dingding, dcId);

        return outerOaEmployeeBindManager.batchUpsert(bindEntities);
    }

    @NotNull
    private OuterOaEmployeeBindEntity convert2OuterOaEmployeeBindEntity(String fsEa, String dcId, String outEa, String appId, DingMappingEmployeeResult employee) {
        OuterOaEmployeeBindEntity bindEntity = new OuterOaEmployeeBindEntity();
        bindEntity.setOutEa(outEa);
        bindEntity.setChannel(ChannelEnum.dingding);
        bindEntity.setFsEa(fsEa);
        bindEntity.setAppId(appId);
        bindEntity.setDcId(dcId);
        bindEntity.setOutEmpId(employee.getDingEmployeeId());
        bindEntity.setFsEmpId(String.valueOf(employee.getEmployeeId()));
        bindEntity.setBindStatus(BindStatusEnum.normal);
        bindEntity.setCreateTime(System.currentTimeMillis());
        bindEntity.setUpdateTime(System.currentTimeMillis());
        return bindEntity;
    }


    /**
     * 批量自动保存职员:由于拉的是全量的用户，会导致没有手机号的账号也绑定过去，需要做过滤操作
     */
    public Integer saveAutoMappingEmployee(List<DingMappingEmployeeResult> list, String appId) {
        if (CollectionUtils.isEmpty(list)) {
            log.warn("saveAutoMappingEmployee param list is null.");
            return 0;
        }

        List<DingMappingEmployeeResult> filteredEmployees = list.stream()
                .filter(v -> !StringUtils.isEmpty(v.getEmployeeId()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filteredEmployees)) {
            log.warn("saveAutoMappingEmployee param dingMappingEmployeeResults is null.");
            return 0;
        }

        // 调用saveMappingEmployee来处理绑定
        return saveMappingEmployee(filteredEmployees, appId);
    }

    /**
     * 解绑
     *
     * @param dingEmployeeId
     * @return
     */
    @Transactional
    public Integer relieveBind(Integer ei, String dingEmployeeId, String appId) {
        if (Objects.isNull(ei) || Objects.isNull(dingEmployeeId)) {
            log.warn("deleteBind param ei or dingEmployeeId is null.");
            return 0;
        }
        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        return outerOaEmployeeBindManager.updateStatusByFsEaAndOutEmpId(
                ChannelEnum.dingding,
                fsEa,
                appId,
                dingEmployeeId,
                BindStatusEnum.stop
        );
    }

    /**
     * 删除
     *
     * @param dingEmployeeId
     * @return
     */
    @Deprecated
    @Transactional
    public Integer deleteBind(Integer ei, String dingEmployeeId, String appId) {
        if (Objects.isNull(ei) || Objects.isNull(dingEmployeeId)) {
            log.warn("deleteBind param ei or dingEmployeeId is null.");
            return 0;
        }
        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        return outerOaEmployeeBindManager.deleteByOutEmpId(
                ChannelEnum.dingding,
                fsEa,
                appId,
                dingEmployeeId
        );
    }

    /**
     * 根据dept,dingEmp删除数据
     * @param ei
     * @param dingEmployeeId
     * @return
     */
    @Transactional
    public Integer deleteByDeptId(Integer ei, String dingEmployeeId, Long dingDeptId, String appId) {
        if (Objects.isNull(ei) || Objects.isNull(dingEmployeeId) || Objects.isNull(dingDeptId)) {
            log.warn("deleteByDeptId param illegal: ei=[{}], dingEmployeeId=[{}], deptId=[{}]", ei, dingEmployeeId, dingDeptId);
            return 0;
        }

        // Convert enterprise account
        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);

        // Find employee data with matching department
        final OuterOaEmployeeDataEntity dataEntity = outerOaEmployeeDataMapper.selectOne(
                new LambdaQueryWrapper<OuterOaEmployeeDataEntity>()
                        .eq(OuterOaEmployeeDataEntity::getChannel, ChannelEnum.dingding)
                        .eq(OuterOaEmployeeDataEntity::getOutEa, outEa)
                        .eq(OuterOaEmployeeDataEntity::getAppId, appId)
                        .eq(OuterOaEmployeeDataEntity::getOutUserId, dingEmployeeId)
                        .eq(OuterOaEmployeeDataEntity::getOutDeptId, String.valueOf(dingDeptId))
        );

        if (dataEntity != null) {
            return outerOaEmployeeBindManager.updateStatusByFsEaAndOutEmpId(ChannelEnum.dingding,
                    fsEa,
                    appId,
                    dingEmployeeId,
                    BindStatusEnum.stop);
        }

        return 0;
    }

    public Integer physicalDeleteByDeptId(Integer ei, String dingEmployeeId, Long dingDeptId, String appId) {
        if (Objects.isNull(ei) || Objects.isNull(dingEmployeeId) || Objects.isNull(dingDeptId)) {
            log.warn("deleteByDeptId param illegal: ei=[{}], dingEmployeeId=[{}], deptId=[{}]", ei, dingEmployeeId, dingDeptId);
            return 0;
        }

        // Convert enterprise account
        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);

        // Find employee data with matching department
        final OuterOaEmployeeDataEntity dataEntity = outerOaEmployeeDataMapper.selectOne(
                new LambdaQueryWrapper<OuterOaEmployeeDataEntity>()
                        .eq(OuterOaEmployeeDataEntity::getChannel, ChannelEnum.dingding)
                        .eq(OuterOaEmployeeDataEntity::getOutEa, outEa)
                        .eq(OuterOaEmployeeDataEntity::getAppId, appId)
                        .eq(OuterOaEmployeeDataEntity::getOutUserId, dingEmployeeId)
                        .eq(OuterOaEmployeeDataEntity::getOutDeptId, String.valueOf(dingDeptId))
        );

        if (dataEntity == null) {
            return 0;
        }

        return outerOaEmployeeBindManager.deleteEmployeeBindEntity(ChannelEnum.dingding, outEa, fsEa, appId, null, Collections.singletonList(dingEmployeeId));
    }

    /**
     * 保存设置（更新）
     *
     * @param dingMappingEmployeeResult
     * @param appId
     * @return
     */
    @Transactional
    public Result<Integer> saveOperation(DingMappingEmployeeResult dingMappingEmployeeResult, String appId) {
        if (Objects.isNull(dingMappingEmployeeResult)) {
            log.warn("saveOperation param dingMappingEmployeeResult is null.");
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        Integer ei = dingMappingEmployeeResult.getEi();
        Integer employeeId = dingMappingEmployeeResult.getEmployeeId();

        if (ei == null || employeeId == null) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        final String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        final String dcId = outerOaEnterpriseBindManager.getDcIdByEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        if (StringUtils.isEmpty(outEa)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        String fsEmpId = String.valueOf(employeeId);

        // 检查员工是否已被绑定
        OuterOaEmployeeBindEntity existingBind = outerOaEmployeeBindManager.queryByFsEmpId(
                ChannelEnum.dingding,
                fsEa,
                appId,
                fsEmpId
        );

        if (existingBind != null) {
            log.warn("该纷享职员已被绑定！employeeName=[{}],employeePhone=[{}].",
                    dingMappingEmployeeResult.getEmployeeName(),
                    dingMappingEmployeeResult.getEmployeePhone());
            return Result.newError(ResultCode.EMP_HAS_BIND);
        }

        // 设置绑定状态
        if (Objects.nonNull(dingMappingEmployeeResult.getEmployeeId())) {
            dingMappingEmployeeResult.setBindStatus(EmpStatusEnum.BIND.getStatus());
        }

        // 创建新的绑定关系
        OuterOaEmployeeBindEntity bindEntity = new OuterOaEmployeeBindEntity();
        bindEntity.setId(IdGenerator.get());
        bindEntity.setChannel(ChannelEnum.dingding);
        bindEntity.setFsEa(fsEa);
        bindEntity.setOutEa(outEa);
        bindEntity.setAppId(appId);
        bindEntity.setDcId(dcId);
        bindEntity.setFsEmpId(fsEmpId);
        bindEntity.setOutEmpId(dingMappingEmployeeResult.getDingEmployeeId());
        bindEntity.setBindStatus(BindStatusEnum.convertDingtalkEmployeeBindStatus(dingMappingEmployeeResult.getBindStatus()));
        bindEntity.setCreateTime(System.currentTimeMillis());
        bindEntity.setUpdateTime(System.currentTimeMillis());

        Integer count = outerOaEmployeeBindManager.batchUpsert(Lists.newArrayList(bindEntity));

        // 创建员工数据
        final DingTalkEmployeeObject employeeData = getEmployeeMongoData(dingMappingEmployeeResult);

        // 使用批量更新方法
        List<DingTalkEmployeeObject> dataEntities = Collections.singletonList(employeeData);
        outerOaEmployeeDataManager.batchUpsert(dataEntities, ChannelEnum.dingding, dcId);
        return Result.newSuccess(count);
    }

    /**
     * 批量更新职员
     *
     * @param emp
     * @param appId
     * @return
     */
    @Transactional
    public Integer updateMappingEmployee(DingMappingEmployeeResult emp, String appId) {
        if (Objects.isNull(emp)) {
            log.warn("updateMappingEmployee param emp is null.");
            return 0;
        }

        // 调用saveMappingEmployee方法处理单个员工
        List<DingMappingEmployeeResult> employees = Collections.singletonList(emp);
        return saveMappingEmployee(employees, appId);
    }

    /**
     * 根据fsEmpId和appId查询员工信息
     */
    public DingMappingEmployeeResult queryMappingEmployeeByEi(Integer ei, Integer fxEmpId, String appId) {
        if (Objects.isNull(fxEmpId)) {
            log.warn("queryMappingEmployeeByEi param fxEmpId is null.");
            return null;
        }

        Result<DingMappingEmployeeResult> result = findById(ei, fxEmpId, appId);
        return result.isSuccess() ? result.getData() : null;
    }

    /**
     * 根据钉钉员工ID查询绑定信息
     *
     * @param ei 企业ID
     * @param dingEmpId 钉钉员工ID
     * @param appId 应用ID
     * @return 员工绑定信息
     */
    public DingMappingEmployeeResult findByDingEmpId(Integer ei, String dingEmpId, String appId) {
        if (Objects.isNull(ei) || Objects.isNull(dingEmpId) || StringUtils.isEmpty(appId)) {
            log.warn("findByDingEmpId param illegal, ei=[{}], dingEmpId=[{}], appId=[{}]", ei, dingEmpId, appId);
            return null;
        }

        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        final String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        if (StringUtils.isEmpty(outEa)) {
            return null;
        }

        // 使用联表查询
        Map<String, Object> result = outerOaEmployeeBindMapper.queryEmployeeBindByOutEmpId(
                ChannelEnum.dingding.name(),
                outEa,
                fsEa,
                appId,
                dingEmpId
        );

        if (result == null || result.isEmpty()) {
            return null;
        }

        return convertMapToResult(result, fsEa);
    }

    /**
     * 根据fsEmpId查询员工绑定信息
     *
     * @param ei 企业ID
     * @param fsEmpId 纷享员工ID
     * @param appId 应用ID
     * @return 员工绑定信息
     */
    public Result<DingMappingEmployeeResult> findById(Integer ei, Integer fsEmpId, String appId) {
        if (Objects.isNull(ei) || Objects.isNull(fsEmpId) || StringUtils.isEmpty(appId)) {
            log.warn("findById param error, ei=[{}] ,fsEmpId=[{}], appId=[{}]", ei, fsEmpId, appId);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        final String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        if (StringUtils.isEmpty(outEa)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        // 使用联表查询
        Map<String, Object> result = outerOaEmployeeBindMapper.queryEmployeeBindByFsEmpId(
                ChannelEnum.dingding.name(),
                outEa,
                fsEa,
                appId,
                String.valueOf(fsEmpId)
        );

        if (result == null || result.isEmpty()) {
            return Result.newSuccess(null);
        }

        DingMappingEmployeeResult employeeResult = convertMapToResult(result, fsEa);
        return Result.newSuccess(employeeResult);
    }

    /**
     * 判断纷享职员是否已绑定
     *
     * @param employeeId
     * @return
     */
    public Boolean checkEmpBindByEmpId(Integer ei, Integer employeeId, String appId) {
        if (Objects.isNull(employeeId)) {
            log.warn("checkEmpBindByEmpId param employeeId is null.");
            return false;
        }

        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        String fsEmpId = String.valueOf(employeeId);

        // 查询绑定关系
        OuterOaEmployeeBindEntity bindEntity = outerOaEmployeeBindManager.queryByFsEmpId(
                ChannelEnum.dingding,
                fsEa,
                appId,
                fsEmpId
        );

        return bindEntity != null;
    }

    /**
     * 更新绑定关系表中钉钉员工
     *
     * @param dingMappingEmployeeResult
     * @param appId
     * @return
     */
    public Integer updateDingEmpByDingId(DingMappingEmployeeResult dingMappingEmployeeResult, String appId) {
        if (Objects.isNull(dingMappingEmployeeResult)) {
            log.warn("updateDingEmpByDingId param dingMappingEmployeeResult is null.");
            return 0;
        }

        // 调用saveMappingEmployee方法处理单个员工
        List<DingMappingEmployeeResult> employees = Collections.singletonList(dingMappingEmployeeResult);
        return saveMappingEmployee(employees, appId);
    }

    /**
     * 更新绑定关系表中钉钉员工
     *
     * @param dingMappingEmployeeResult
     * @param appId
     * @return
     */
    public Integer updateDingEmpByFsId(DingMappingEmployeeResult dingMappingEmployeeResult, String appId) {
        if (Objects.isNull(dingMappingEmployeeResult)) {
            log.warn("updateDingEmpByFsId param dingMappingEmployeeResult is null.");
            return 0;
        }

        // 调用saveMappingEmployee方法处理单个员工
        List<DingMappingEmployeeResult> employees = Collections.singletonList(dingMappingEmployeeResult);
        return saveMappingEmployee(employees, appId);
    }

    public Integer updateModelEmp(DingMappingEmployeeResult dingMappingEmployeeResult, String appId) {
        if (Objects.isNull(dingMappingEmployeeResult)) {
            log.warn("updateModelEmp param dingMappingEmployeeResult is null.");
            return 0;
        }

        String fsEa = eieaConverter.enterpriseIdToAccount(dingMappingEmployeeResult.getEi());
        final String dcId = outerOaEnterpriseBindManager.getDcIdByEaAndAppId(ChannelEnum.dingding, fsEa, appId);

        // 使用EmployeeMongoData
        DingTalkEmployeeObject employeeData = getEmployeeMongoData(dingMappingEmployeeResult);

        // 使用批量更新方法
        List<DingTalkEmployeeObject> dataEntities = Collections.singletonList(employeeData);
        return outerOaEmployeeDataManager.batchUpsert(dataEntities, ChannelEnum.dingding, dcId);
    }


    /**
     * 查询绑定关系表中钉钉员工是否已经存在
     *
     * @param dingMappingEmployeeResult
     * @param appId
     * @return
     */
    public DingMappingEmployeeResult findIsBindByDingId(DingMappingEmployeeResult dingMappingEmployeeResult, String appId) {
        if (Objects.isNull(dingMappingEmployeeResult)) {
            log.warn("findIsBindByDingId param dingMappingEmployeeResult is null.");
            return null;
        }

        String fsEa = eieaConverter.enterpriseIdToAccount(dingMappingEmployeeResult.getEi());

        // 查询绑定关系
        OuterOaEmployeeBindEntity bindEntity = outerOaEmployeeBindManager.queryNormalByFsEaAndOutEmpId(
                ChannelEnum.dingding,
                fsEa,
                appId,
                dingMappingEmployeeResult.getDingEmployeeId());

        return convertToResult(bindEntity);
    }

    /**
     * 根据dingEmpId返回列表的数据
     *
     * @param ei,dingEmpId
     * @param appId
     * @return
     */
    public List<DingMappingEmployeeResult> findDingEmpIdList(Integer ei, String dingEmpId, String appId) {
        if (Objects.isNull(ei) || Objects.isNull(dingEmpId)) {
            log.warn("findDingEmpIdList param illegal: ei=[{}], dingEmpId=[{}]", ei, dingEmpId);
            return Collections.emptyList();
        }

        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        final String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);

        final OuterOaEmployeeBindEntity employeeBindEntity = outerOaEmployeeBindManager.getNormalEmployeeBindEntity(ChannelEnum.dingding, outEa, fsEa, appId, dingEmpId);
        DingMappingEmployeeResult dingMappingEmployeeResult = convertToResult(employeeBindEntity);
        if(ObjectUtils.isEmpty(dingMappingEmployeeResult)){
            return null;
        }
        return Lists.newArrayList(dingMappingEmployeeResult);
    }

    /**
     * 查找用户的所有回话应用信息
     */
    public List<FsUserInfoModel> findByOutUserId(String outEa, String outUserId, String appId) {
        log.info("findByOutUserId,outEa={},outUserId={},appId={}", outEa, outUserId, appId);
        if (org.apache.commons.lang3.StringUtils.isEmpty(outEa) || org.apache.commons.lang3.StringUtils.isEmpty(outUserId) || org.apache.commons.lang3.StringUtils.isEmpty(appId)) {
            return null;
        }

        Result<List<DingEnterprise>> result = dingEnterpriseManager.queryEnterpriseByDingCorpId(outEa, appId);
        log.info("findByOutUserId,result={}", result);

        if (!result.isSuccess() || CollectionUtils.isEmpty(result.getData())) {
            return new ArrayList<>();
        }

        List<DingEnterprise> dingEnterpriseList = result.getData();
        List<FsUserInfoModel> fsUserInfoModelList = new ArrayList<>();

        for (DingEnterprise dingEnterprise : dingEnterpriseList) {
            Integer ei = dingEnterprise.getEi();
            DingMappingEmployeeResult employeeResult = findByDingEmpId(ei, outUserId, appId);
            log.info("findByOutUserId,employeeResult={}", employeeResult);
            if (employeeResult == null) {
                continue;
            }
            FsUserInfoModel fsUserInfoModel = new FsUserInfoModel();
            fsUserInfoModel.setFsEa(dingEnterprise.getEa());
            fsUserInfoModel.setFsUserId(employeeResult.getEmployeeId() + "");
            fsUserInfoModelList.add(fsUserInfoModel);
        }

        log.info("findByOutUserId,fsUserInfoModelList={}", fsUserInfoModelList);
        return fsUserInfoModelList;
    }

    public Result<List<DingMappingEmployeeResult>> findByOutUserIds(Integer ei, List<String> outUserIds, String appId) {
        if (Objects.isNull(ei) || CollectionUtils.isEmpty(outUserIds)) {
            log.warn("findByOutUserIds param error, ei=[{}] ,outUserIds=[{}], appId=[{}]", ei, outUserIds, appId);
            return null;
        }

        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        final String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        if (StringUtils.isEmpty(outEa)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        // 批量查询绑定关系
        List<Map<String, Object>> results = outerOaEmployeeBindMapper.batchQueryEmployeeBindByOutEmpIds(
                ChannelEnum.dingding.name(),
                outEa,
                fsEa,
                appId,
                outUserIds
        );

        if (CollectionUtils.isEmpty(results)) {
            return Result.newSuccess(new ArrayList<>());
        }

        // 转换为Result对象
        List<DingMappingEmployeeResult> employeeResults = results.stream()
                .map(result -> convertMapToResult(result, fsEa))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return Result.newSuccess(employeeResults);
    }

    /**
     * 绑定关系表中新增钉钉员工
     *
     * @param dingMappingEmployeeResult
     * @param appId
     * @return
     */
    public Integer insertDingEmp(DingMappingEmployeeResult dingMappingEmployeeResult, String appId) {
        if (Objects.isNull(dingMappingEmployeeResult)) {
            log.warn("insertDingEmp param dingMappingEmployeeResult is null.");
            return 0;
        }

        // 调用saveMappingEmployee方法处理单个员工
        List<DingMappingEmployeeResult> employees = Collections.singletonList(dingMappingEmployeeResult);
        return saveMappingEmployee(employees, appId);
    }

    /**
     * 通过unionid查询钉钉员工
     *
     * @param ei
     * @param unionId
     * @param appId
     * @return
     */
    public Result<DingMappingEmployeeResult> queryEmpByUnionId(Integer ei, String unionId, String appId) {
        if (Objects.isNull(ei) || Objects.isNull(unionId)) {
            log.warn("queryEmpByUnionId param illegal");
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        final OuterOaEnterpriseBindEntity entity = outerOaEnterpriseBindManager.getByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);

        appId = entity.getAppId();
        final OuterOaEmployeeDataEntity outerOaEmployeeDataEntity = outerOaEmployeeDataMapper.selectDingByUnionId(ChannelEnum.dingding.name(), entity.getOutEa(), appId, unionId);
        if (Objects.isNull(outerOaEmployeeDataEntity)) {
            return Result.newSuccess(null);
        }

        // 查询所有绑定关系
        final OuterOaEmployeeBindEntity outerOaEmployeeBindEntity = outerOaEmployeeBindManager.queryNormalByFsEaAndOutEmpId(
                ChannelEnum.dingding,
                fsEa,
                appId,
                outerOaEmployeeDataEntity.getOutUserId()
        );


        return Result.newSuccess(convertToResult(outerOaEmployeeBindEntity, convertToEmployeeMongoData(outerOaEmployeeDataEntity.getOutUserInfo())));
    }

    /**
     * 通过钉钉userId查询钉钉员工
     *
     * @param ei
     * @param appId
     * @param dingUserId
     * @return
     */
    public Result<DingMappingEmployeeResult> queryEmpByDingUserId(Integer ei, String appId, String dingUserId) {
        if (Objects.isNull(ei) || Objects.isNull(dingUserId)) {
            log.warn("queryEmpByDingUserId param illegal");
            return Result.newError(ResultCode.PARAMS_ERROR);
        }


        // 使用findByDingEmpId方法查询
        DingMappingEmployeeResult result = findByDingEmpId(ei, dingUserId, appId);
        return Result.newSuccess(result);
    }

    /**
     * 根据crmId查询信息
     */
    public Result<List<DingMappingEmployeeResult>> fixNoMainDeptByUserID(Integer ei, List<Integer> userIds, String appId) {
        if (Objects.isNull(ei) || Objects.isNull(userIds)) {
            log.warn("queryEmpByUnionId param illegal");
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        final String ea = eieaConverter.enterpriseIdToAccount(ei);
        final String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, ea, appId);
        final List<String> ids = userIds.stream().map(String::valueOf).collect(Collectors.toList());
        final List<Map<String, Object>> maps = outerOaEmployeeBindMapper.batchQueryEmployeeBindByFsEmpIds(ChannelEnum.dingding.name(), outEa, ea, appId, ids);
        final List<DingMappingEmployeeResult> results = maps.stream().map(result -> convertMapToResult(result, ea)).collect(Collectors.toList());
        return Result.newSuccess(results);
    }


    /**
     * 分页查询绑定了的纷享的员工
     *
     * @param offset
     * @param limit
     * @param appId
     * @return
     */
    public Result<List<BindFxUserResult>> queryBindFxEmp(Integer offset, Integer limit, String appId) {
        if (offset == null || limit == null) {
            log.warn("queryBindFxEmp param error: offset=[{}], limit=[{}]", offset, limit);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        // 分页查询所有绑定状态为normal的绑定关系
        Page<OuterOaEmployeeBindEntity> page = new Page<>(offset / limit + 1, limit);
        LambdaQueryWrapper<OuterOaEmployeeBindEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OuterOaEmployeeBindEntity::getChannel, ChannelEnum.dingding)
                .eq(OuterOaEmployeeBindEntity::getAppId, appId)
                .eq(OuterOaEmployeeBindEntity::getBindStatus, BindStatusEnum.normal)
                .orderByDesc(OuterOaEmployeeBindEntity::getUpdateTime);

        outerOaEmployeeBindMapper.selectPage(page, queryWrapper);

        List<OuterOaEmployeeBindEntity> bindEntities = page.getRecords();
        if (CollectionUtils.isEmpty(bindEntities)) {
            return Result.newSuccess(new ArrayList<>());
        }

        // 转换为BindFxUserResult
        List<BindFxUserResult> results = new ArrayList<>();
        for (OuterOaEmployeeBindEntity bindEntity : bindEntities) {
            BindFxUserResult result = new BindFxUserResult();
            result.setEi(eieaConverter.enterpriseAccountToId(bindEntity.getFsEa()));
            result.setEmployeeId(Integer.valueOf(bindEntity.getFsEmpId()));
            results.add(result);
        }

        return Result.newSuccess(results);
    }

    /**
     * 批量更新员工
     */
    public Result<Integer> batchUpdate(Integer ei, List<User> userList, String appId) {
        if (CollectionUtils.isEmpty(userList)) {
            return Result.newSuccess(0);
        }

        final String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        final OuterOaEnterpriseBindEntity entity = outerOaEnterpriseBindManager.getEntity(ChannelEnum.dingding, fsEa, null, appId);
        List<DingTalkEmployeeObject> dataEntities = new ArrayList<>();
        for (User user : userList) {
            if (StringUtils.isEmpty(user.getUserid())) {
                continue;
            }

            // 获取用户数据
            OuterOaEmployeeDataEntity dataEntity = getEmployeeDataByOutUserId(
                    entity.getOutEa(),
                    appId,
                    user.getUserid()
            );

            if (dataEntity == null) {
                continue;
            }

            // 获取现有用户信息并转换为EmployeeMongoData
            DingTalkEmployeeObject employeeData = convertToEmployeeMongoData(dataEntity.getOutUserInfo());
            if (employeeData == null) {
                employeeData = new DingTalkEmployeeObject();
            }

            // 更新信息
            employeeData.setUserid(user.getUserid());
            employeeData.setName(user.getName());
            employeeData.setPhone(user.getMobile());

            // 保存更新
            dataEntities.add(employeeData);
        }
        Integer count = outerOaEmployeeDataManager.batchUpsert(dataEntities, ChannelEnum.dingding, entity.getId());

        return Result.newSuccess(count);
    }

    public Result<List<DingMappingEmployeeResult>> queryEmployeesBatch(Integer ei, String appId) {
        if (ei == null) {
            log.warn("queryEmployeesBatch param error: ei=[{}]", ei);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        String fsEa = eieaConverter.enterpriseIdToAccount(ei);

        // 查询所有绑定关系
        List<OuterOaEmployeeBindEntity> bindEntities = outerOaEmployeeBindManager.queryByFsEa(
                ChannelEnum.dingding,
                fsEa,
                appId
        );

        if (CollectionUtils.isEmpty(bindEntities)) {
            return Result.newSuccess(new ArrayList<>());
        }

        final List<DingMappingEmployeeResult> collect = bindEntities.stream()
                .map(this::convertToResult)
                .collect(Collectors.toList());

        return Result.newSuccess(collect);
    }

    public Result<List<DingMappingEmployeeResult>> queryEmployeesByDingUserIds(Integer ei, List<String> userIds, String appId) {
        if (ei == null || CollectionUtils.isEmpty(userIds)) {
            log.warn("queryEmployeesByDingUserIds param error: ei=[{}], userIds=[{}]", ei, userIds);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        return findByOutUserIds(ei, userIds, appId);
    }

    public Result<List<DingMappingEmployeeResult>> batchGetDingEmployeesByFsIds(Integer ei, List<Integer> fsEmpIds, String appId) {
        if (ei == null || CollectionUtils.isEmpty(fsEmpIds)) {
            log.warn("batchGetDingEmployeesByFsIds param error: ei=[{}], fsEmpIds=[{}], appId=[{}]", ei, fsEmpIds, appId);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }


        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        final String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);

        // 将Integer列表转换为String列表
        List<String> fsEmpIdStrings = fsEmpIds.stream()
                .map(String::valueOf)
                .collect(Collectors.toList());

        // 批量查询绑定关系
        List<Map<String, Object>> results = outerOaEmployeeBindMapper.batchQueryEmployeeBindByFsEmpIds(
                ChannelEnum.dingding.name(),
                outEa,
                fsEa,
                appId,
                fsEmpIdStrings
        );

        if (CollectionUtils.isEmpty(results)) {
            return Result.newSuccess(new ArrayList<>());
        }

        // 转换为结果对象
        List<DingMappingEmployeeResult> employeeResults = results.stream()
                .map(result -> convertMapToResult(result, fsEa))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return Result.newSuccess(employeeResults);
    }

    public Result<List<DingMappingEmployeeResult>> batchGetDingEmployeesByDingIds(Integer ei, List<String> dingEmpIds, String appId) {
        if (ei == null || CollectionUtils.isEmpty(dingEmpIds)) {
            log.warn("batchGetDingEmployeesByDingIds param error: ei=[{}], dingEmpIds=[{}]", ei, dingEmpIds);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        return findByOutUserIds(ei, dingEmpIds, appId);
    }

    public Result<List<DingMappingEmployeeResult>> findUnbindByDingEmployeePhone(Integer ei, String dingEmployeePhone, String appId) {
        if (ei == null || StringUtils.isEmpty(dingEmployeePhone)) {
            log.warn("findUnbindByDingEmployeePhone param error: ei=[{}], dingEmployeePhone=[{}]", ei, dingEmployeePhone);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        final String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        if (StringUtils.isEmpty(outEa)) {
            return Result.newSuccess(new ArrayList<>());
        }

        final List<Map<String, Object>> unbindByDingEmployeePhone = outerOaEmployeeBindMapper.findUnbindByDingEmployeePhone(ChannelEnum.dingding.name(), outEa, appId, dingEmployeePhone);

        return Result.newSuccess(unbindByDingEmployeePhone.stream().map(result -> convertMapToResult(result, fsEa)).collect(Collectors.toList()));
    }

    /**
     * 根据外部用户ID获取员工数据
     *
     * @param outEa     外部企业ID
     * @param appId     应用ID
     * @param outUserId 外部用户ID
     * @return 员工数据实体
     */
    private OuterOaEmployeeDataEntity getEmployeeDataByOutUserId(String outEa, String appId, String outUserId) {
        if (StringUtils.isEmpty(outEa) || StringUtils.isEmpty(appId) || StringUtils.isEmpty(outUserId)) {
            log.warn("getEmployeeDataByOutUserId param illegal: channel={}, outEa={}, appId={}, outUserId={}",
                    ChannelEnum.dingding, outEa, appId, outUserId);
            return null;
        }

        LambdaQueryWrapper<OuterOaEmployeeDataEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OuterOaEmployeeDataEntity::getChannel, ChannelEnum.dingding)
                .eq(OuterOaEmployeeDataEntity::getOutEa, outEa)
                .eq(OuterOaEmployeeDataEntity::getAppId, appId)
                .eq(OuterOaEmployeeDataEntity::getOutUserId, outUserId);

        return outerOaEmployeeDataMapper.selectOne(queryWrapper);
    }


}
