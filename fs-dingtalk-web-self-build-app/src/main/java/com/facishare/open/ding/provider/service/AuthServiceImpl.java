package com.facishare.open.ding.provider.service;

import com.facishare.open.ding.provider.manager.AuthManager;
import com.facishare.open.ding.api.service.AuthService;
import com.facishare.open.ding.common.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by system on 2018/4/9.
 */
@Service
@Slf4j
public class AuthServiceImpl implements AuthService {

    @Autowired
    private AuthManager authManager;

    @Override
    public Result<Boolean> isAppAdmin(String fsUserAccount) {
        boolean result = authManager.isAppAdmin(fsUserAccount);
        return Result.newSuccess(result);
    }

}
