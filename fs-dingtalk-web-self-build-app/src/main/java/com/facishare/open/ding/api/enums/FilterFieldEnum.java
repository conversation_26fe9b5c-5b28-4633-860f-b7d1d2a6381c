package com.facishare.open.ding.api.enums;

import org.apache.commons.lang3.StringUtils;

public enum FilterFieldEnum {
    COUNTRY(1,"country"),

    CITY(1,"city"),

    DISTRICT(1,"district"),

    OBJECT_REFERENCE(1,"object_reference"),

    PROVINCE(1,"province"),

    EMPLOYEE(1,"employee"),

    FILE_ATTACHMENT(1,"file_attachment"),

    SELECT_ONE(2,"select_one")
    ;

    //1应用类型，2下拉选类型
    private int type;

    private String field;

    FilterFieldEnum(int type,String field){
        this.type = type;
        this.field = field;
    }

    public String getField() {
        return field;
    }

    public int getType() {
        return type;
    }

    public static boolean isInvalid(int type, String field) {
        if (StringUtils.isNotEmpty(field)) {
            for (FilterFieldEnum filterFieldEnum : FilterFieldEnum.values()) {
                if ((type<=0?true:type==filterFieldEnum.getType()) && field.equals(filterFieldEnum.getField())) {
                    return true;
                }
            }
        }
        return false;
    }
}
