package service;

import base.BaseAbstractTest;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.service.ToolsService;
import com.facishare.open.ding.common.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;

@Slf4j
public class ToolsServiceTest extends BaseAbstractTest {
    @Resource
    private ToolsService toolsService;

    @Test
    public void bindEmpByExcel(){
        List<DingMappingEmployeeResult> employeeResults = new LinkedList<>();
            DingMappingEmployeeResult result = new DingMappingEmployeeResult();
            result.setEi(83384);
            result.setEmployeePhone("15086666004");
            result.setEmployeeName("啊嘞嘞");
            result.setEmployeeId(1111);
            result.setDingEmployeeId("01634349520821687946");
            result.setEmployeeStatus(1);
            result.setBindStatus(2);
            employeeResults.add(result);
        toolsService.bindEmpByExcel(employeeResults, null);
    }

    @Test
    public void enterpriseUnbind(){
        Result<Integer> integerResult = toolsService.enterpriseUnbind("11111", "dingdingdang");
        log.info("ToolsServiceTest.enterpriseUnbind,result={}.", integerResult);
    }
}
