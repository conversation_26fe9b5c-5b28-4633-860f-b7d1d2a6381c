package com.facishare.open.ding.provider.dingding;

import base.BaseAbstractTest;
import org.junit.Test;

import java.util.List;


public class DingRequestUtilTest extends BaseAbstractTest {

    @Test
    public void Test(){
        String accessToken = "515c5bc2223937b894847e54322b823e";
        String clientIp = "http://************:25775/dingtalkinner/";
        String nginxIp = null;
        List<String> res = DingRequestUtil.getAdminList(clientIp, nginxIp, accessToken);
        System.out.println(res);
    }

    @Test
    public void Test2(){
        String agentId = "**********";
        String accessToken = "515c5bc2223937b894847e54322b823e";
        String clientIp = "http://************:25775/dingtalkinner/";
        String nginxIp = null;
        DingRequestUtil.queryAppScope(clientIp, nginxIp, agentId, accessToken);
    }
}
