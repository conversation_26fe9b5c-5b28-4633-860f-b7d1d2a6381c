package base;

import org.junit.BeforeClass;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * Created by system on 2018/4/27.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring/applicationContext.xml")
public abstract class BaseAbstractTest {

    @BeforeClass
    public static void SetUp(){
        System.setProperty("config.mode", "localFirst");
        System.setProperty("process.profile", "fstest");
        System.setProperty("process.name","fs-dingtalk-web-self-build-app");
    }

}
