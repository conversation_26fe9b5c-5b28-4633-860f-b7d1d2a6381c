package com.facishare.open.oa.base.dbproxy.pg.entity.dingtalk.isv;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 轮询同步数据实体类 - PostgreSQL
 */
@Data
@TableName("dingtalk_isv_polling_sync_time")
public class PollingSyncDataPgEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 最后同步时间
     */
    private Long lastSyncTime;

    /**
     * 事件级别
     */
    private Integer eventLevel;

    /**
     * 套件票据
     */
    private String suiteTicket;

    /**
     * 更新时间
     */
    private Date updateTime;
}