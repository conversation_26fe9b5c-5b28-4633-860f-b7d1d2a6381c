package com.facishare.open.oa.base.dbproxy.pg.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * QyweixinBusinessInfoBindEntity
 * 企业微信业务信息绑定实体类
 */
@Data
@TableName("qyweixin_business_info_bind")
public class QyweixinBusinessInfoBindEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private String id;

    /**
     * 纷享EA
     */
    private String fsEa;

    /**
     * 企业ID
     */
    private String outEa;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 状态，normal-正常，stop-停用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateTime;
} 