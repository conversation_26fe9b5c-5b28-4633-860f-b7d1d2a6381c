package com.facishare.open.oa.base.dbproxy.pg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaScheduleBindEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * Mapper接口 - 日程绑定
 */
@Mapper
public interface OuterOaScheduleBindMapper extends BaseMapper<OuterOaScheduleBindEntity> {

    /**
     * 批量插入或更新日程绑定信息 基于(dc_id, fs_schedule_id, out_schedule_id,
     * out_user_id)作为唯一索引进行更新
     *
     * @param list 需要更新的实体列表
     * @return 更新成功的记录数
     */
    @Update("<script>" + "INSERT INTO outer_oa_schedule_bind (" + "    id, dc_id, channel, app_id, fs_ea, out_ea, "
            + "    fs_schedule_id, out_schedule_id, out_user_id, " + "    status, create_time, update_time"
            + ") VALUES " + "<foreach collection='list' item='item' separator=','>" + "("
            + "    #{item.id}, #{item.dcId}, #{item.channel}, " + "    #{item.appId}, #{item.fsEa}, #{item.outEa}, "
            + "    #{item.fsScheduleId}, #{item.outScheduleId}, #{item.outUserId}, "
            + "    #{item.status}, #{item.createTime}, #{item.updateTime}" + ")" + "</foreach> "
            + "ON CONFLICT (dc_id, fs_schedule_id, out_schedule_id, out_user_id) DO UPDATE SET "
            + "    channel = EXCLUDED.channel, " + "    app_id = EXCLUDED.app_id, " + "    fs_ea = EXCLUDED.fs_ea, "
            + "    out_ea = EXCLUDED.out_ea, " + "    status = EXCLUDED.status, "
            + "    update_time = EXCLUDED.update_time" + // create_time不更新，保持原值
            "</script>")
    Integer batchUpsert(@Param("list") List<OuterOaScheduleBindEntity> list);
}
