package com.facishare.open.oa.base.dbproxy.pg.entity.dingtalk.isv;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 同步数据映射实体类 - PostgreSQL
 */
@Data
@TableName("dingtalk_isv_sync_data_mappings")
public class SyncDataMappingsPgEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 企业ei
     */
    private Integer ei;

    /**
     * 企业ID
     */
    private String cropId;

    /**
     * CRM对象API名称
     */
    private String crmObjectApiName;

    /**
     * 钉钉对象API名称
     */
    private String dingObjectApiName;

    /**
     * CRM数据ID
     */
    private String crmDataId;

    /**
     * 钉钉数据ID
     */
    private String dingDataId;

    /**
     * MD5字符串
     */
    private String mdStr;

    /**
     * 同步方向
     */
    private Integer director;

    /**
     * 同步状态 1:同步失败 2:同步成功
     */
    private Integer status;

    /**
     * 数据是否已经创建 1:未创建 2:已创建
     */
    private Integer created;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 版本号
     */
    private Integer version;
}