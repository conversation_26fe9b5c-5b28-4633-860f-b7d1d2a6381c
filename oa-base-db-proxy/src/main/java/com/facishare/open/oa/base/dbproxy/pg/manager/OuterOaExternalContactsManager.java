package com.facishare.open.oa.base.dbproxy.pg.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaExternalContactsEntity;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaExternalContactsMapper;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaExternalContactsParams;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaExternalContactsParams;
import com.fxiaoke.api.IdGenerator;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * Manager 类 - 外部联系人绑定
 */
@Component
public class OuterOaExternalContactsManager {

    @Resource
    private OuterOaExternalContactsMapper outerOaExternalContactsMapper;

    public Integer insert(OuterOaExternalContactsEntity entity) {
        if (StringUtils.isEmpty(entity.getId())) {
            entity.setId(IdGenerator.get());
        }
        return outerOaExternalContactsMapper.insert(entity);
    }

    public Integer updateById(OuterOaExternalContactsEntity entity) {
        return outerOaExternalContactsMapper.updateById(entity);
    }

    public OuterOaExternalContactsEntity getExternalContactsEntity(ChannelEnum channel, String outEa, String outUserId, String externalUserId) {
        LambdaQueryWrapper<OuterOaExternalContactsEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaExternalContactsEntity::getChannel,channel);
        wrapper.eq(OuterOaExternalContactsEntity::getOutEa,outEa);
        wrapper.eq(OuterOaExternalContactsEntity::getOutUserId,outUserId);
        wrapper.eq(OuterOaExternalContactsEntity::getExternalUserId,externalUserId);
        return outerOaExternalContactsMapper.selectOne(wrapper);
    }

    //是否有数据
    public Boolean hasExternalContactsEntity(ChannelEnum channel, String outEa, String outUserId) {
        LambdaQueryWrapper<OuterOaExternalContactsEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaExternalContactsEntity::getChannel,channel);
        wrapper.eq(OuterOaExternalContactsEntity::getOutEa,outEa);
        if (StringUtils.isNotEmpty(outUserId)) {
            wrapper.eq(OuterOaExternalContactsEntity::getOutUserId,outUserId);
        }
        return outerOaExternalContactsMapper.exists(wrapper);
    }

    public List<OuterOaExternalContactsEntity> getUserAllExternalContacts(ChannelEnum channel, String outEa, String outUserId) {
        LambdaQueryWrapper<OuterOaExternalContactsEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaExternalContactsEntity::getChannel,channel);
        wrapper.eq(OuterOaExternalContactsEntity::getOutEa,outEa);
        wrapper.eq(OuterOaExternalContactsEntity::getOutUserId,outUserId);
        return outerOaExternalContactsMapper.selectList(wrapper);
    }

    public List<OuterOaExternalContactsEntity> batchGetUserAllExternalContacts(ChannelEnum channel, String outEa, String outUserId, List<String> externalUserIds) {
        LambdaQueryWrapper<OuterOaExternalContactsEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaExternalContactsEntity::getChannel,channel);
        wrapper.eq(OuterOaExternalContactsEntity::getOutEa,outEa);
        if (StringUtils.isNotEmpty(outUserId)) {
            wrapper.eq(OuterOaExternalContactsEntity::getOutUserId,outUserId);
        }
        if (CollectionUtils.isNotEmpty(externalUserIds)) {
            wrapper.in(OuterOaExternalContactsEntity::getExternalUserId, externalUserIds);
        }
        return outerOaExternalContactsMapper.selectList(wrapper);
    }

    //delete
    public Integer deleteExternalUserId(ChannelEnum channel, String outEa, String outUserId, String externalUserId) {
        LambdaQueryWrapper<OuterOaExternalContactsEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OuterOaExternalContactsEntity::getChannel,channel);
        wrapper.eq(OuterOaExternalContactsEntity::getOutEa,outEa);
        wrapper.eq(OuterOaExternalContactsEntity::getOutUserId,outUserId);
        wrapper.eq(OuterOaExternalContactsEntity::getExternalUserId,externalUserId);
        return outerOaExternalContactsMapper.delete(wrapper);
    }

    /**
     * 批量插入或更新外部联系人信息
     * @param entities 外部联系人实体列表
     * @return 成功插入或更新的记录数
     */
    public Integer batchUpsertInfos(List<OuterOaExternalContactsEntity> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return 0;
        }

        // 确保所有实体都有ID
        for (OuterOaExternalContactsEntity entity : entities) {
//            if (StringUtils.isEmpty(entity.getId())) {
//                entity.setId(IdGenerator.get());
//            }
            // 确保时间戳字段有值
            if (entity.getCreateTime() == null) {
                entity.setCreateTime(System.currentTimeMillis());
            }
            if (entity.getUpdateTime() == null) {
                entity.setUpdateTime(System.currentTimeMillis());
            }
        }

        return outerOaExternalContactsMapper.batchUpsertInfos(entities);
    }

    /**
     * 根据参数查询外部联系人信息列表
     * @param params 查询参数
     * @return 外部联系人实体列表
     */
    public List<OuterOaExternalContactsEntity> getEntities(OuterOaExternalContactsParams params) {
        LambdaQueryWrapper<OuterOaExternalContactsEntity> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(params.getId())) {
            wrapper.eq(OuterOaExternalContactsEntity::getId, params.getId());
        }
        if (params.getChannel() != null) {
            wrapper.eq(OuterOaExternalContactsEntity::getChannel, params.getChannel());
        }
        if (StringUtils.isNotEmpty(params.getOutEa())) {
            wrapper.eq(OuterOaExternalContactsEntity::getOutEa, params.getOutEa());
        }
        if (StringUtils.isNotEmpty(params.getOutUserId())) {
            wrapper.eq(OuterOaExternalContactsEntity::getOutUserId, params.getOutUserId());
        }
        if (StringUtils.isNotEmpty(params.getExternalUserId())) {
            wrapper.eq(OuterOaExternalContactsEntity::getExternalUserId, params.getExternalUserId());
        }

        return outerOaExternalContactsMapper.selectList(wrapper);
    }
}
