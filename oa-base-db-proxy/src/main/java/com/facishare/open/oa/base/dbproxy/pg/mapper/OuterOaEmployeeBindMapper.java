package com.facishare.open.oa.base.dbproxy.pg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;

import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

/**
 * Mapper 接口 - 员工绑定信息
 */
@Mapper
public interface OuterOaEmployeeBindMapper extends BaseMapper2<OuterOaEmployeeBindEntity> {
        @Insert("<script>"
                        + "INSERT INTO outer_oa_employee_bind (id, channel, fs_ea, out_ea, app_id, fs_emp_id, out_emp_id, bind_status, create_time, update_time, dc_id) "
                        + "VALUES " + "<foreach collection='employeeBindEntities' item='mapping' separator=','>"
                        + "(#{mapping.id}, #{mapping.channel}, #{mapping.fsEa}, #{mapping.outEa}, #{mapping.appId}, #{mapping.fsEmpId}, #{mapping.outEmpId}, #{mapping.bindStatus}, #{mapping.createTime}, #{mapping.updateTime}, #{mapping.dcId})"
                        + "</foreach> " + "ON CONFLICT (id) DO NOTHING" + "</script>")
        int batchBindEmployeeList(@Param("employeeBindEntities") List<OuterOaEmployeeBindEntity> employeeBindEntities);

        /**
         * 批量插入或更新员工绑定信息
         * 修复：使用正确的唯一约束字段组合 (dc_id, out_emp_id)
         *
         * @param entities 员工绑定信息实体列表
         * @return 成功插入或更新的记录数
         */
        @Update("<script>" + "INSERT INTO outer_oa_employee_bind "
                        + "(id, channel, dc_id, out_ea, app_id, out_emp_id, fs_ea, fs_emp_id, bind_status, create_time, update_time) "
                        + "VALUES " + "<foreach collection='list' item='item' separator=','> " + "    ( "
                        + "    #{item.id}, #{item.channel}, #{item.dcId}, #{item.outEa}, #{item.appId}, "
                        + "    #{item.outEmpId}, #{item.fsEa}, #{item.fsEmpId}, #{item.bindStatus}, #{item.createTime}, #{item.updateTime} "
                        + "    ) " + "</foreach> " + "ON CONFLICT (dc_id, out_emp_id) " + "DO UPDATE SET "
                        + "    fs_ea = EXCLUDED.fs_ea, " + "    fs_emp_id = EXCLUDED.fs_emp_id, " + "    bind_status = EXCLUDED.bind_status, "
                        + "    update_time = EXCLUDED.update_time " + "</script>")
        Integer batchUpsert(@Param("list") List<OuterOaEmployeeBindEntity> entities);

        /**
         * 联表查询员工绑定关系和详细信息
         *
         * @param channel    渠道
         * @param outEa      外部企业账号
         * @param fsEa       纷享企业账号
         * @param appId      应用ID
         * @param bindStatus 绑定状态
         * @param pageSize   分页大小
         * @param offset     分页偏移量
         * @return 员工绑定关系和详细信息
         */
        @Select({ "<script>", "SELECT b.*, d.* FROM outer_oa_employee_bind b",
                        "LEFT JOIN outer_oa_employee_data d ON b.out_emp_id = d.out_user_id",
                        "AND d.channel = #{channel}", "AND d.out_ea = #{outEa}", "AND d.app_id = #{appId}",
                        "WHERE b.fs_ea = #{fsEa}", "AND b.app_id = #{appId}", "<if test='bindStatus != null'>",
                        "AND b.bind_status = #{bindStatus}", "</if>", "ORDER BY b.id", "<if test='pageSize != null'>",
                        "LIMIT #{pageSize} OFFSET #{offset}", "</if>", "</script>" })
        List<Map<String, Object>> queryEmployeeBindWithData(@Param("channel") String channel,
                        @Param("outEa") String outEa, @Param("fsEa") String fsEa, @Param("appId") String appId,
                        @Param("bindStatus") String bindStatus, @Param("pageSize") Integer pageSize,
                        @Param("offset") Integer offset);

        /**
         * 根据fsEmpId联表查询
         *
         * @param channel 渠道
         * @param outEa   外部企业账号
         * @param fsEa    纷享企业账号
         * @param appId   应用ID
         * @param fsEmpId 纷享员工ID
         * @return 员工绑定关系和详细信息
         */
        @Select({ "SELECT b.*, d.* FROM outer_oa_employee_bind b",
                        "LEFT JOIN outer_oa_employee_data d ON b.out_emp_id = d.out_user_id",
            "AND d.channel = #{channel}",
            "AND d.out_ea = #{outEa}",
            "AND d.app_id = #{appId}",
            "WHERE b.fs_ea = #{fsEa}",
            "AND b.app_id = #{appId}",
            "AND b.fs_emp_id = #{fsEmpId}",
            "AND b.bind_status = 'normal'"
    })
    Map<String, Object> queryEmployeeBindByFsEmpId(
            @Param("channel") String channel,
            @Param("outEa") String outEa,
            @Param("fsEa") String fsEa,
            @Param("appId") String appId,
            @Param("fsEmpId") String fsEmpId);

        /**
         * 根据outEmpId联表查询
         *
         * @param channel  渠道
         * @param outEa    外部企业账号
         * @param fsEa     纷享企业账号
         * @param appId    应用ID
         * @param outEmpId 外部员工ID
         * @return 员工绑定关系和详细信息
         */
        @Select({ "SELECT b.*, d.* FROM outer_oa_employee_bind b",
                        "LEFT JOIN outer_oa_employee_data d ON b.out_emp_id = d.out_user_id",
            "AND d.channel = #{channel}",
            "AND d.out_ea = #{outEa}",
            "AND d.app_id = #{appId}",
            "WHERE b.fs_ea = #{fsEa}",
            "AND b.app_id = #{appId}",
            "AND b.out_emp_id = #{outEmpId}",
            "AND b.bind_status = 'normal'"
    })
    Map<String, Object> queryEmployeeBindByOutEmpId(
            @Param("channel") String channel,
            @Param("outEa") String outEa,
            @Param("fsEa") String fsEa,
            @Param("appId") String appId,
            @Param("outEmpId") String outEmpId);

        /**
         * 批量查询绑定关系
         *
         * @param channel  渠道
         * @param outEa    外部企业账号
         * @param fsEa     纷享企业账号
         * @param appId    应用ID
         * @param fsEmpIds 纷享员工ID列表
         * @return 员工绑定关系和详细信息列表
         */
        @Select({ "<script>", "SELECT b.*, d.* FROM outer_oa_employee_bind b",
                        "LEFT JOIN outer_oa_employee_data d ON b.out_emp_id = d.out_user_id",
            "AND d.channel = #{channel}",
            "AND d.out_ea = #{outEa}",
            "AND d.app_id = #{appId}",
            "WHERE b.fs_ea = #{fsEa}",
            "AND b.app_id = #{appId}",
            "AND b.fs_emp_id IN",
            "<foreach collection='fsEmpIds' item='fsEmpId' open='(' separator=',' close=')'>",
            "#{fsEmpId}",
            "</foreach>",
            "AND b.bind_status = 'normal'",
            "</script>"
    })
    List<Map<String, Object>> batchQueryEmployeeBindByFsEmpIds(
            @Param("channel") String channel,
            @Param("outEa") String outEa,
            @Param("fsEa") String fsEa,
            @Param("appId") String appId,
                        @Param("fsEmpIds") List<String> fsEmpIds);

    /**
     * 批量查询绑定关系（根据外部员工ID列表）
     *
     * @param channel   渠道
     * @param outEa     外部企业账号
     * @param fsEa      纷享企业账号
     * @param appId     应用ID
     * @param outEmpIds 外部员工ID列表
     * @return 员工绑定关系和详细信息列表
     */
    @Select({"<script>",
            "SELECT b.*, d.* FROM outer_oa_employee_bind b",
            "RIGHT JOIN outer_oa_employee_data d",
            "ON b.out_emp_id = d.out_user_id AND d.channel = b.channel AND d.out_ea = b.out_ea AND d.app_id = b.app_id",
            "WHERE d.out_ea = #{outEa}",
            "AND d.app_id = #{appId}",
            "AND d.out_user_id in",
            "<foreach collection='outEmpIds' item='outEmpId' open='(' separator=',' close=')'>",
            "#{outEmpId}",
            "</foreach>",
            "AND d.channel = #{channel}",
            "AND (b.fs_ea = #{fsEa} or b.fs_ea is null)",
            "</script>"
    })
    List<Map<String, Object>> batchQueryEmployeeBindByOutEmpIds(
            @Param("channel") String channel,
            @Param("outEa") String outEa,
            @Param("fsEa") String fsEa,
            @Param("appId") String appId,
            @Param("outEmpIds") List<String> outEmpIds);


    @Select("SELECT fs_emp_id FROM outer_oa_employee_bind WHERE fs_ea = #{fsEa} AND app_id = #{appId} AND channel = #{channel} AND bind_status = 'normal'")
    List<Integer> queryEmpIdByEi(@Param("channel") String channel, @Param("fsEa") String fsEa, @Param("appId") String appId);


        // ============== 钉钉 ==============

        /**
         * 联表查询带过滤条件（名称或手机号）
         *
         * @param channel        渠道
         * @param bindStatusEnum
         * @param outEa          外部企业账号
         * @param fsEa           纷享企业账号
         * @param appId          应用ID
         * @param nameOrPhone    名称或手机号
         * @param deptIds        部门ID列表
         * @param pageSize       分页大小
         * @param offset         分页偏移量
         * @return 员工绑定关系和详细信息
         */
        @Select({ "<script>", "SELECT b.*, d.* FROM outer_oa_employee_bind b",
                        "LEFT JOIN outer_oa_employee_data d ON b.out_emp_id = d.out_user_id",
                        "AND d.channel = #{channel}", "AND d.out_ea = #{outEa}", "AND d.app_id = #{appId}",
                        "WHERE b.fs_ea = #{fsEa}", "AND b.app_id = #{appId}", "<if test='nameOrPhone != null'>",
                        "AND (d.out_user_info->>'name' LIKE CONCAT('%',#{nameOrPhone},'%')",
                        "OR d.out_user_info->>'phone' LIKE CONCAT('%',#{nameOrPhone},'%'))", "</if>",
                        "<if test='deptIds != null and deptIds.size() > 0'>", "AND d.out_dept_id IN",
                        "<foreach collection='deptIds' item='deptId' open='(' separator=',' close=')'>", "#{deptId}",
                        "</foreach>", "</if>", "<if test='bindStatusEnum != null'>",
                        "AND b.bind_status = #{bindStatusEnum}", "</if>", "ORDER BY b.id",
                        "<if test='pageSize != null'>", "LIMIT #{pageSize} OFFSET #{offset}", "</if>", "</script>" })
        List<Map<String, Object>> queryDingEmployeeBindWithFilters(@Param("channel") String channel,
                        @Param("bindStatusEnum") BindStatusEnum bindStatusEnum, @Param("outEa") String outEa,
                        @Param("fsEa") String fsEa, @Param("appId") String appId,
                        @Param("nameOrPhone") String nameOrPhone, @Param("deptIds") List<String> deptIds,
                        @Param("pageSize") Integer pageSize, @Param("offset") Integer offset);

        /**
         * 统计符合条件的员工数量
         *
         * @param channel        渠道
         * @param outEa          外部企业账号
         * @param fsEa           纷享企业账号
         * @param appId          应用ID
         * @param nameOrPhone    名称或手机号
         * @param deptIds        部门ID列表
         * @param bindStatusEnum
         * @return 员工数量
         */
        @Select({ "<script>", "SELECT COUNT(1) FROM outer_oa_employee_bind b",
                        "LEFT JOIN outer_oa_employee_data d ON b.out_emp_id = d.out_user_id",
                        "AND d.channel = #{channel}", "<if test='outEa != null'>", "AND d.out_ea = #{outEa}", "</if>",
                        "AND d.app_id = #{appId}", "WHERE b.fs_ea = #{fsEa}", "AND b.app_id = #{appId}",
                        "<if test='nameOrPhone != null'>",
                        "AND (d.out_user_info->>'name' LIKE CONCAT('%',#{nameOrPhone},'%')",
                        "OR d.out_user_info->>'phone' LIKE CONCAT('%',#{nameOrPhone},'%'))", "</if>",
                        "<if test='deptIds != null and deptIds.size() > 0'>", "AND d.out_dept_id IN",
                        "<foreach collection='deptIds' item='deptId' open='(' separator=',' close=')'>", "#{deptId}",
                        "</foreach>", "</if>", "<if test='bindStatusEnum != null'>",
                        "AND b.bind_status = #{bindStatusEnum}", "</if>", "</script>" })
        Integer countDingEmployeeBindWithFilters(@Param("channel") String channel, @Param("outEa") String outEa,
                        @Param("fsEa") String fsEa, @Param("appId") String appId,
                        @Param("nameOrPhone") String nameOrPhone, @Param("deptIds") List<String> deptIds,
                        @Param("bindStatusEnum") BindStatusEnum bindStatusEnum);

        @Select("SELECT * FROM outer_oa_employee_bind WHERE fs_ea = #{fsEa} AND app_id = #{appId} AND channel = #{channel} AND bind_status != 'normal' AND out_user_info->>'phone' = #{dingEmployeePhone}")
        List<Map<String, Object>> findUnbindByDingEmployeePhone(@Param("channel") String channel,
                        @Param("fsEa") String fsEa, @Param("appId") String appId,
                        @Param("dingEmployeePhone") String dingEmployeePhone);

        /**
         * 批量更新员工状态
         */
        @Update({ "<script>", "UPDATE outer_oa_employee_bind", "SET bind_status = #{bindStatus}",
                        "WHERE fs_ea = #{fsEa}", "AND app_id = #{appId}", "AND channel = #{channel}",
                        "AND fs_emp_id IN",
                        "<foreach collection='empIds' item='empId' open='(' separator=',' close=')'>", "#{empId}",
                        "</foreach>", "</script>" })
        Integer batchUpdateEmpStatus(@Param("channel") String channel, @Param("fsEa") String fsEa,
                        @Param("appId") String appId, @Param("bindStatus") String bindStatus,
                        @Param("empIds") List<String> empIds);

        /**
         * 根据外部员工ID更新绑定状态
         *
         * @param channel  渠道
         * @param fsEa     纷享企业账号
         * @param appId    应用ID
         * @param outEmpId 外部员工ID
         * @param status   绑定状态
         * @return 受影响的记录数
         */
        @Update({ "<script>", "UPDATE outer_oa_employee_bind", "SET bind_status = #{status}",
                        "WHERE channel = #{channel}", "AND fs_ea = #{fsEa}", "AND app_id = #{appId}",
                        "AND out_emp_id = #{outEmpId}", "</script>" })
        Integer updateStatusByOutEmpId(@Param("channel") ChannelEnum channel, @Param("fsEa") String fsEa,
                        @Param("appId") String appId, @Param("outEmpId") String outEmpId,
                        @Param("status") BindStatusEnum status);

    @Update({
            "<script>",
            "UPDATE outer_oa_employee_bind",
            "SET bind_status = #{status}",
            "WHERE channel = #{channel}",
            "AND out_ea = #{outEa}",
            "AND app_id = #{appId}",
            "AND out_emp_id = #{outEmpId}",
            "</script>"
    })
    Integer updateStatusByOutEaAndOutEmpId(
            @Param("channel") ChannelEnum channel,
            @Param("outEa") String outEa,
            @Param("appId") String appId,
            @Param("outEmpId") String outEmpId,
            @Param("status") BindStatusEnum status
    );

    /**
     * 根据渠道、纷享企业账号和应用ID查询员工绑定记录数量
     *
     * @param channel 渠道
     * @param fsEa    纷享企业账号
     * @param appId   应用ID
     * @return 员工绑定记录数量
     */
    @Select("SELECT COUNT(*) FROM outer_oa_employee_bind WHERE channel = #{channel} AND fs_ea = #{fsEa} AND app_id = #{appId}")
    Integer count(@Param("channel") String channel, @Param("fsEa") String fsEa, @Param("appId") String appId);

    /**
     * 根据渠道、纷享企业账号和应用ID查询员工绑定记录数量
     *
     * @return 员工绑定记录数量
     */
    @Select("SELECT fs_emp_id FROM outer_oa_employee_bind WHERE dc_id=#{dcId}")
    List<String> queryCrmUsrIds(@Param("dcId") String dcId);

    /**
     * 根据渠道、纷享企业账号和应用ID查询员工绑定记录数量
     *
     * @return 员工绑定记录数量
     */
    @Select("SELECT count(*) FROM outer_oa_employee_bind WHERE dc_id=#{dcId}")
    Integer countDataByDcId(@Param("dcId") String dcId);

        /**
         * 根据dcId联表查询员工绑定关系和详细信息
         *
         * @param dcId     数据中心ID
         * @param pageSize 分页大小
         * @param offset   分页偏移量
         * @return 员工绑定关系和详细信息
         */
        @Select({ "<script>",
                        "SELECT b.*, d.id as d_id, d.channel as d_channel, d.out_ea as d_out_ea, d.app_id as d_app_id, d.out_user_id, d.out_dept_id, d.text1, d.text2, d.text3, d.text4, d.create_time as d_create_time, d.update_time as d_update_time FROM outer_oa_employee_bind b",
                        "LEFT JOIN outer_oa_employee_data d ON b.out_emp_id = d.out_user_id",
                        "AND d.channel = b.channel", "AND d.out_ea = b.out_ea", "AND d.app_id = b.app_id",
                        "WHERE b.dc_id = #{dcId}", "<if test='pageSize != null'>", "LIMIT #{pageSize} OFFSET #{offset}",
                        "</if>", "</script>" })
        List<Map<String, Object>> queryEmployeeBindByDcId(@Param("dcId") String dcId,
                        @Param("pageSize") Integer pageSize, @Param("offset") Integer offset);

    /**
     * 查询未绑定的员工信息
     *
     * @param channel  渠道
     * @param outEa    外部企业账号
     * @param fsEa     纷享企业账号
     * @param appId    应用ID
     * @param pageSize 分页大小
     * @param offset   分页偏移量
     * @return 未绑定的员工信息列表
     */
    @Select({ "<script>", "SELECT d.* FROM outer_oa_employee_data d", "WHERE d.channel = #{channel}",
            "AND d.out_ea = #{outEa}", "AND d.app_id = #{appId}", "AND NOT EXISTS (",
            "    SELECT 1 FROM outer_oa_employee_bind b", "    WHERE b.channel = d.channel",
            "    AND b.out_ea = d.out_ea", "    AND b.app_id = d.app_id",
            "    AND b.out_emp_id = d.out_user_id", "    AND b.fs_ea = #{fsEa}", ")",
            "<if test='pageSize != null'>", "LIMIT #{pageSize} OFFSET #{offset}", "</if>", "</script>" })
    List<Map<String, Object>> queryUnboundEmployees(@Param("channel") ChannelEnum channel,
                                                    @Param("outEa") String outEa, @Param("fsEa") String fsEa, @Param("appId") String appId,
                                                    @Param("pageSize") Integer pageSize, @Param("offset") Integer offset);

    /**
     * 统计未绑定的员工数量
     *
     * @param channel 渠道
     * @param outEa   外部企业账号
     * @param fsEa    纷享企业账号
     * @param appId   应用ID
     * @return 未绑定的员工数量
     */
    @Select({ "<script>", "SELECT COUNT(*) FROM outer_oa_employee_data d", "WHERE d.channel = #{channel}",
            "AND d.out_ea = #{outEa}", "AND d.app_id = #{appId}", "AND NOT EXISTS (",
            "    SELECT 1 FROM outer_oa_employee_bind b", "    WHERE b.channel = d.channel",
            "    AND b.out_ea = d.out_ea", "    AND b.app_id = d.app_id",
            "    AND b.out_emp_id = d.out_user_id", "    AND b.fs_ea = #{fsEa}",
            "    AND b.bind_status = 'normal'", ")", "</script>" })
    Integer countUnboundEmployees(@Param("channel") String channel, @Param("outEa") String outEa,
                                  @Param("fsEa") String fsEa, @Param("appId") String appId);

}
