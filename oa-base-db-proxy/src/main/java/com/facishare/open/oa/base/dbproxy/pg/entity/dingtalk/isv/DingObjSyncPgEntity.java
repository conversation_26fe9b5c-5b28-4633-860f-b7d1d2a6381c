package com.facishare.open.oa.base.dbproxy.pg.entity.dingtalk.isv;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 钉钉对象同步信息实体类 - PostgreSQL
 */
@Data
@TableName("dingtalk_isv_ding_obj_sync")
public class DingObjSyncPgEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 租户ID
     */
    private Integer tenantId;

    /**
     * 钉钉企业ID
     */
    private String dingCorpId;

    /**
     * 钉钉API名称
     */
    private String dingApiName;

    /**
     * CRM对象ID
     */
    private String crmObjId;

    /**
     * 钉钉对象ID
     */
    private String dingObjId;

    /**
     * 是否同步
     */
    private Integer isSync;

    /**
     * CRM API名称
     */
    private String crmApiName;
}