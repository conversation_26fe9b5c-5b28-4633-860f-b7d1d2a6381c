package com.facishare.open.oa.base.dbproxy.pg.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.facishare.open.oa.base.dbproxy.pg.entity.dto.EmployeeBaseInfo;
import com.facishare.open.oa.base.dbproxy.pg.entity.dto.EmployeeExtendInfo;
import com.facishare.open.oa.base.dbproxy.pg.handler.JsonbTypeHandler;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;

/**
 * EmployeeInfoEntity 员工信息实体类
 */
@Data
@TableName(value = "employee_info", autoResultMap = true)
public class EmployeeInfoEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private String id;

    /**
     * 企业账号
     */
    private String fsEa;

    /**
     * 人员ID
     */
    private String empId;

    /**
     * 基础信息(JSONB)
     */
    @TableField(typeHandler = JsonbTypeHandler.class, jdbcType = JdbcType.OTHER)
    private EmployeeBaseInfo baseInfo;

    /**
     * 扩展信息(JSONB)
     */
    @TableField(typeHandler = JsonbTypeHandler.class, jdbcType = JdbcType.OTHER)
    private EmployeeExtendInfo extendInfo;

    /**
     * 创建时间（long类型时间戳）
     */
    private Long createTime;

    /**
     * 修改时间（long类型时间戳）
     */
    private Long updateTime;
}