package com.facishare.open.oa.base.dbproxy.pg.params;

import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaOrderInfoTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * OuterOaOrderInfoParams
 * 订单信息参数类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OuterOaOrderInfoParams implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private String id;

    /**
     * 渠道
     */
    private ChannelEnum channel;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 购买类型（buy：普通购买，upgrade：升级购买，renew：续费购买）
     */
    private OuterOaOrderInfoTypeEnum orderType;

    /**
     * 外部应用appId
     */
    private String appId;

    /**
     * 付款企业ea
     */
    private String paidOutEa;

    /**
     * 订单开始时间（long类型时间戳）
     */
    private Long beginTime;

    /**
     * 订单结束时间（long类型时间戳）
     */
    private Long endTime;

    /**
     * 订单信息
     */
    private String orderInfo;

    /**
     * 创建时间（long类型时间戳）
     */
    private Long createTime;

    /**
     * 修改时间（long类型时间戳）
     */
    private Long updateTime;
} 