package com.facishare.open.oa.base.dbproxy.pg.params;

import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.BindTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * OuterOaEnterpriseBindParams
 * 企业绑定信息参数类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OuterOaEnterpriseBindParams implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id; // 数据中心id

    private ChannelEnum channel; // 渠道

    private String fsEa; // 纷享企业 ea

    private String outEa; // 外部企业 ea

    private String appId; // 外部应用 appId

    private String connectInfo; // 连接参数

    /**
     * 绑定类型
     */
    private BindTypeEnum bindType;
    /**
     * 绑定状态
     */
    private BindStatusEnum bindStatus;

    private Long createTime; // 创建时间

    private Long updateTime; // 修改时间
} 