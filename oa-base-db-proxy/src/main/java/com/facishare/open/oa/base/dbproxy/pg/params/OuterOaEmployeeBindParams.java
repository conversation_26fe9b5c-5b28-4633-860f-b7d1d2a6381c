package com.facishare.open.oa.base.dbproxy.pg.params;

import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * OuterOaEmployeeBindParams 员工绑定信息参数类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OuterOaEmployeeBindParams implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private String id;

    /**
     * 渠道
     */
    private ChannelEnum channel;

    /**
     * 数据中心id
     */
    private String dcId;
    /**
     * 纷享企业ea
     */
    private String fsEa;

    /**
     * 外部企业ea
     */
    private String outEa;

    /**
     * 外部应用appId
     */
    private String appId;

    /**
     * 纷享员工id
     */
    private String fsEmpId;

    /**
     * 外部员工id
     */
    private String outEmpId;

    /**
     * 绑定状态
     */
    private BindStatusEnum bindStatus;

    /**
     * 创建时间（long类型时间戳）
     */
    private Long createTime;

    /**
     * 修改时间（long类型时间戳）
     */
    private Long updateTime;

    /**
     * 当前页码，从1开始
     */
    private Integer page=1;

    /**
     * 每页大小
     */
    private Integer pageSize=100;

    // 自定义 Builder 类
    public static class OuterOaEmployeeBindParamsBuilder {
        private Integer page = 1; // 默认值
        private Integer pageSize = 100; // 默认值
    }
}