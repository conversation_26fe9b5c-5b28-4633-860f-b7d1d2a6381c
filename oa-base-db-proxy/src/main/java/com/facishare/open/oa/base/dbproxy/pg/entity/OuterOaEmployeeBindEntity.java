package com.facishare.open.oa.base.dbproxy.pg.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * OuterOaEnterpriseBindEntity
 * 员工绑定信息实体类
 */
@Data
@TableName("outer_oa_employee_bind")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OuterOaEmployeeBindEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    @TableField(fill = FieldFill.INSERT)
    private String id;

    /**
     * 渠道
     */
    private ChannelEnum channel;
    /**
     * 数据中心id
     */
    private String dcId;
    /**
     * 纷享企业ea
     */
    private String fsEa;

    /**
     * 外部企业ea
     */
    private String outEa;

    /**
     * 外部应用appId
     */
    private String appId;

    /**
     * 纷享员工id
     */
    private String fsEmpId;

    /**
     * 外部员工id
     */
    private String outEmpId;

    /**
     * 绑定状态
     */
    private BindStatusEnum bindStatus;

    /**
     * 创建时间（long类型时间戳）
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createTime;

    /**
     * 修改时间（long类型时间戳）
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateTime;
}
