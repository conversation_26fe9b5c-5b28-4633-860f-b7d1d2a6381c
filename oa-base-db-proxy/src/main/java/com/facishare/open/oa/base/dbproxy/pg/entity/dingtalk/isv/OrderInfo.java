package com.facishare.open.oa.base.dbproxy.pg.entity.dingtalk.isv;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/2/26 20:50:15
 */
@Data
public class OrderInfo {
    private String openId;
    private String unionId;
    private Integer subQuantity;
    private Integer suiteId;
    private String suiteKey;
    private String goodName;
    private String orderChargeType;
    private Integer minOfPeople;
    private Integer maxOfPeople;
    private String goodsCode;
    private String itemName;
    private String itemCode;
    private Long payFee;
    private Long nominalPayFee;
    private Long discountFee;
    private Double discount;
    private String distributorCorpId;
    private String distributorCorpName;
}
