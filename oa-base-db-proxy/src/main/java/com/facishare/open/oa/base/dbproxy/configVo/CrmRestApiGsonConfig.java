package com.facishare.open.oa.base.dbproxy.configVo;

import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.ToNumberPolicy;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> (^_−)☆
 * @date 2022/4/19
 */
@Configuration
public class CrmRestApiGsonConfig {

    @Bean("crmRestApiGson")
    public Gson crmRestApiGson() {
        //ObjectData会序列化null
        Gson customGson = new GsonBuilder()
                .registerTypeAdapterFactory(new ValueNullableAdapterFactory(ObjectData.class))
                .setObjectToNumberStrategy(ToNumberPolicy.LONG_OR_DOUBLE)
                .create();
        return customGson;
    }
}
