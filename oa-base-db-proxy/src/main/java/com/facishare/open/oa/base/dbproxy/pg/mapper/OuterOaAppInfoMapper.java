package com.facishare.open.oa.base.dbproxy.pg.mapper;

import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaAppInfoEntity;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaAppInfoTypeEnum;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * Mapper 接口 - 应用信息
 */
@Mapper
public interface OuterOaAppInfoMapper extends BaseMapper2<OuterOaAppInfoEntity> {

        /**
         * 批量插入或更新应用信息
         *
         * @param entities 应用信息实体列表
         * @return 成功插入或更新的记录数
         */
        @Update("<script>" + "INSERT INTO outer_oa_app_info "
                        + "(id, channel, out_ea, app_id, app_type, app_info, status, create_time, update_time) "
                        + "VALUES " + "<foreach collection='list' item='item' separator=','> " + "    ( "
                        + "    #{item.id}, #{item.channel}, #{item.outEa}, #{item.appId}, "
                        + "    #{item.appType}, #{item.appInfo}, #{item.status}, #{item.createTime}, #{item.updateTime} "
                        + "    ) " + "</foreach> " + "ON CONFLICT (channel, out_ea, app_id) " + "DO UPDATE SET "
                        + "    app_info = EXCLUDED.app_info, " + "    status = EXCLUDED.status, "
                        + "    update_time = EXCLUDED.update_time " + "</script>")
        Integer batchUpsertInfos(@Param("list") List<OuterOaAppInfoEntity> entities);

    @Select("select * from outer_oa_app_info where channel=#{channel} and app_type=#{type} and app_id=#{appId}")
    OuterOaAppInfoEntity getByAppIdAndChannel(@Param("channel") ChannelEnum channel, @Param("type") OuterOaAppInfoTypeEnum type, @Param("appId") String appId);

        // 根据appId和channel修改appInfo
        @Update("UPDATE outer_oa_app_info SET app_info = #{appInfo} WHERE channel = #{channel} AND app_id = #{appId}")
        void updateAppInfo(@Param("appInfo") String appInfo, @Param("channel") ChannelEnum channel,
                        @Param("appId") String appId);

        // ===========钉钉==========

        /**
         * 根据条件查询应用授权信息 使用 PostgreSQL 的 JSON 查询功能
         *
         * @param channel 渠道
         * @param outEa   企业ID
         * @param appId   应用ID
         * @param suiteId 套件ID
         * @return 应用授权信息列表
         */
        @Select("<script>" + "SELECT * FROM outer_oa_app_info WHERE channel = #{channel} "
                        + "<if test='outEa != null'> AND out_ea = #{outEa} </if>"
                        + "<if test='appId != null'> AND app_id = #{appId} </if>"
                        + "<if test='suiteId != null'> AND (app_info::jsonb ->> 'suiteId')::bigint = #{suiteId} </if>"
                        + "</script>")
        List<OuterOaAppInfoEntity> queryAppAuth(@Param("channel") ChannelEnum channel, @Param("outEa") String outEa,
                        @Param("appId") String appId, @Param("suiteId") Long suiteId);

        // 根据fsEa和appId获取outEa
        @Select("SELECT out_ea FROM outer_oa_app_info WHERE channel = #{channel} AND ei = #{ei} AND app_id = #{appId}")
        String getOutEaByFsEaAndAppId(@Param("channel") ChannelEnum channel, @Param("ei") Integer ei,
                        @Param("appId") String appId);

        /**
         * 查询企业绑定表中非正常状态的应用信息
         * 通过联表查询获取在outer_oa_app_info表中存在，且在outer_oa_enterprise_bind表中状态不为normal的应用信息
         *
         * @param channel 渠道
         * @param outEa   企业ID
         * @return 应用信息列表
         */
        @Select("SELECT app.* FROM outer_oa_app_info app " + "LEFT JOIN outer_oa_enterprise_bind bind "
                        + "ON app.out_ea = bind.out_ea " + "AND app.channel = bind.channel "
                        + "WHERE app.channel = #{channel} " + "AND app.out_ea = #{outEa} "
                        + "AND bind.status != 'normal'")
        List<OuterOaAppInfoEntity> queryAppInfoWithAbnormalBind(@Param("channel") ChannelEnum channel,
                        @Param("outEa") String outEa);
}
