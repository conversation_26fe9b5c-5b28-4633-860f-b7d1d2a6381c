package com.facishare.open.oa.base.dbproxy.mongo.store;

import com.facishare.open.oa.base.dbproxy.mongo.dao.OaConnectorOutDepartmentInfoMongoDao;
import com.facishare.open.oa.base.dbproxy.mongo.document.OaConnectorOutDepartmentInfoDoc;
import com.github.autoconf.ConfigFactory;
import com.github.mongo.support.DatastoreExt;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.IndexModel;
import com.mongodb.client.model.IndexOptions;
import com.mongodb.client.model.Indexes;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * oa连接器部门详情 mongo store
 * <AUTHOR>
 * @date 2023/12/06
 */
@Repository
@Slf4j
public class OaConnectorOutDepartmentInfoMongoStore {

    @Getter
    private final DatastoreExt store;

    private final static String CollectionPrefix = "oa_connector_out_department_info";
    private final String dbName;
    private final Set<String> collectionCache = Sets.newConcurrentHashSet();

    private static class SingleCodecHolder {
        //需要让自定义的codec放前面
        private static final CodecRegistry codecRegistry = CodecRegistries.fromRegistries(
                MongoClientSettings.getDefaultCodecRegistry(),
                CodecRegistries.fromProviders(PojoCodecProvider.builder()
                        .register(OaConnectorOutDepartmentInfoDoc.class)
                        .automatic(true).build()));
    }


    public OaConnectorOutDepartmentInfoMongoStore(@Qualifier("oaBaseMongoStore") DatastoreExt store) {
        this.store = store;
        this.dbName = ConfigFactory.getInstance().getConfig("fs-feishu-config")
                .get("mongo.dbName", "fs-open-qywx");
        store.getMongo().getDatabase(dbName)
                .listCollectionNames().iterator().forEachRemaining(v -> {
                    if (v.startsWith(CollectionPrefix)) {
                        collectionCache.add(v);
                    }
                });
    }

    private String getCollectionName() {
        return CollectionPrefix;
    }

    /**
     * 创建集合，检查索引
     * 这里不移除索引，另外使用批量接口移除
     * 也不根据名称更新索引，同样，更新索引需要走批量接口更新
     */
    public synchronized MongoCollection<OaConnectorOutDepartmentInfoDoc> getOrCreateCollection() {
        //dbName会从配置文件的mongo.servers解析
        String collectionName = getCollectionName();
        MongoCollection< OaConnectorOutDepartmentInfoDoc> collection = getDatabase()
                .withCodecRegistry(SingleCodecHolder.codecRegistry)
                .getCollection(collectionName, OaConnectorOutDepartmentInfoDoc.class);
        if (!collectionCache.add(collectionName)) {
            return collection;
        }

        List<IndexModel> indexList = Lists.newArrayList();
        //过期自动清理时间,365天
//        Bson expireTimeBson = Indexes.descending( OaConnectorOutDepartmentInfoMongoDao.oa_createTime);
//        indexList.add(new IndexModel(expireTimeBson, new IndexOptions()
//                .name("index_expire_time")
//                .expireAfter(365L, TimeUnit.DAYS)
//                .background(true)));

        //根据outEa字段创建索引,索引名称=index_outEa
        Bson channelOutEaBson = Indexes.compoundIndex(
                Indexes.ascending(OaConnectorOutDepartmentInfoMongoDao.oa_channel),
                Indexes.ascending(OaConnectorOutDepartmentInfoMongoDao.oa_outEa));
        indexList.add(new IndexModel(channelOutEaBson, new IndexOptions()
                .name("index_channel_outEa")
                .background(true)));

        Bson channelOutEaOutDepartmentIdBson = Indexes.compoundIndex(
                Indexes.ascending(OaConnectorOutDepartmentInfoMongoDao.oa_channel),
                Indexes.ascending(OaConnectorOutDepartmentInfoMongoDao.oa_outEa),
                Indexes.ascending(OaConnectorOutDepartmentInfoMongoDao.oa_outDepartmentId));
        indexList.add(new IndexModel(channelOutEaOutDepartmentIdBson, new IndexOptions()
                .name("index_channel_outEa_outDepartmentId")
                .background(true)));

        List<String> created = collection.createIndexes(indexList);
        log.info("OaConnectorOutDepartmentInfoMongoStore.getOrCreateCollection created indexes: {}, wanted: {}, created: {}", created, indexList, created);

        return collection;
    }

    public MongoDatabase getDatabase() {
        return store.getMongo().getDatabase(dbName);
    }
}
