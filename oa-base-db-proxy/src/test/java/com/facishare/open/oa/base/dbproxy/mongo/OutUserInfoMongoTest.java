package com.facishare.open.oa.base.dbproxy.mongo;

import com.facishare.open.oa.base.dbproxy.BaseTest;
import com.facishare.open.oa.base.dbproxy.mongo.dao.OaConnectorOutUserInfoMongoDao;
import com.facishare.open.oa.base.dbproxy.mongo.document.OaConnectorOutUserInfoDoc;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class OutUserInfoMongoTest extends BaseTest {
    @Autowired
    private OaConnectorOutUserInfoMongoDao oaConnectorOutUserInfoMongoDao;

    @Test
    public void queryUserInfos() {
        List<OaConnectorOutUserInfoDoc> docs = oaConnectorOutUserInfoMongoDao.queryUserInfos(ChannelEnum.feishu, "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA", null);
        System.out.println(docs);
    }
}
